// utils/envUtils.ts


/** @internal */
export interface BrowserEnv {
  isSafari: boolean;
  isChrome: boolean;
  isIOSWebView: boolean;
  isAndroidWebView: boolean;
  isWebView: boolean;
  hasHomeIndicator: boolean;
  userAgent: string;
}

/**
 * Singleton class for environment detection and safe area utilities.
 * @internal
 */
export class EnvUtils {
  private static _instance: EnvUtils;
  private _env: BrowserEnv | null = null;

  private constructor() {}

  public static get instance(): EnvUtils {
    if (!EnvUtils._instance) {
      EnvUtils._instance = new EnvUtils();
    }
    return EnvUtils._instance;
  }

  
  public async detectBrowserEnvironment(): Promise<BrowserEnv> {
    if (this._env) return this._env;
    const ua = navigator.userAgent || '';
    const isIOS = /iPad|iPhone|iPod/.test(ua);
    const isAndroid = /Android/.test(ua);
    const isSafari = /^((?!chrome|android).)*safari/i.test(ua);
    const isChrome = /Chrome/.test(ua) && !/Edg|OPR|Brave/.test(ua);
    const isIOSWebView = isIOS && !/Safari/.test(ua);
    const isAndroidWebView = isAndroid && /wv/.test(ua);
    const homeIndicator = isIOS && await this.hasHomeIndicator();
    this._env = {
      isSafari,
      isChrome,
      isIOSWebView,
      isAndroidWebView,
      isWebView: isIOSWebView || isAndroidWebView,
      hasHomeIndicator: homeIndicator,
      userAgent: ua
    };
    return this._env;
  }

  public async hasHomeIndicator(): Promise<boolean> {
    const test = document.createElement('div');
    test.style.cssText = `
      position: absolute;
      bottom: 0;
      height: constant(safe-area-inset-bottom);
      height: env(safe-area-inset-bottom);
      visibility: hidden;
    `;
    document.body.appendChild(test);
    const computed = window.getComputedStyle(test).height;
    document.body.removeChild(test);
    return parseInt(computed) > 0;
  }

  public getStatusBarHeight(): number {
    if (typeof window !== 'undefined' && window.navigator && /iPhone|iPad|iPod/i.test(navigator.userAgent)) {
      const envInset = parseInt(getComputedStyle(document.documentElement).getPropertyValue('env(safe-area-inset-top)') || '0', 10);
      if (envInset > 0) return envInset;
      if (window.screen.height >= 812) return 44;
      return 20;
    }
    return 0;
  }
}
