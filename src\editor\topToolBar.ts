import saveIconPath from "../../public/assets/save.svg?inline";
import cancelIconPath from "../../public/assets/cancel.svg?inline";
import backIconPath from "../../assets/back.svg?inline";
import { editorMode, setHomeMode } from "./editorModeStore";
import { Editor } from "./editor";
import { CropButtonHandler } from "../utils/cropButtonHandler";

/**
 * @internal
 */
export class TopToolbar {
  public static instance: TopToolbar | null = null;
  public readonly element: HTMLDivElement;

  private backButton: HTMLButtonElement;
  private saveButton: HTMLButtonElement;
  private cancelButton: HTMLButtonElement;
  private doneButton: HTMLButtonElement;
  private toolContainer: HTMLDivElement;

  private constructor() {
    this.element = document.createElement("div");
    this.element.style.backgroundColor = "white";
    this.element.style.color = "white";
    this.element.style.display = "flex";
    this.element.style.justifyContent = "space-between";
    this.element.style.alignItems = "center";

    const backContainer = document.createElement("div");
    backContainer.style.display = "flex";
    backContainer.style.alignItems = "center";

    this.backButton = document.createElement("button");
    this.backButton.id = "backButton";
    this.backButton.title = "Back";
    this.backButton.style.background = "none";
    this.backButton.style.border = "none";
    this.backButton.style.cursor = "pointer";

    const backIcon = document.createElement("img");
    backIcon.src = backIconPath;
    backIcon.alt = "Back";
    backIcon.style.width = "20px";
    backIcon.style.height = "20px";
    this.backButton.appendChild(backIcon);
    backContainer.appendChild(this.backButton);

    this.toolContainer = document.createElement("div");
    this.toolContainer.style.flex = "1";
    this.toolContainer.style.display = "flex";
    this.toolContainer.style.gap = "10px";

    const actionContainer = document.createElement("div");
    actionContainer.style.display = "flex";
    actionContainer.style.gap = "10px";
    actionContainer.style.alignItems = "center";

    this.saveButton = document.createElement("button");
    this.saveButton.id = "saveButton";
    this.saveButton.title = "Save";
    this.saveButton.style.background = "none";
    this.saveButton.style.border = "none";
    this.saveButton.style.cursor = "pointer";
    this.saveButton.style.display = "none";

    const saveIcon = document.createElement("img");
    saveIcon.src = saveIconPath;
    saveIcon.alt = "Save";
    saveIcon.style.width = "24px";
    saveIcon.style.height = "24px";
    this.saveButton.appendChild(saveIcon);

    this.cancelButton = document.createElement("button");
    this.cancelButton.id = "cancelButton";
    this.cancelButton.title = "Cancel";
    this.cancelButton.style.background = "none";
    this.cancelButton.style.border = "none";
    this.cancelButton.style.cursor = "pointer";
    this.cancelButton.style.display = "none";
    // this.cancelButton.textContent = 'Cancel';

    const cancelIcon = document.createElement("img");
    cancelIcon.src = cancelIconPath;
    cancelIcon.alt = "Cancel";
    cancelIcon.style.width = "24px";
    cancelIcon.style.height = "24px";
    this.cancelButton.appendChild(cancelIcon);

    this.doneButton = document.createElement("button");
    this.doneButton.id = "doneButton";
    this.doneButton.title = "Done";
    this.doneButton.style.background = "none";
    this.doneButton.style.border = "none";
    this.doneButton.style.cursor = "pointer";
    this.doneButton.style.color = "black";
    this.doneButton.style.display = "none";
    this.doneButton.textContent = "Done";

    actionContainer.appendChild(this.saveButton);
    actionContainer.appendChild(this.cancelButton);
    actionContainer.appendChild(this.doneButton);

    this.element.appendChild(backContainer);
    this.element.appendChild(this.toolContainer);
    this.element.appendChild(actionContainer);

    this.bindEvents();
    this.subscribeToStore();
  }

  public static getInstance(): TopToolbar {
    if (!TopToolbar.instance) {
      TopToolbar.instance = new TopToolbar();
    }
    return TopToolbar.instance;
  }

  private bindEvents() {
    this.backButton.onclick = () => {
      this.element.dispatchEvent(
        new CustomEvent("editor:back", { bubbles: true })
      );
    };

    this.saveButton.onclick = () => {
      this.element.dispatchEvent(
        new CustomEvent("editor:save", { bubbles: true })
      );
    };

    this.cancelButton.onclick = () => {
      this.element.dispatchEvent(
        new CustomEvent("editor:cancel", { bubbles: true })
      );
    };

    this.doneButton.onclick = () => {
      const editor = Editor.getInstance();
      if (!editor) return;

      const mode = editorMode.get();
      if (mode === "annotate") {
        const editorContainer = editor.getEditorContainer();
        const imageContainer = editor.getImageContainer();
        const imageElement = editor.getImageElement();
        const fabricCanvas =
          (window as any).fabricCanvas ||
          CropButtonHandler.getInstance().getFabricCanvas();
        if (fabricCanvas && imageContainer) {
          const dataUrl = fabricCanvas.toDataURL({ format: "png" });
          if (imageElement) {
            imageElement.src = dataUrl;
            imageElement.style.display = "block";
            imageElement.alt = "Annotated Image";
            imageContainer.innerHTML = "";
            imageContainer.appendChild(imageElement);
            editorContainer.innerHTML = "";
            editorContainer.appendChild(imageContainer);
          } else {
            editorContainer.innerHTML = "";
            const img = document.createElement("img");
            img.src = dataUrl;
            img.alt = "Annotated Image";
            img.style.maxWidth = "100%";
            img.style.maxHeight = "100%";
            img.style.objectFit = "contain";
            imageContainer.appendChild(img);
            editorContainer.appendChild(imageContainer);
          }
          setHomeMode();
        }
        return;
      } else if (mode === "crop") {
        const cropper = editor.getCropper();
        const cropperSelection = cropper?.getCropperSelection();
        if (!cropperSelection) return;

        const editorContainer = editor.getEditorContainer();
        const imageContainer = editor.getImageContainer();
        const imageElement = editor.getImageElement();
        const editorContainerRect = editorContainer.getBoundingClientRect();
        const canvasWidth = editorContainerRect.width;
        const canvasHeight = editorContainerRect.height;

        cropperSelection
          .$toCanvas({ width: canvasWidth, height: canvasHeight })
          .then((canvasElement: HTMLCanvasElement) => {
            // Convert canvas to image
            const croppedImage = canvasElement.toDataURL("image/png");
            // Update the existing image element if available
            if (imageElement && imageContainer) {
              imageElement.src = croppedImage;
              imageElement.style.display = "block";
              imageElement.alt = "Cropped Image";
              imageContainer.innerHTML = "";
              imageContainer.appendChild(imageElement);
              editorContainer.innerHTML = "";
              editorContainer.appendChild(imageContainer);
            } else {
              // Fallback: create new image if references are missing
              editorContainer.innerHTML = "";
              const img = document.createElement("img");
              img.src = croppedImage;
              img.alt = "Cropped Image";
              img.style.maxWidth = "100%";
              img.style.maxHeight = "100%";
              img.style.objectFit = "contain";
              editorContainer.appendChild(img);
            }
            setHomeMode();
            editor.setCropper(null);
          });
      }
    };
  }

  private subscribeToStore() {
    editorMode.subscribe(mode => {
      if (mode === "crop" || mode === "annotate") {
        this.backButton.style.display = "";
        this.saveButton.style.display = "none";
        this.cancelButton.style.display = "none";
        this.doneButton.style.display = "";
      } else if (mode === "home") {
        this.backButton.style.display = "none";
        this.saveButton.style.display = "block";
        this.cancelButton.style.display = "block";
        this.doneButton.style.display = "none";
      } else {
        this.backButton.style.display = "none";
        this.saveButton.style.display = "none";
        this.cancelButton.style.display = "none";
        this.doneButton.style.display = "none";
      }
    });
  }

  public addTool(toolElement: HTMLElement) {
    this.toolContainer.appendChild(toolElement);
  }
}
