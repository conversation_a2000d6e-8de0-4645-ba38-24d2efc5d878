import ropePatternImage from "../../public/assets/images/rope_pattern.png?inline";
// import cameraCaptureIcon from "../../public/assets/camera-capture.svg";
import switchCameraIcon from "../../public/assets/switch-camera.svg";
import flashIcon from "../../public/assets/camera-flash.svg";
import guideIcon from "../../public/assets/tourGuide.svg";
import closeIcon from "../../public/assets/close-cam.svg";
import type { cameraResultOptions } from "../editor/editor";
import { Preview } from "./preview";
import { DeviceInfo } from "../utils/DeviceInfo";
import { SafeAreaUtils } from "../utils/SafeAreaUtils";
import { AutoFlash } from "./autoFlash";

// -----------------------------------Insight AI START-----------------------------------

/**
 * @public
 */
export interface CameraBaseOptions {
  preview: boolean;
  location: boolean;
  needCropping: boolean;
}

/**
 * @public
 */
export interface insightAICamOptions extends CameraBaseOptions {
  needCropping: false;
  cropperInitOptions?: undefined;
}

/**
 * @public
 * If needCropping is true, cropperInitOptions is required.
 */
export interface insightAICamCropOptions extends CameraBaseOptions {
  needCropping: true;
  cropperInitOptions: observationOptions | insightAIOptions;
}

/**
 * @public
 */
export interface insightAIOptions {
  mode: "InsightAI";
  report?: false;
  annotation?: false;
  ratingImagesScale?: false;
  observationOptions?: undefined;
}

/**
 * @public
 */
export type insightAICameraOptions =
  | insightAICamOptions
  | insightAICamCropOptions;

// -----------------------------------Insight AI End-----------------------------------
/**
 * @public
 */
export interface CameraCropperInitOptions {
  cropOptions: observationOptions | insightAIOptions;
}

/**
 * @public
 */
export interface cameraOnlyOptions extends CameraBaseOptions {
  needCropping: false;
  cropperInitOptions?: undefined;
}

/**
 * @public
 */
export interface cameraCropperOptions extends CameraBaseOptions {
  needCropping: true;
  cropperInitOptions: CameraCropperInitOptions;
}

/**
 * @public
 */
export interface CameraCropperInitOptions {
  cropOptions: observationOptions | insightAIOptions;
}

/**
 * @public
 */
export interface observationOptions {
  mode: "Observations";
  report?: boolean;
  maxRating?: number | string;
  currentRating?: number | string;
  productName?: string;
  isDemo?: string | boolean;
  annotation?: boolean;
  observationOptions: externalAbrOptions | internalAbrOptions | otherAbrOptions;
}

/**
 * @public
 */
export interface externalAbrOptions {
  abrasionMode?: "External";
  ratingImagesScale: true;
}

/**
 * @public
 */
export interface internalAbrOptions {
  abrasionMode: "Internal";
  ratingImagesScale: true;
}

/**
 * @public
 */
export interface otherAbrOptions {
  abrasionMode: "Others";
  ratingImagesScale: false;
}

/**
 * @public
 */
export type cameraOptions = cameraOnlyOptions | cameraCropperOptions;

/**
 * @public
 */
export class Camera {
  // Helper to get iOS status bar height for Cordova/Ionic
  // private getStatusBarHeight(): number {
  //   // iOS safe area inset top (works in most modern iOS Cordova apps)
  //   if (typeof window !== 'undefined' && window.navigator && /iPhone|iPad|iPod/i.test(navigator.userAgent)) {
  //     // Try to use CSS env variable if available
  //     const envInset = parseInt(getComputedStyle(document.documentElement).getPropertyValue('env(safe-area-inset-top)') || '0', 10);
  //     if (envInset > 0) return envInset;
  //     // Fallback for older iOS: 20px (pre-X), 44px (iPhone X+)
  //     if (window.screen.height >= 812) return 44;
  //     return 20;
  //   }
  //   return 0;
  // }

  //** @internal */
  private currentCamera: "user" | "environment" = "environment";

  //** @internal */
  private localMediaStream: MediaStream | null = null;

  //** @internal */
  public overlayTheme = "light";

  //** @internal */
  private overlayImg: HTMLImageElement;

  //** @internal */
  public overlayLoaded: boolean = false;
  //** @internal */
  private ropePatternOverlayImage = ropePatternImage;
  //** @internal */
  private modal: HTMLDivElement | null = null;

  //** @internal */
  constructor() {
    this.overlayImg = new Image();
    this.overlayImg.src = this.ropePatternOverlayImage;
    this.overlayImg.onload = () => {
      this.overlayLoaded = true;
    };
    this.overlayImg.onerror = () => {
      this.overlayLoaded = false;
    };
  }

  /**
   * Open the camera, handle capture, and return the result.
   * Handles geolocation if needed.
   * @param options cameraOptions or cameraCropperOptions
   */
  public async openCamera(
    options: cameraOptions | cameraCropperOptions | insightAICameraOptions
  ): Promise<cameraResultOptions> {
    // 1. Handle geolocation if needed
    let latitude = 0;
    let longitude = 0;
    if (options.location) {
      try {
        const coords = await this.getLocation();
        latitude = coords.latitude;
        longitude = coords.longitude;
      } catch {
        latitude = 0;
        longitude = 0;
      }
    }
    // 2. Show camera modal and handle capture
    const image = await this.showCameraModal();
    // 3. If preview is false
    if (!options.preview) {
      // If needCropping is true, send image directly to cropper
      if (
        (options as any).needCropping &&
        (options as any).cropperInitOptions
      ) {
        return new Promise((resolve, reject) => {
          import("../editor/editor").then(({ Editor }) => {
            const cropperOptions = {
              ...(options as any).cropperInitOptions,
              imageUrl: image,
              latitude,
              longitude,
            };
            const editor = Editor.init(cropperOptions);
            editor.on("success", (cropResult: any) => {
              resolve({ ...cropResult, latitude, longitude });
            });
            editor.on("error", (err: any) => {
              reject(err);
            });
            editor.on("cancelled", () => {
              reject(new Error("Cropper cancelled"));
            });
          });
        });
      } else {
        // No cropping, just return image
        return { image, latitude, longitude };
      }
    }
    // 4. If preview is true, show preview modal and handle accept/retake
    return new Promise((resolve, reject) => {
      const handleRetake = async () => {
        try {
          const result = await this.openCamera(options);
          resolve(result);
        } catch (err) {
          reject(err);
        }
      };

      // Accept handler: just resolve with result (no cropping)
      const handleAccept = (result: cameraResultOptions) => {
        resolve(result);
      };

      // Crop handler: open cropper UI and resolve with cropper result
      const handleCrop = (img: string) => {
        // Dynamically import Editor to avoid circular deps
        import("../editor/editor").then(({ Editor }) => {
          const cropperOptions = {
            ...(options as any).cropperInitOptions,
            imageUrl: img,
            latitude,
            longitude,
          };
          const editor = Editor.init(cropperOptions);
          editor.on("success", (cropResult: any) => {
            resolve({ ...cropResult, latitude, longitude });
          });
          editor.on("error", (err: any) => {
            reject(err);
          });
          editor.on("cancelled", () => {
            reject(new Error("Cropper cancelled"));
          });
        });
      };

      new Preview({
        image,
        latitude,
        longitude,
        needCropping: (options as any).needCropping ?? false,
        onRetake: handleRetake,
        onAccept: handleAccept,
        onCrop: handleCrop,
      });
    });
  }

  /** @internal */
  private getLocation(): Promise<{ latitude: number; longitude: number }> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) return reject();
      navigator.geolocation.getCurrentPosition(
        pos =>
          resolve({
            latitude: pos.coords.latitude,
            longitude: pos.coords.longitude,
          }),
        () => reject()
      );
    });
  }

  /**
   * @internal
   * Show the camera modal, handle capture, and return the image as a base64 string
   */
  private showCameraModal(): Promise<string> {
    return new Promise(async (resolve, reject) => {
      // Device type detection: mobile/tablet vs desktop
      const isMobileOrTablet = () => {
        return (
          /Mobi|Android|iPhone|iPad|iPod|Opera Mini|IEMobile|WPDesktop/i.test(
            navigator.userAgent
          ) || window.innerWidth <= 800
        );
      };
      const mobile = isMobileOrTablet();

      // --- Desktop UI: Centered modal with border radius, shadow, and fixed size ---
      const modal = document.createElement("div");
      function setModalSize() {
        if (!mobile) {
          // Desktop: Centered modal, fixed size, border radius, shadow
          const modalW = Math.min(900, Math.round(window.innerWidth * 0.8));
          const modalH = Math.min(700, Math.round(window.innerHeight * 0.85));
          modal.style.position = "fixed";
          modal.style.left = `calc(50% - ${modalW / 2}px)`;
          modal.style.top = `calc(50% - ${modalH / 2}px)`;
          modal.style.width = modalW + "px";
          modal.style.height = modalH + "px";
          modal.style.background = "#000";
          modal.style.borderRadius = "16px";
          modal.style.boxShadow = "0 8px 32px #0008, 0 1.5px 8px #0004";
          modal.style.display = "flex";
          modal.style.flexDirection = "column";
          modal.style.justifyContent = "center";
          modal.style.alignItems = "center";
          modal.style.zIndex = "99999";
          modal.style.padding = "0";
          modal.style.overflow = "hidden";
        } else {
          // Mobile: Fullscreen
          modal.style.position = "fixed";
          modal.style.top = "0";
          modal.style.left = "0";
          modal.style.width = window.innerWidth + "px";
          modal.style.height = window.innerHeight + "px";
          modal.style.background = "#000";
          modal.style.borderRadius = "0";
          modal.style.boxShadow = "none";
          modal.style.display = "flex";
          modal.style.flexDirection = "column";
          modal.style.justifyContent = "center";
          modal.style.alignItems = "center";
          modal.style.zIndex = "99999";
          modal.style.padding = "0";
          modal.style.overflow = "hidden";
        }
      }
      setModalSize();
      window.addEventListener("resize", setModalSize);
      window.addEventListener("orientationchange", setModalSize);
      this.modal = modal;

      // --- Top Toolbar ---
      const topToolbar = document.createElement("div");
      const topBarHeight = mobile ? 32 : 28;
      const device = await DeviceInfo.getInfo();
      // Use SafeAreaUtils for iOS webview-specific adjustments
      const safeInsets = SafeAreaUtils.getSafeAreaInsets(device);
      const safeTop = safeInsets.top;
      function setTopToolbarSize() {
        if (!mobile) {
          topToolbar.style.width = "100%";
          topToolbar.style.left = "0";
        } else {
          topToolbar.style.width = window.innerWidth + "px";
          topToolbar.style.left = "0";
        }
        topToolbar.style.height = `${topBarHeight + safeTop}px`;
        topToolbar.style.paddingTop = `${safeTop}px`;
        console.log(device);
        // Align items to flex-end if iOS webview, else center
        if (device.isIOSWebView) {
          topToolbar.style.alignItems = "flex-end";
        } else {
          topToolbar.style.alignItems = "center";
        }
      }
      setTopToolbarSize();
      window.addEventListener("resize", setTopToolbarSize);
      window.addEventListener("orientationchange", setTopToolbarSize);
      topToolbar.style.display = "flex";
      topToolbar.style.flexDirection = "row";
      // topToolbar.style.alignItems = "center";
      topToolbar.style.justifyContent = "flex-end";
      topToolbar.style.background = "#000";
      topToolbar.style.position = "absolute";
      topToolbar.style.top = "0";
      topToolbar.style.left = "0";
      topToolbar.style.zIndex = "100001";
      topToolbar.style.padding = mobile ? "0 8px" : "0 16px";

      // Guide icon (right, before close)
      // Guide icon as button
      const guideBtn = document.createElement("button");
      guideBtn.title = "Guide";
      guideBtn.style.background = "none";
      guideBtn.style.border = "none";
      guideBtn.style.cursor = "pointer";
      guideBtn.style.width = mobile ? "32px" : "28px";
      guideBtn.style.height = mobile ? "32px" : "28px";
      guideBtn.style.display = "flex";
      guideBtn.style.alignItems = "center";
      guideBtn.style.justifyContent = "center";
      guideBtn.style.marginRight = "6px";
      guideBtn.style.zIndex = "10";
      const guideIconImg = document.createElement("img");
      guideIconImg.src = guideIcon;
      guideIconImg.alt = "Guide";
      guideIconImg.style.height = mobile ? "24px" : "24px";
      guideIconImg.style.width = mobile ? "24px" : "24px";
      guideBtn.appendChild(guideIconImg);

      // flash button (leftmost, only shown if torch is supported)
      const flashBtn = document.createElement("button");
      flashBtn.title = "Flash";
      flashBtn.style.background = "none";
      flashBtn.style.border = "none";
      flashBtn.style.cursor = "pointer";
      flashBtn.style.width = mobile ? "32px" : "28px";
      flashBtn.style.height = mobile ? "32px" : "28px";
      flashBtn.style.display = "flex";
      flashBtn.style.alignItems = "center";
      flashBtn.style.justifyContent = "center";
      flashBtn.style.zIndex = "10";
      flashBtn.style.marginRight = '5px';
      flashBtn.style.visibility = 'hidden'; // Hide by default, show if torch supported
      // Add flash icon image
      const flashIconImg = document.createElement("img");
      flashIconImg.src = flashIcon;
      flashIconImg.alt = "Flash";
      flashIconImg.style.width = mobile ? "24px" : "24px";
      flashIconImg.style.height = mobile ? "24px" : "24px";
      flashBtn.appendChild(flashIconImg);
      // Close button (rightmost)
      const closeBtn = document.createElement("button");
      closeBtn.title = "Close";
      closeBtn.style.background = "none";
      closeBtn.style.border = "none";
      closeBtn.style.cursor = "pointer";
      closeBtn.style.width = mobile ? "32px" : "28px";
      closeBtn.style.height = mobile ? "32px" : "28px";
      closeBtn.style.display = "flex";
      closeBtn.style.alignItems = "center";
      closeBtn.style.justifyContent = "center";
      closeBtn.style.zIndex = "10";
      closeBtn.style.marginRight = '5px'
      // Add close icon image
      const closeIconImg = document.createElement("img");
      closeIconImg.src = closeIcon;
      closeIconImg.alt = "Close";
      closeIconImg.style.width = mobile ? "24px" : "24px";
      closeIconImg.style.height = mobile ? "24px" : "24px";
      closeBtn.appendChild(closeIconImg);

      // Add guide icon and close button to right edge
      // Place flashBtn to the left, guideBtn and closeBtn to the right
      // Create a left container for flashBtn
      const leftTopbarContainer = document.createElement("div");
      leftTopbarContainer.style.display = "flex";
      leftTopbarContainer.style.alignItems = "center";
      leftTopbarContainer.style.flex = "1 1 0";
      leftTopbarContainer.style.justifyContent = "flex-start";
      leftTopbarContainer.appendChild(flashBtn);

      // Create a right container for guideBtn and closeBtn
      const rightTopbarContainer = document.createElement("div");
      rightTopbarContainer.style.display = "flex";
      rightTopbarContainer.style.alignItems = "center";
      rightTopbarContainer.style.flex = "1 1 0";
      rightTopbarContainer.style.justifyContent = "flex-end";
      rightTopbarContainer.appendChild(guideBtn);
      rightTopbarContainer.appendChild(closeBtn);

      // Add both containers to the topToolbar
      topToolbar.appendChild(leftTopbarContainer);
      // Add a spacer to center content if needed
      const topbarSpacer = document.createElement("div");
      topbarSpacer.style.flex = "2 1 0";
      topToolbar.appendChild(topbarSpacer);
      topToolbar.appendChild(rightTopbarContainer);

      modal.appendChild(topToolbar);

      // Camera area (between topbar and bottombar, pixel-perfect)
      const cameraArea = document.createElement("div");
      const bottomBarHeight = 80;
      // Use SafeAreaUtils for consistent bottom inset calculation
      const safeBottom = safeInsets.bottom;
      modal.appendChild(cameraArea);

      cameraArea.style.display = "flex";
      cameraArea.style.justifyContent = "center";
      cameraArea.style.alignItems = "center";
      cameraArea.style.position = "absolute";
      cameraArea.style.left = mobile ? "0" : "16px";
      cameraArea.style.top = `${topBarHeight + safeTop}px`;
      function setCameraAreaSize() {
        if (!mobile) {
          cameraArea.style.width = `calc(100% - 32px)`;
          cameraArea.style.height = `calc(100% - ${topBarHeight + safeTop + bottomBarHeight + safeBottom}px)`;
        } else {
          cameraArea.style.width = window.innerWidth + "px";
          cameraArea.style.height =
            window.innerHeight - topBarHeight - safeTop - bottomBarHeight - safeBottom + "px";
        }
      }
      setCameraAreaSize();
      window.addEventListener("resize", setCameraAreaSize);
      window.addEventListener("orientationchange", setCameraAreaSize);
      cameraArea.style.background = "#000";
      cameraArea.style.overflow = "hidden";
      cameraArea.style.borderRadius = !mobile ? "12px" : "0";

      // (Close button now in topToolbar)

      // --- Canvas for camera preview and blur effect ---
      const canvas = document.createElement("canvas");
      canvas.style.position = "absolute";
      canvas.style.top = "50%";
      canvas.style.left = "50%";
      canvas.style.transform = "translate(-50%, -50%)";
      cameraArea.appendChild(canvas);

      // Video element (hidden, used as source for canvas)
      const video = document.createElement("video");
      video.style.display = "none";
      video.setAttribute("playsinline", "true");
      video.setAttribute("autoplay", "true");
      video.setAttribute("muted", "true");
      video.autoplay = true;
      video.playsInline = true;
      cameraArea.appendChild(video);

      // --- Contrast & Brightness Sliders ---
      // Wrapper for left (contrast) and right (brightness)
      const contrastWrapper = document.createElement("div");
      contrastWrapper.style.display = "flex";
      contrastWrapper.style.flexDirection = "column";
      contrastWrapper.style.alignItems = "center";
      contrastWrapper.style.position = "absolute";
      contrastWrapper.style.left = "0";
      contrastWrapper.style.top = "50%";
      contrastWrapper.style.transform = "translateY(-50%)";
      contrastWrapper.style.zIndex = "2";

      const brightnessWrapper = document.createElement("div");
      brightnessWrapper.style.display = "flex";
      brightnessWrapper.style.flexDirection = "column";
      brightnessWrapper.style.alignItems = "center";
      brightnessWrapper.style.position = "absolute";
      brightnessWrapper.style.right = "0";
      brightnessWrapper.style.top = "50%";
      brightnessWrapper.style.transform = "translateY(-50%)";
      brightnessWrapper.style.zIndex = "2";

      // Contrast icon
      const contrastIcon = document.createElement("div");
      contrastIcon.innerHTML = `<svg fill="yellow" width="30" height="30" viewBox="0 0 100 100"><g><path d="M50,12.5c-20.712,0-37.5,16.793-37.5,37.502C12.5,70.712,29.288,87.5,50,87.5c20.712,0,37.5-16.788,37.5-37.498C87.5,29.293,70.712,12.5,50,12.5z M50.124,22.443C65.265,22.51,77.56,34.848,77.56,50.002c0,15.155-12.295,27.488-27.436,27.555V22.443z"/></g></svg>`;
      contrastIcon.style.marginBottom = "12px";
      contrastWrapper.appendChild(contrastIcon);

      // Brightness icon
      const brightnessIcon = document.createElement("div");
      brightnessIcon.innerHTML = `<svg width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="5" fill="yellow"/><g stroke="yellow" stroke-width="2"><line x1="12" y1="2" x2="12" y2="5"/><line x1="12" y1="19" x2="12" y2="22"/><line x1="2" y1="12" x2="5" y2="12"/><line x1="19" y1="12" x2="22" y2="12"/><line x1="4.22" y1="4.22" x2="6.34" y2="6.34"/><line x1="17.66" y1="17.66" x2="19.78" y2="19.78"/><line x1="4.22" y1="19.78" x2="6.34" y2="17.66"/><line x1="17.66" y1="6.34" x2="19.78" y2="4.22"/></g></svg>`;
      brightnessIcon.style.marginBottom = "12px";
      brightnessWrapper.appendChild(brightnessIcon);

      // Sliders
      const contrastSlider = document.createElement("input");
      contrastSlider.type = "range";
      contrastSlider.min = "50";
      contrastSlider.max = "200";
      contrastSlider.value = "100";
      contrastSlider.step = "1";
      (contrastSlider.style as CSSStyleDeclaration)["webkitAppearance"] =
        "slider-vertical";

      contrastSlider.style.width = "30px";
      contrastSlider.style.margin = "0";
      contrastSlider.style.background = "transparent";
      contrastSlider.style.cursor = "pointer";
      contrastWrapper.appendChild(contrastSlider);

      const brightnessSlider = document.createElement("input");
      brightnessSlider.type = "range";
      brightnessSlider.min = "50";
      brightnessSlider.max = "200";
      brightnessSlider.value = "100";
      brightnessSlider.step = "1";
      (brightnessSlider.style as CSSStyleDeclaration)["webkitAppearance"] =
        "slider-vertical";

      brightnessSlider.style.width = "30px";
      brightnessSlider.style.margin = "0";
      brightnessSlider.style.background = "transparent";
      brightnessSlider.style.cursor = "pointer";
      brightnessWrapper.appendChild(brightnessSlider);

      cameraArea.appendChild(contrastWrapper);
      cameraArea.appendChild(brightnessWrapper);

      // State for filter values
      let currentBrightness = 100;
      let currentContrast = 100;

      function updateCanvasFilter() {
        canvas.style.filter = `brightness(${currentBrightness}%) contrast(${currentContrast}%)`;
      }
      contrastSlider.addEventListener("input", function () {
        currentContrast = Number(this.value);
        updateCanvasFilter();
      });
      brightnessSlider.addEventListener("input", function () {
        currentBrightness = Number(this.value);
        updateCanvasFilter();
      });
      // On resize, keep wrappers vertically centered
      function setSliderVerticals() {
        contrastWrapper.style.top = "50%";
        brightnessWrapper.style.top = "50%";
      }
      window.addEventListener("resize", setSliderVerticals);
      window.addEventListener("orientationchange", setSliderVerticals);
      setSliderVerticals();

      // Helper to resize canvas to always fill cameraArea height (cover strategy)
      function resizeCanvasToFit() {
        if (!video.videoWidth || !video.videoHeight) return;
        const videoAspect = video.videoWidth / video.videoHeight;
        if (!mobile) {
          // Desktop: Fit canvas within cameraArea (modal), never overflow
          const containerW = cameraArea.clientWidth;
          const containerH = cameraArea.clientHeight;
          let drawW = containerW;
          let drawH = drawW / videoAspect;
          if (drawH > containerH) {
            drawH = containerH;
            drawW = drawH * videoAspect;
          }
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;
          canvas.style.width = `${drawW}px`;
          canvas.style.height = `${drawH}px`;
        } else {
          // Mobile: Always fill height, scale width up (may crop left/right)
          const containerH = cameraArea.clientHeight;
          const drawH = containerH;
          const drawW = drawH * videoAspect;
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;
          canvas.style.height = `${drawH}px`;
          canvas.style.width = `${drawW}px`;
        }
      }

      // On video metadata loaded and window resize, resize canvas
      video.onloadedmetadata = () => {
        resizeCanvasToFit();
      };
      window.addEventListener("resize", resizeCanvasToFit);

      // Adjustable rectangle and overlay
      let rectWidthRatio = 0.35; // 35% of video width by default
      const minRectWidthRatio = 0.15;
      const maxRectWidthRatio = 0.95;
      // let overlayTheme = "light"; // unused
      const overlayImg = new Image();
      overlayImg.src = this.ropePatternOverlayImage;
      overlayImg.style.opacity = "0.4";
      overlayImg.style.pointerEvents = "none";

      // Rectangle/overlay sizing logic (fit within video area)
      function getRect(videoW: number, videoH: number) {
        // No margin: rectangle fills the entire video area vertically
        const rectH = videoH;
        const rectW = videoW * rectWidthRatio;
        const rectX = (videoW - rectW) / 2;
        const rectY = 0;
        return { rectX, rectY, rectW, rectH };
      }

      // Draw video, blur, and overlay on canvas (Camera.js style)
      function drawVideoOnCanvas() {
        resizeCanvasToFit();
        const ctx = canvas.getContext("2d");
        if (!ctx || video.readyState < 2) return;
        const videoW = video.videoWidth;
        const videoH = video.videoHeight;
        if (!videoW || !videoH) return;
        // Rectangle area
        const { rectX, rectY, rectW, rectH } = getRect(videoW, videoH);
        // 1. Fill the entire canvas with black
        ctx.save();
        ctx.globalAlpha = 1;
        ctx.fillStyle = "#000";
        ctx.fillRect(0, 0, videoW, videoH);
        ctx.restore();
        // 2. Clear the rectangle area (window)
        ctx.save();
        ctx.clearRect(rectX, rectY, rectW, rectH);
        ctx.restore();
        // 3. Draw the sharp video only in the rectangle
        ctx.save();
        ctx.beginPath();
        ctx.rect(rectX, rectY, rectW, rectH);
        ctx.clip();
        ctx.drawImage(video, 0, 0, videoW, videoH);
        ctx.restore();
        // 4. Draw the overlay image in the rectangle, with dynamic color
        ctx.save();
        ctx.globalAlpha = 0.4;
        // Compute average color in the rectangle for theme
        let theme = "light";
        try {
          const imageData = ctx.getImageData(rectX, rectY, rectW, rectH);
          let r = 0,
            g = 0,
            b = 0;
          const data = imageData.data;
          for (let i = 0; i < data.length; i += 4) {
            r += data[i];
            g += data[i + 1];
            b += data[i + 2];
          }
          const pixelCount = data.length / 4;
          r /= pixelCount;
          g /= pixelCount;
          b /= pixelCount;
          const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
          theme = luminance < 128 ? "light" : "dark";
        } catch {
          /* ignore errors for theme detection */
        }
        ctx.filter = theme === "dark" ? "brightness(0) invert(1)" : "none";
        ctx.drawImage(overlayImg, rectX, rectY, rectW, rectH);
        ctx.filter = "none";
        ctx.restore();
        // 5. Draw the yellow border
        ctx.save();
        ctx.strokeStyle = "yellow";
        ctx.lineWidth = 4;
        ctx.strokeRect(rectX, rectY, rectW, rectH);
        ctx.restore();
      }

      // Animation loop for canvas drawing
      // let animationFrameId: number; // unused
      function animate() {
        drawVideoOnCanvas();
        requestAnimationFrame(animate);
      }

      // --- Bottom Bar ---
      const bottomBar = document.createElement("div");
      function setBottomBarSize() {
        if (!mobile) {
          bottomBar.style.width = "100%";
          bottomBar.style.left = "0";
        } else {
          bottomBar.style.width = window.innerWidth + "px";
          bottomBar.style.left = "0";
        }
        bottomBar.style.background = "#000";
        bottomBar.style.display = "flex";
        bottomBar.style.flexDirection = "column";
        bottomBar.style.alignItems = "center";
        bottomBar.style.position = "absolute";
        bottomBar.style.bottom = "0";
        bottomBar.style.borderRadius = mobile ? "0" : "0 0 12px 12px";
        bottomBar.style.zIndex = "100000";
        bottomBar.style.height = `${bottomBarHeight + safeBottom}px`;
        bottomBar.style.paddingBottom = `${safeBottom}px`;
      }
      setBottomBarSize();
      window.addEventListener("resize", setBottomBarSize);
      window.addEventListener("orientationchange", setBottomBarSize);
      modal.appendChild(bottomBar);
      // Prevent body scroll when modal is open
      const prevBodyOverflow = document.body.style.overflow;
      document.body.style.overflow = "hidden";

      // Bottom bar message
      const bottomMsg = document.createElement("div");
      bottomMsg.textContent = "Orient Rope to Pattern Overlay";
      bottomMsg.style.color = "#fff";
      bottomMsg.style.fontSize = mobile ? "0.95em" : "0.9em";
      bottomMsg.style.textAlign = "center";
      bottomMsg.style.margin = "4px 0 2px 0";
      bottomBar.appendChild(bottomMsg);

      // Bottom bar controls row
      const controlsRow = document.createElement("div");
      controlsRow.style.display = "flex";
      controlsRow.style.width = "100%";
      controlsRow.style.justifyContent = "space-between";
      controlsRow.style.alignItems = "center";
      controlsRow.style.position = "relative";
      controlsRow.style.gap = mobile ? "8px" : "16px";
      controlsRow.style.padding = "0 8px";
      bottomBar.appendChild(controlsRow);

      // Left container (Cancel button)
      const leftContainer = document.createElement("div");
      leftContainer.style.display = "flex";
      leftContainer.style.alignItems = "center";
      controlsRow.appendChild(leftContainer);

      // Center container (Capture button)
      const centerContainer = document.createElement("div");
      centerContainer.style.position = "absolute";
      centerContainer.style.left = "50%";
      centerContainer.style.transform = "translateX(-50%)";
      centerContainer.style.display = "flex";
      centerContainer.style.alignItems = "center";
      centerContainer.style.background = "#000";
      controlsRow.appendChild(centerContainer);

      // Right container (Expand, Collapse, Switch)
      const rightContainer = document.createElement("div");
      rightContainer.style.display = "flex";
      rightContainer.style.alignItems = "center";
      controlsRow.appendChild(rightContainer);

      // Cancel button (left)
      const cancelBtn = document.createElement("button");
      cancelBtn.textContent = "Cancel";
      cancelBtn.style.background = "none";
      cancelBtn.style.border = "none";
      cancelBtn.style.color = "#fff";
      cancelBtn.style.fontSize = "1em";
      cancelBtn.style.cursor = "pointer";
      cancelBtn.style.marginRight = mobile ? "8px" : "16px";
      leftContainer.appendChild(cancelBtn);

      // Capture button (center, smaller)
      const captureBtn = document.createElement("button");
      captureBtn.style.background = "#fff";
      captureBtn.style.border = '5px solid grey';
      captureBtn.style.width = mobile ? "48px" : "56px";
      captureBtn.style.height = mobile ? "48px" : "56px";
      captureBtn.style.borderRadius = "50%";
      captureBtn.style.display = "flex";
      captureBtn.style.alignItems = "center";
      captureBtn.style.justifyContent = "center";
      captureBtn.style.margin = "0 8px";
      captureBtn.style.boxShadow = "0 2px 8px #0006";
      captureBtn.style.cursor = "pointer";
      // Add camera icon inside capture button
      // const captureIconImg = document.createElement("img");
      // captureIconImg.src = cameraCaptureIcon;
      // captureIconImg.alt = "Capture";
      // captureIconImg.style.width = mobile ? "40px" : "40px";
      // captureIconImg.style.height = mobile ? "40px" : "40px";
      // captureBtn.appendChild(captureIconImg);
      centerContainer.appendChild(captureBtn);

      // Expand/collapse button group (right of capture, now in right container)
      const expandCollapseGroup = document.createElement("div");
      expandCollapseGroup.style.display = "flex";
      expandCollapseGroup.style.alignItems = "center";
      expandCollapseGroup.style.background = "#222";
      expandCollapseGroup.style.borderRadius = "8px";
      expandCollapseGroup.style.marginLeft = "8px";
      expandCollapseGroup.style.marginRight = "8px";
      rightContainer.appendChild(expandCollapseGroup);

      // Collapse overlay width button
      const decreaseBtn = document.createElement("button");
      decreaseBtn.innerHTML = "&minus;";
      decreaseBtn.title = "Decrease overlay width";
      decreaseBtn.style.background = "none";
      decreaseBtn.style.color = "#fff";
      decreaseBtn.style.border = "none";
      decreaseBtn.style.fontSize = "1.3em";
      decreaseBtn.style.cursor = "pointer";
      decreaseBtn.style.width = "32px";
      decreaseBtn.style.height = "32px";
      expandCollapseGroup.appendChild(decreaseBtn);

      // Expand overlay width button
      const increaseBtn = document.createElement("button");
      increaseBtn.innerHTML = "+";
      increaseBtn.title = "Increase overlay width";
      increaseBtn.style.background = "none";
      increaseBtn.style.color = "#fff";
      increaseBtn.style.border = "none";
      increaseBtn.style.fontSize = "1.3em";
      increaseBtn.style.cursor = "pointer";
      increaseBtn.style.width = "32px";
      increaseBtn.style.height = "32px";
      expandCollapseGroup.appendChild(increaseBtn);

      // Switch camera button (rightmost)
      const switchBtn = document.createElement("button");
      // switchBtn.innerHTML = "&#8635;"; // Remove unicode icon
      switchBtn.title = "Switch camera";
      switchBtn.style.background = "none";
      switchBtn.style.border = "none";
      switchBtn.style.cursor = "pointer";
      switchBtn.style.width = "48px";
      switchBtn.style.height = "48px";
      switchBtn.style.display = "flex";
      switchBtn.style.alignItems = "center";
      switchBtn.style.justifyContent = "center";
      // Add switch camera SVG icon
      const switchIconImg = document.createElement("img");
      switchIconImg.src = switchCameraIcon;
      switchIconImg.alt = "Switch Camera";
      switchIconImg.style.width = "44px";
      switchIconImg.style.height = "44px";
      switchBtn.appendChild(switchIconImg);
      // rightContainer.appendChild(switchBtn);

      // Rectangle/overlay sizing logic
      function updateRectSize() {
        // Redraw canvas with new rectangle size
        drawVideoOnCanvas();
      }

      // Increase/decrease overlay width handlers
      increaseBtn.onclick = () => {
        rectWidthRatio = Math.min(rectWidthRatio + 0.05, maxRectWidthRatio);
        updateRectSize();
      };
      decreaseBtn.onclick = () => {
        rectWidthRatio = Math.max(rectWidthRatio - 0.05, minRectWidthRatio);
        updateRectSize();
      };

      // Cancel handler
      cancelBtn.onclick = () => {
        this.cleanup().then(() => {
          document.body.style.overflow = prevBodyOverflow;
          reject(new Error("User cancelled"));
        });
      };
      closeBtn.onclick = cancelBtn.onclick;

      // Append modal
      document.body.appendChild(modal);

      // Start camera
      // --- AutoFlash integration ---
      let autoFlash: AutoFlash | null = null;
      let torchSupported = false;
      this.startCamera(video)
        .then(async () => {
          // Check for torch support and show/hide flash button
          const stream = video.srcObject as MediaStream;
          const videoTrack = stream.getVideoTracks()[0];
          torchSupported = await AutoFlash.isSupported(videoTrack);
          if (torchSupported) {
            flashBtn.style.visibility = 'visible';
            autoFlash = new AutoFlash(videoTrack, video, {
              onTorchChange: (on: boolean) => {
                // Optionally update flash icon state here
                flashIconImg.style.filter = on ? 'drop-shadow(0 0 8px yellow)' : 'none';
              }
            });
            autoFlash.start();
            // Manual toggle (optional, for demo: tap flashBtn to toggle auto/manual)
            let manualTorch = false;
            flashBtn.onclick = () => {
              if (!autoFlash) return;
              manualTorch = !manualTorch;
              if (manualTorch) {
                autoFlash.stop();
                autoFlash.setTorch(true);
                flashIconImg.style.filter = 'drop-shadow(0 0 8px yellow)';
              } else {
                autoFlash.setTorch(false);
                autoFlash.start();
                flashIconImg.style.filter = 'none';
              }
            };
          } else {
            flashBtn.style.visibility = 'hidden';
          }
          animate();
          // Capture handler
          captureBtn.onclick = () => {
            // Capture the sharp rectangle area only
            const videoW = video.videoWidth;
            const videoH = video.videoHeight;
            const { rectX, rectY, rectW, rectH } = getRect(videoW, videoH);
            const captureCanvas = document.createElement("canvas");
            captureCanvas.width = rectW;
            captureCanvas.height = rectH;
            const ctx = captureCanvas.getContext("2d");
            if (ctx) {
              ctx.drawImage(
                video,
                rectX,
                rectY,
                rectW,
                rectH,
                0,
                0,
                rectW,
                rectH
              );
              const image = captureCanvas.toDataURL("image/png");
              if (autoFlash) autoFlash.stop();
              this.cleanup().then(() => {
                document.body.style.overflow = prevBodyOverflow;
                resolve(image);
              });
            } else {
              if (autoFlash) autoFlash.stop();
              this.cleanup().then(() => {
                document.body.style.overflow = prevBodyOverflow;
                reject(new Error("Failed to capture image"));
              });
            }
          };
          // Switch camera handler
          switchBtn.onclick = () => {
            if (autoFlash) autoFlash.stop();
            this.currentCamera =
              this.currentCamera === "user" ? "environment" : "user";
            this.cleanup().then(() => {
              this.startCamera(video, true);
            });
          };
        })
        .catch(err => {
          if (autoFlash) autoFlash.stop();
          this.cleanup().then(() => {
            document.body.style.overflow = prevBodyOverflow;
            reject(err);
          });
        });
    });
  }

  /**
   * @internal
   * Start the camera and set the video source
   */
  private async startCamera(
    video: HTMLVideoElement,
    restart = false
  ): Promise<void> {
    // if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
    //   alert("Camera access is not supported in this environment.");
    //   throw new Error("Camera access is not supported in this environment.");
    // }
    if (restart && this.localMediaStream) {
      this.localMediaStream.getTracks().forEach(track => track.stop());
      this.localMediaStream = null;
    }
    const constraints: MediaStreamConstraints = {
      video: {
        facingMode: this.currentCamera,
        width: { ideal: 1280 },
        height: { ideal: 720 },
      },
      audio: false,
    };
    const stream = await navigator.mediaDevices.getUserMedia(constraints);
    video.srcObject = stream;
    this.localMediaStream = stream;
    await video.play();
  }

  /**
   * Cleanup modal and camera stream
   */
  /**
   * @internal
   * Returns a Promise that resolves after camera is fully released (for Android reliability)
   */
  private cleanup(): Promise<void> {
    return new Promise(resolve => {
      if (this.localMediaStream) {
        this.localMediaStream.getTracks().forEach(track => track.stop());
      }
      // Try to clear any video element srcObject
      if (this.modal) {
        // Remove video element srcObject if present
        const video = this.modal.querySelector('video');
        if (video) {
          try {
            (video as HTMLVideoElement).srcObject = null;
          } catch { }
        }
        document.body.removeChild(this.modal);
        this.modal = null;
      }
      // Wait 300ms to ensure camera is released (Android fix)
      setTimeout(() => {
        this.localMediaStream = null;
        resolve();
      }, 300);
    });
  }
}
