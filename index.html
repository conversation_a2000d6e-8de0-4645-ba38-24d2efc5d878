<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Cam Crop</title>
    <!-- <link rel="stylesheet" href="src/style.css"> -->
    <script type="module" src="./src/main.ts"></script>
  </head>
  <body>
    <input type="file" id="fileInput" accept="image/*" />
    <h4>Insight AI</h4>
    <button style="color:black" id="insightAIcameraBtn">insight Ai Camera</button>
    <button style="color:black" id="insightAICamCropperBtn">insight AI cam cropper</button> 
    <button style="color:black" id="insightAIcropperBtn">insight AI cropper</button>

    <h4>External</h4>
    <button style="color:black" id="extCamBtn">external Camera</button>
    <button style="color:black" id="extCamCropBtn">externalCam Cropper</button>
    <button style="color:black" id="extCropBtn">external Cropper</button>

    <h4>Internal</h4>
    <button style="color:black" id="intCamBtn">internal Camera</button>
    <button style="color:black" id="intCamBtn">internal Cam Cropper</button>
    <button style="color:black" id="intCropBtn">internal Cropper</button>

    <h4>other observations</h4>
    <button style="color:black" id="otherCamBtn">Camera</button>
    <button style="color:black" id="otherCamCropBtn">Cam Cropper</button>
    <button style="color:black" id="otherCropBtn">Cropper</button>
  </body>
</html>
