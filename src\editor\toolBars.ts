/**
 * @internal
 */
export function createToolBars(): {
  topBar: HTMLDivElement;
  bottomBar: HTMLDivElement;
} {
  // Create the top bar
  const topBar = document.createElement("div");
  topBar.style.position = "sticky";
  topBar.style.top = "0";
  topBar.style.backgroundColor = "#333";
  topBar.style.color = "white";
  topBar.style.padding = "10px";
  topBar.style.display = "flex";
  topBar.style.justifyContent = "space-between";
  topBar.style.alignItems = "center";
  topBar.style.zIndex = "1000"; // Ensure it stays on top
  topBar.innerText = "Top Bar";

  // Create the bottom bar
  const bottomBar = document.createElement("div");
  bottomBar.style.position = "sticky";
  bottomBar.style.bottom = "0";
  bottomBar.style.backgroundColor = "#333";
  bottomBar.style.color = "white";
  bottomBar.style.padding = "10px";
  bottomBar.style.display = "flex";
  bottomBar.style.justifyContent = "space-between";
  bottomBar.style.alignItems = "center";
  bottomBar.style.zIndex = "1000"; // Ensure it stays on top
  bottomBar.innerText = "Bottom Bar";

  return { topBar, bottomBar };
}
