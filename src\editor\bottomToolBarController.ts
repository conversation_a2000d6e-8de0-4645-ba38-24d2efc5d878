// Controller logic for BottomToolbar
import { BottomToolbarView } from "./bottomToolBarView";
import { Editor } from "./editor";
import redo from "../../public/assets/redo.svg?inline";
import undo from "../../public/assets/undo.svg?inline";
import zoomin from "../../public/assets/zoom-in.svg?inline";
import zoomout from "../../public/assets/zoom-out.svg?inline";
import rotateleft from "../../public/assets/rotate-left-1.svg?inline";
import rotateRight from "../../public/assets/rotate-right-1.svg?inline";
import annotateIcon from "../../public/assets/annotate.svg?inline";
import addAnnotation from "../../public/assets/pencil.svg?inline";
import addText from "../../public/assets/add-text.svg?inline";
import resetIcon from "../../public/assets/reset.svg?inline";
import eraserIcon from "../../public/assets/eraser.svg?inline";
import cropIcon from "../../public/assets/crop.svg?inline";
import imageScaleIcon from "../../public/assets/image-scale.svg?inline";
import { ImagesScale } from "../utils/imagesScale";
import { ModalUI } from "../utils/annotation";
import { CropButtonHandler } from "../utils/cropButtonHandler";
import { setAnnotateMode, shapeStore, shapeType } from "./editorModeStore";

/**
 * @internal
 */
export class BottomToolbarController {
  private static instance: BottomToolbarController;
  private view: BottomToolbarView;
  private editor: Editor;
  private zoomLevel: number = 10;
  private scaleDisplayed: boolean = false;
  editorContainer!: HTMLDivElement;
  // private selectedShape: string = 'rectangle';

  private constructor(editor: Editor) {
    this.editor = editor;
    // this.view = new BottomToolbarView({
    this.view = BottomToolbarView.getInstance({
      eraser: eraserIcon,
      crop: cropIcon,
      annotate: annotateIcon,
      imageScale: imageScaleIcon,
      rotateLeft: rotateleft,
      rotateRight: rotateRight,
      undo: undo,
      redo: redo,
      zoomOut: zoomout,
      zoomIn: zoomin,
      addText: addText,
      resetIcon: resetIcon,
      addAnnotation: addAnnotation,
    });
    this.wireEvents();
  }

  public static getInstance(editor: Editor): BottomToolbarController {
    if (!BottomToolbarController.instance) {
      BottomToolbarController.instance = new BottomToolbarController(editor);
    }
    return BottomToolbarController.instance;
  }

  private wireEvents() {
    // Eraser
    this.view.eraserButton.onclick = () => {
      const cropButtonHandler = CropButtonHandler.getInstance();
      cropButtonHandler.enableEraserMode();
    };
    // Crop
    this.view.cropButton.onclick = () => {
      this.editor.initializeCropper();
    };

    this.view.annotateButton.onclick = () => {
      setAnnotateMode();
      const cropButtonHandler = CropButtonHandler.getInstance();
      cropButtonHandler.initialize(this.editor);
      const imageContainer = this.editor
        .getEditorContainer()
        .querySelector(".image-container");
      if (imageContainer) {
        const img = imageContainer.querySelector("img");
        if (img instanceof HTMLImageElement) {
          // Get the visible size and position of the image as displayed
          const imgRect = img.getBoundingClientRect();
          const containerRect = imageContainer.getBoundingClientRect();
          // Calculate the visible area of the image inside the container
          // (in case of object-fit, scaling, etc.)
          // We'll use the displayed width/height and offset within the container
          const scaleX = img.naturalWidth / img.width;
          const scaleY = img.naturalHeight / img.height;
          // The visible part of the image in the container
          const visibleLeft = Math.max(0, containerRect.left - imgRect.left);
          const visibleTop = Math.max(0, containerRect.top - imgRect.top);
          const visibleWidth = Math.min(imgRect.width, containerRect.width);
          const visibleHeight = Math.min(imgRect.height, containerRect.height);
          // Map visible area to natural image coordinates
          const sx = visibleLeft * scaleX;
          const sy = visibleTop * scaleY;
          const sw = visibleWidth * scaleX;
          const sh = visibleHeight * scaleY;
          // The canvas should match the visible area
          const canvasElement = document.createElement("canvas");
          canvasElement.width = visibleWidth;
          canvasElement.height = visibleHeight;
          const ctx = canvasElement.getContext("2d");
          if (ctx) {
            ctx.drawImage(
              img,
              sx,
              sy,
              sw,
              sh,
              0,
              0,
              visibleWidth,
              visibleHeight
            );
            cropButtonHandler.handleCropButtonClick(
              canvasElement,
              "Recctangle"
            );
          }
        } else {
          return;
        }
      } else {
        return;
      }
    };

    this.view.addTextButton.onclick = () => {
      const cropButtonHandler = CropButtonHandler.getInstance();
      cropButtonHandler.handleAddTextButtonClick();
    };

    // Annotate
    this.view.addAnnotationButton.onclick = (event: Event) => {
      const annotationModal = ModalUI.getInstance();
      annotationModal.setContainer(this.editor.getCropContainer());
      if (annotationModal.isOpen()) {
        annotationModal.hide();
      } else {
        const button = event.currentTarget as HTMLElement;
        annotationModal.show(button);
      }
    };

    // Image Scale
    this.view.imageScaleButton.onclick = () => {
      if (!this.scaleDisplayed) {
        this.scaleDisplayed = true;
        const imageScale = ImagesScale.getInstance(
          "AMSTEEL-BLUE",
          "External",
          this.editor
        );
        imageScale.show();
        imageScale.onClose(() => {
          this.scaleDisplayed = false;
        });
      }
    };
    // Rotate Left
    this.view.rotateLeftButton.onclick = () => {
      const cropper = this.editor.getCropper();
      if (cropper) {
        const cropperImage = cropper.getCropperImage();
        if (cropperImage) {
          const angleInRadians = -90 * (Math.PI / 180);
          cropperImage.$rotate(angleInRadians);
        }
      }
    };
    // Rotate Right
    this.view.rotateRightButton.onclick = () => {
      const cropper = this.editor.getCropper();
      if (cropper) {
        const cropperImage = cropper.getCropperImage();
        if (cropperImage) {
          const angleInRadians = 90 * (Math.PI / 180);
          cropperImage.$rotate(angleInRadians);
        }
      }
    };
    // Undo
    this.view.undoButton.onclick = () => {
      const cropButtonHandler = CropButtonHandler.getInstance();
      cropButtonHandler.getUndoRedoManager().undo();
    };
    // Redo
    this.view.redoButton.onclick = () => {
      const cropButtonHandler = CropButtonHandler.getInstance();
      cropButtonHandler.getUndoRedoManager().redo();
    };
    // Zoom Out
    this.view.zoomOutButton.onclick = () => {
      const cropper = this.editor.getCropper();
      if (cropper) {
        const cropperImage = cropper.getCropperImage();
        if (cropperImage) {
          cropperImage.$scale(0.9);
          this.zoomLevel = Math.max(this.zoomLevel - 10, 10);
          this.view.zoomLabel.textContent = `${this.zoomLevel}%`;
        }
      }
    };
    // Zoom In
    this.view.zoomInButton.onclick = () => {
      const cropper = this.editor.getCropper();
      if (cropper) {
        const cropperImage = cropper.getCropperImage();
        if (cropperImage) {
          cropperImage.$scale(1.1);
          this.zoomLevel = Math.min(this.zoomLevel + 10, 200);
          this.view.zoomLabel.textContent = `${this.zoomLevel}%`;
        }
      }
    };

    shapeStore.subscribe((value: shapeType) => {
      if (value === "Oval") {
        console.log("Oval shape selected");
        const cropperSelection = this.editor
          .getCropper()
          ?.getCropperSelection(); // Get the current cropper selectio
        // Apply styles or logic for oval
        cropperSelection?.$addStyles(`
                    :host {
                        border-radius: 50%;
                    }
                `);

        if (cropperSelection) {
          cropperSelection.style.borderRadius = "50%";
          cropperSelection.style.overflow = "hidden";
        }

        // cropper-shade {
        //     border-radius:50% ;
        //   }
      } else if (value === "Rectangle") {
        console.log("Rectangle shape selected");
        // Reset styles if needed
        const cropperSelection = this.editor
          .getCropper()
          ?.getCropperSelection();
        cropperSelection?.$addStyles(`
                    :host {
                        border-radius: 0;
                    }
                `);

        if (cropperSelection) {
          cropperSelection.style.borderRadius = "0";
          cropperSelection.style.overflow = "hidden";
        }
      }
    });
  }

  public getToolbar(): HTMLDivElement {
    return this.view.getToolbar();
  }
}
