/** @internal */
export function showCroppedImageModal(
  croppedImage: string,
  actions: {
    onEdit: () => void;
    onContinue: () => void;
    onCancel: () => void;
  },
  shadowRoot: ShadowRoot
) {
  // Create modal container
  const modal = document.createElement("div");
  modal.className = "cropped-image-modal";

  // Create image preview
  const image = document.createElement("img");
  image.src = croppedImage;
  image.alt = "Cropped Image";
  image.className = "cropped-image-preview";
  modal.appendChild(image);

  // Create buttons container
  const buttonsContainer = document.createElement("div");
  buttonsContainer.className = "modal-buttons-container";

  // Create Edit button
  const editButton = document.createElement("button");
  editButton.textContent = "Edit";
  editButton.className = "modal-button edit-button";
  editButton.onclick = () => {
    actions.onEdit();
    shadowRoot.removeChild(modal);
  };
  buttonsContainer.appendChild(editButton);

  // Create Continue button
  const continueButton = document.createElement("button");
  continueButton.textContent = "Continue";
  continueButton.className = "modal-button continue-button";
  continueButton.onclick = () => {
    actions.onContinue();
    shadowRoot.removeChild(modal);
  };
  buttonsContainer.appendChild(continueButton);

  // Create Cancel button
  const cancelButton = document.createElement("button");
  cancelButton.textContent = "Cancel";
  cancelButton.className = "modal-button cancel-button";
  cancelButton.onclick = () => {
    actions.onCancel();
    shadowRoot.removeChild(modal);
  };
  buttonsContainer.appendChild(cancelButton);

  // Append buttons container to modal
  modal.appendChild(buttonsContainer);

  // Append modal to shadow root
  shadowRoot.appendChild(modal);
}

/** @internal */
export function switchToOvalSelection(cropper: any): void {
  // Remove existing cropper selection if any
  const cropperSelection = cropper.getCropperSelection();
  // if (existingSelection) {
  //     cropperCanvas?.removeChild(existingSelection);
  // }

  // Create and append oval cropper selection
  // const ovalSelection = document.createElement('cropper-oval-selection');
  // ovalSelection.setAttribute('movable', 'true');
  // ovalSelection.setAttribute('resizable', 'true');
  // ovalSelection.setAttribute('zoomable', 'true');
  // ovalSelection.setAttribute('outlined', 'true');
  // cropperCanvas?.appendChild(ovalSelection);

  // Ensure the oval selection is rendered properly and set border radius
  cropperSelection?.$addStyles(`
    :host {
        border-radius: 50%;
    }
    `);
}
