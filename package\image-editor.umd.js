(function(B,ut){typeof exports=="object"&&typeof module<"u"?ut(exports):typeof define=="function"&&define.amd?define(["exports"],ut):(B=typeof globalThis<"u"?globalThis:B||self,ut(B.ImageEditor={}))})(this,function(B){"use strict";var S0=Object.defineProperty;var T0=(B,ut,dt)=>ut in B?S0(B,ut,{enumerable:!0,configurable:!0,writable:!0,value:dt}):B[ut]=dt;var M=(B,ut,dt)=>T0(B,typeof ut!="symbol"?ut+"":ut,dt);const ut=typeof window<"u"&&typeof window.document<"u",dt=ut?window:{},Pn=ut?"ontouchstart"in dt.document.documentElement:!1,Ln=ut?"PointerEvent"in dt:!1,be="cropper",ni=`${be}-canvas`,Hl=`${be}-crosshair`,Nl=`${be}-grid`,Wl=`${be}-handle`,ri=`${be}-image`,Qt=`${be}-selection`,Vl=`${be}-shade`,Xl=`${be}-viewer`,ns="select",uo="move",oi="scale",Rn="rotate",rs="transform",Ce="none",go="n-resize",fo="e-resize",po="s-resize",mo="w-resize",Di="ne-resize",Ai="nw-resize",Pi="se-resize",Li="sw-resize",Yl="action",Gl=Pn?"touchend touchcancel":"mouseup",Ul=Pn?"touchmove":"mousemove",vo=Ln?"pointerdown":Pn?"touchstart":"mousedown",yo=Ln?"pointermove":Ul,bo=Ln?"pointerup pointercancel":Gl,Co="error",wo="keydown",ai="load",xo="wheel",hi="action",Re="actionend",ql="actionmove",Ie="actionstart",we="change",In="transform";function os(r){return typeof r=="string"}const _o=Number.isNaN||dt.isNaN;function $(r){return typeof r=="number"&&!_o(r)}function Ct(r){return $(r)&&r>0&&r<1/0}function Kl(r){return typeof r>"u"}function So(r){return typeof r=="object"&&r!==null}const{hasOwnProperty:Zl}=Object.prototype;function as(r){if(!So(r))return!1;try{const{constructor:t}=r,{prototype:e}=t;return t&&e&&Zl.call(e,"isPrototypeOf")}catch{return!1}}function jn(r){return typeof r=="function"}function Ri(r){return typeof r=="object"&&r!==null&&r.nodeType===1}const Jl=/([a-z\d])([A-Z])/g;function To(r){return String(r).replace(Jl,"$1-$2").toLowerCase()}const Ql=/-[A-z\d]/g;function Eo(r){return r.replace(Ql,t=>t.slice(1).toUpperCase())}const ko=/\s\s*/;function st(r,t,e,i){t.trim().split(ko).forEach(s=>{r.removeEventListener(s,e,i)})}function ot(r,t,e,i){t.trim().split(ko).forEach(s=>{r.addEventListener(s,e,i)})}function Oo(r,t,e,i){ot(r,t,e,Object.assign(Object.assign({},i),{once:!0}))}const tc={bubbles:!0,cancelable:!0,composed:!0};function ec(r,t,e,i){return r.dispatchEvent(new CustomEvent(t,Object.assign(Object.assign(Object.assign({},tc),{detail:e}),i)))}const Mo=Promise.resolve();function ic(r,t){return t?Mo.then(r?t.bind(r):t):Mo}function Do(r){const{documentElement:t}=r.ownerDocument,e=r.getBoundingClientRect();return{left:e.left+(dt.pageXOffset-t.clientLeft),top:e.top+(dt.pageYOffset-t.clientTop)}}const sc=/deg|g?rad|turn$/i;function Bn(r){const t=parseFloat(r)||0;if(t!==0){const[e="rad"]=String(r).match(sc)||[];switch(e.toLowerCase()){case"deg":return t/360*(Math.PI*2);case"grad":return t/400*(Math.PI*2);case"turn":return t*(Math.PI*2)}}return t}const Ao="contain",nc="cover";function hs(r,t=Ao){const{aspectRatio:e}=r;let{width:i,height:s}=r;const n=Ct(i),o=Ct(s);if(n&&o){const a=s*e;t===Ao&&a>i||t===nc&&a<i?s=i/e:i=s*e}else n?s=i/e:o&&(i=s*e);return{width:i,height:s}}function Po(r,...t){if(t.length===0)return r;const[e,i,s,n,o,a]=r,[h,l,c,u,d,g]=t[0];return r=[e*h+s*l,i*h+n*l,e*c+s*u,i*c+n*u,e*d+s*g+o,i*d+n*g+a],Po(r,...t.slice(1))}var rc=":host([hidden]){display:none!important}";const oc=/left|top|width|height/i,Lo="open",ls=new WeakMap,cs=new WeakMap,Ro=new Map,Io=dt.document&&Array.isArray(dt.document.adoptedStyleSheets)&&"replaceSync"in dt.CSSStyleSheet.prototype;class he extends HTMLElement{get $sharedStyle(){return`${this.themeColor?`:host{--theme-color: ${this.themeColor};}`:""}${rc}`}constructor(){var t,e;super(),this.shadowRootMode=Lo,this.slottable=!0;const i=(e=(t=Object.getPrototypeOf(this))===null||t===void 0?void 0:t.constructor)===null||e===void 0?void 0:e.$name;i&&Ro.set(i,this.tagName.toLowerCase())}static get observedAttributes(){return["shadow-root-mode","slottable","theme-color"]}attributeChangedCallback(t,e,i){if(Object.is(i,e))return;const s=Eo(t),n=this[s];let o=i;switch(typeof n){case"boolean":o=i!==null&&i!=="false";break;case"number":o=Number(i);break}switch(this[s]=o,t){case"theme-color":{const a=cs.get(this),h=this.$sharedStyle;a&&h&&(Io?a.replaceSync(h):a.textContent=h);break}}}$propertyChangedCallback(t,e,i){if(!Object.is(i,e))switch(t=To(t),typeof i){case"boolean":i===!0?this.hasAttribute(t)||this.setAttribute(t,""):this.removeAttribute(t);break;case"number":_o(i)?i="":i=String(i);default:i?this.getAttribute(t)!==i&&this.setAttribute(t,i):this.removeAttribute(t)}}connectedCallback(){Object.getPrototypeOf(this).constructor.observedAttributes.forEach(e=>{const i=Eo(e);let s=this[i];Kl(s)||this.$propertyChangedCallback(i,void 0,s),Object.defineProperty(this,i,{enumerable:!0,configurable:!0,get(){return s},set(n){const o=s;s=n,this.$propertyChangedCallback(i,o,n)}})});const t=this.attachShadow({mode:this.shadowRootMode||Lo});if(this.shadowRoot||ls.set(this,t),cs.set(this,this.$addStyles(this.$sharedStyle)),this.$style&&this.$addStyles(this.$style),this.$template){const e=document.createElement("template");e.innerHTML=this.$template,t.appendChild(e.content)}if(this.slottable){const e=document.createElement("slot");t.appendChild(e)}}disconnectedCallback(){cs.has(this)&&cs.delete(this),ls.has(this)&&ls.delete(this)}$getTagNameOf(t){var e;return(e=Ro.get(t))!==null&&e!==void 0?e:t}$setStyles(t){return Object.keys(t).forEach(e=>{let i=t[e];$(i)&&(i!==0&&oc.test(e)?i=`${i}px`:i=String(i)),this.style[e]=i}),this}$getShadowRoot(){return this.shadowRoot||ls.get(this)}$addStyles(t){let e;const i=this.$getShadowRoot();return Io?(e=new CSSStyleSheet,e.replaceSync(t),i.adoptedStyleSheets=i.adoptedStyleSheets.concat(e)):(e=document.createElement("style"),e.textContent=t,i.appendChild(e)),e}$emit(t,e,i){return ec(this,t,e,i)}$nextTick(t){return ic(this,t)}static $define(t,e){So(t)&&(e=t,t=""),t||(t=this.$name||this.name),t=To(t),ut&&dt.customElements&&!dt.customElements.get(t)&&customElements.define(t,this,e)}}he.$version="2.0.0";var ac=":host{display:inline-block}img{display:block;height:100%;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;width:100%}";const jo=new WeakMap,Bo=["alt","crossorigin","decoding","importance","loading","referrerpolicy","sizes","src","srcset"];class Fn extends he{constructor(){super(...arguments),this.$matrix=[1,0,0,1,0,0],this.$onLoad=null,this.$onCanvasAction=null,this.$onCanvasActionEnd=null,this.$onCanvasActionStart=null,this.$actionStartTarget=null,this.$style=ac,this.$image=new Image,this.initialCenterSize="contain",this.rotatable=!1,this.scalable=!1,this.skewable=!1,this.slottable=!1,this.translatable=!1}set $canvas(t){jo.set(this,t)}get $canvas(){return jo.get(this)}static get observedAttributes(){return super.observedAttributes.concat(Bo,["initial-center-size","rotatable","scalable","skewable","translatable"])}attributeChangedCallback(t,e,i){Object.is(i,e)||(super.attributeChangedCallback(t,e,i),Bo.includes(t)&&this.$image.setAttribute(t,i))}$propertyChangedCallback(t,e,i){if(!Object.is(i,e))switch(super.$propertyChangedCallback(t,e,i),t){case"initialCenterSize":this.$nextTick(()=>{this.$center(i)});break}}connectedCallback(){super.connectedCallback();const{$image:t}=this,e=this.closest(this.$getTagNameOf(ni));e&&(this.$canvas=e,this.$setStyles({display:"block",position:"absolute"}),this.$onCanvasActionStart=i=>{var s,n;this.$actionStartTarget=(n=(s=i.detail)===null||s===void 0?void 0:s.relatedEvent)===null||n===void 0?void 0:n.target},this.$onCanvasActionEnd=()=>{this.$actionStartTarget=null},this.$onCanvasAction=this.$handleAction.bind(this),ot(e,Ie,this.$onCanvasActionStart),ot(e,Re,this.$onCanvasActionEnd),ot(e,hi,this.$onCanvasAction)),this.$onLoad=this.$handleLoad.bind(this),ot(t,ai,this.$onLoad),this.$getShadowRoot().appendChild(t)}disconnectedCallback(){const{$image:t,$canvas:e}=this;e&&(this.$onCanvasActionStart&&(st(e,Ie,this.$onCanvasActionStart),this.$onCanvasActionStart=null),this.$onCanvasActionEnd&&(st(e,Re,this.$onCanvasActionEnd),this.$onCanvasActionEnd=null),this.$onCanvasAction&&(st(e,hi,this.$onCanvasAction),this.$onCanvasAction=null)),t&&this.$onLoad&&(st(t,ai,this.$onLoad),this.$onLoad=null),this.$getShadowRoot().removeChild(t),super.disconnectedCallback()}$handleLoad(){const{$image:t}=this;this.$setStyles({width:t.naturalWidth,height:t.naturalHeight}),this.$canvas&&this.$center(this.initialCenterSize)}$handleAction(t){if(this.hidden||!(this.rotatable||this.scalable||this.translatable))return;const{$canvas:e}=this,{detail:i}=t;if(i){const{relatedEvent:s}=i;let{action:n}=i;switch(n===rs&&(!this.rotatable||!this.scalable)&&(this.rotatable?n=Rn:this.scalable?n=oi:n=Ce),n){case uo:if(this.translatable){let o=null;s&&(o=s.target.closest(this.$getTagNameOf(Qt))),o||(o=e.querySelector(this.$getTagNameOf(Qt))),o&&o.multiple&&!o.active&&(o=e.querySelector(`${this.$getTagNameOf(Qt)}[active]`)),(!o||o.hidden||!o.movable||o.dynamic||!(this.$actionStartTarget&&o.contains(this.$actionStartTarget)))&&this.$move(i.endX-i.startX,i.endY-i.startY)}break;case Rn:if(this.rotatable)if(s){const{x:o,y:a}=this.getBoundingClientRect();this.$rotate(i.rotate,s.clientX-o,s.clientY-a)}else this.$rotate(i.rotate);break;case oi:if(this.scalable)if(s){const o=s.target.closest(this.$getTagNameOf(Qt));if(!o||!o.zoomable||o.zoomable&&o.dynamic){const{x:a,y:h}=this.getBoundingClientRect();this.$zoom(i.scale,s.clientX-a,s.clientY-h)}}else this.$zoom(i.scale);break;case rs:if(this.rotatable&&this.scalable){const{rotate:o}=i;let{scale:a}=i;a<0?a=1/(1-a):a+=1;const h=Math.cos(o),l=Math.sin(o),[c,u,d,g]=[h*a,l*a,-l*a,h*a];if(s){const f=this.getBoundingClientRect(),p=s.clientX-f.x,v=s.clientY-f.y,[b,w,x,_]=this.$matrix,k=f.width/2,A=f.height/2,L=p-k,O=v-A,I=(L*_-x*O)/(b*_-x*w),F=(O*b-w*L)/(b*_-x*w);this.$transform(c,u,d,g,I*(1-c)+F*d,F*(1-g)+I*u)}else this.$transform(c,u,d,g,0,0)}break}}}$ready(t){const{$image:e}=this,i=new Promise((s,n)=>{const o=new Error("Failed to load the image source");if(e.complete)e.naturalWidth>0&&e.naturalHeight>0?s(e):n(o);else{const a=()=>{st(e,Co,h),s(e)},h=()=>{st(e,ai,a),n(o)};Oo(e,ai,a),Oo(e,Co,h)}});return jn(t)&&i.then(s=>(t(s),s)),i}$center(t){const{parentElement:e}=this;if(!e)return this;const i=e.getBoundingClientRect(),s=i.width,n=i.height,{x:o,y:a,width:h,height:l}=this.getBoundingClientRect(),c=o+h/2,u=a+l/2,d=i.x+s/2,g=i.y+n/2;if(this.$move(d-c,g-u),t&&(h!==s||l!==n)){const f=s/h,p=n/l;switch(t){case"cover":this.$scale(Math.max(f,p));break;case"contain":this.$scale(Math.min(f,p));break}}return this}$move(t,e=t){if(this.translatable&&$(t)&&$(e)){const[i,s,n,o]=this.$matrix,a=(t*o-n*e)/(i*o-n*s),h=(e*i-s*t)/(i*o-n*s);this.$translate(a,h)}return this}$moveTo(t,e=t){if(this.translatable&&$(t)&&$(e)){const[i,s,n,o]=this.$matrix,a=(t*o-n*e)/(i*o-n*s),h=(e*i-s*t)/(i*o-n*s);this.$setTransform(i,s,n,o,a,h)}return this}$rotate(t,e,i){if(this.rotatable){const s=Bn(t),n=Math.cos(s),o=Math.sin(s),[a,h,l,c]=[n,o,-o,n];if($(e)&&$(i)){const[u,d,g,f]=this.$matrix,{width:p,height:v}=this.getBoundingClientRect(),b=p/2,w=v/2,x=e-b,_=i-w,k=(x*f-g*_)/(u*f-g*d),A=(_*u-d*x)/(u*f-g*d);this.$transform(a,h,l,c,k*(1-a)-A*l,A*(1-c)-k*h)}else this.$transform(a,h,l,c,0,0)}return this}$zoom(t,e,i){if(!this.scalable||t===0)return this;if(t<0?t=1/(1-t):t+=1,$(e)&&$(i)){const[s,n,o,a]=this.$matrix,{width:h,height:l}=this.getBoundingClientRect(),c=h/2,u=l/2,d=e-c,g=i-u,f=(d*a-o*g)/(s*a-o*n),p=(g*s-n*d)/(s*a-o*n);this.$transform(t,0,0,t,f*(1-t),p*(1-t))}else this.$scale(t);return this}$scale(t,e=t){return this.scalable&&this.$transform(t,0,0,e,0,0),this}$skew(t,e=0){if(this.skewable){const i=Bn(t),s=Bn(e);this.$transform(1,Math.tan(s),Math.tan(i),1,0,0)}return this}$translate(t,e=t){return this.translatable&&$(t)&&$(e)&&this.$transform(1,0,0,1,t,e),this}$transform(t,e,i,s,n,o){return $(t)&&$(e)&&$(i)&&$(s)&&$(n)&&$(o)?this.$setTransform(Po(this.$matrix,[t,e,i,s,n,o])):this}$setTransform(t,e,i,s,n,o){if((this.rotatable||this.scalable||this.skewable||this.translatable)&&(Array.isArray(t)&&([t,e,i,s,n,o]=t),$(t)&&$(e)&&$(i)&&$(s)&&$(n)&&$(o))){const a=[...this.$matrix],h=[t,e,i,s,n,o];if(this.$emit(In,{matrix:h,oldMatrix:a})===!1)return this;this.$matrix=h,this.style.transform=`matrix(${h.join(", ")})`}return this}$getTransform(){return this.$matrix.slice()}$resetTransform(){return this.$setTransform([1,0,0,1,0,0])}}Fn.$name=ri,Fn.$version="2.0.0";var hc=':host{display:block;min-height:100px;min-width:200px;overflow:hidden;position:relative;touch-action:none;-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;user-select:none}:host([background]){background-color:#fff;background-image:repeating-linear-gradient(45deg,#ccc 25%,transparent 0,transparent 75%,#ccc 0,#ccc),repeating-linear-gradient(45deg,#ccc 25%,transparent 0,transparent 75%,#ccc 0,#ccc);background-image:repeating-conic-gradient(#ccc 0 25%,#fff 0 50%);background-position:0 0,.5rem .5rem;background-size:1rem 1rem}:host([disabled]){pointer-events:none}:host([disabled]):after{bottom:0;content:"";cursor:not-allowed;display:block;left:0;pointer-events:none;position:absolute;right:0;top:0}';class $n extends he{constructor(){super(...arguments),this.$onPointerDown=null,this.$onPointerMove=null,this.$onPointerUp=null,this.$onWheel=null,this.$wheeling=!1,this.$pointers=new Map,this.$style=hc,this.$action=Ce,this.background=!1,this.disabled=!1,this.scaleStep=.1,this.themeColor="#39f"}static get observedAttributes(){return super.observedAttributes.concat(["background","disabled","scale-step"])}connectedCallback(){super.connectedCallback(),this.disabled||this.$bind()}disconnectedCallback(){this.disabled||this.$unbind(),super.disconnectedCallback()}$propertyChangedCallback(t,e,i){if(!Object.is(i,e))switch(super.$propertyChangedCallback(t,e,i),t){case"disabled":i?this.$unbind():this.$bind();break}}$bind(){this.$onPointerDown||(this.$onPointerDown=this.$handlePointerDown.bind(this),ot(this,vo,this.$onPointerDown)),this.$onPointerMove||(this.$onPointerMove=this.$handlePointerMove.bind(this),ot(this.ownerDocument,yo,this.$onPointerMove)),this.$onPointerUp||(this.$onPointerUp=this.$handlePointerUp.bind(this),ot(this.ownerDocument,bo,this.$onPointerUp)),this.$onWheel||(this.$onWheel=this.$handleWheel.bind(this),ot(this,xo,this.$onWheel,{passive:!1,capture:!0}))}$unbind(){this.$onPointerDown&&(st(this,vo,this.$onPointerDown),this.$onPointerDown=null),this.$onPointerMove&&(st(this.ownerDocument,yo,this.$onPointerMove),this.$onPointerMove=null),this.$onPointerUp&&(st(this.ownerDocument,bo,this.$onPointerUp),this.$onPointerUp=null),this.$onWheel&&(st(this,xo,this.$onWheel,{capture:!0}),this.$onWheel=null)}$handlePointerDown(t){const{buttons:e,button:i,type:s}=t;if(this.disabled||(s==="pointerdown"&&t.pointerType==="mouse"||s==="mousedown")&&($(e)&&e!==1||$(i)&&i!==0||t.ctrlKey))return;const{$pointers:n}=this;let o="";if(t.changedTouches)Array.from(t.changedTouches).forEach(({identifier:a,pageX:h,pageY:l})=>{n.set(a,{startX:h,startY:l,endX:h,endY:l})});else{const{pointerId:a=0,pageX:h,pageY:l}=t;n.set(a,{startX:h,startY:l,endX:h,endY:l})}n.size>1?o=rs:Ri(t.target)&&(o=t.target.action||t.target.getAttribute(Yl)||""),this.$emit(Ie,{action:o,relatedEvent:t})!==!1&&(t.preventDefault(),this.$action=o,this.style.willChange="transform")}$handlePointerMove(t){const{$action:e,$pointers:i}=this;if(this.disabled||e===Ce||i.size===0||this.$emit(ql,{action:e,relatedEvent:t})===!1)return;if(t.preventDefault(),t.changedTouches)Array.from(t.changedTouches).forEach(({identifier:n,pageX:o,pageY:a})=>{const h=i.get(n);h&&Object.assign(h,{endX:o,endY:a})});else{const{pointerId:n=0,pageX:o,pageY:a}=t,h=i.get(n);h&&Object.assign(h,{endX:o,endY:a})}const s={action:e,relatedEvent:t};if(e===rs){const n=new Map(i);let o=0,a=0,h=0,l=0,c=t.pageX,u=t.pageY;i.forEach((f,p)=>{n.delete(p),n.forEach(v=>{let b=v.startX-f.startX,w=v.startY-f.startY,x=v.endX-f.endX,_=v.endY-f.endY,k=0,A=0,L=0,O=0;if(b===0?w<0?L=Math.PI*2:w>0&&(L=Math.PI):b>0?L=Math.PI/2+Math.atan(w/b):b<0&&(L=Math.PI*1.5+Math.atan(w/b)),x===0?_<0?O=Math.PI*2:_>0&&(O=Math.PI):x>0?O=Math.PI/2+Math.atan(_/x):x<0&&(O=Math.PI*1.5+Math.atan(_/x)),O>0||L>0){const I=O-L,F=Math.abs(I);F>o&&(o=F,h=I,c=(f.startX+v.startX)/2,u=(f.startY+v.startY)/2)}if(b=Math.abs(b),w=Math.abs(w),x=Math.abs(x),_=Math.abs(_),b>0&&w>0?k=Math.sqrt(b*b+w*w):b>0?k=b:w>0&&(k=w),x>0&&_>0?A=Math.sqrt(x*x+_*_):x>0?A=x:_>0&&(A=_),k>0&&A>0){const I=(A-k)/k,F=Math.abs(I);F>a&&(a=F,l=I,c=(f.startX+v.startX)/2,u=(f.startY+v.startY)/2)}})});const d=o>0,g=a>0;d&&g?(s.rotate=h,s.scale=l,s.centerX=c,s.centerY=u):d?(s.action=Rn,s.rotate=h,s.centerX=c,s.centerY=u):g?(s.action=oi,s.scale=l,s.centerX=c,s.centerY=u):s.action=Ce}else{const[n]=Array.from(i.values());Object.assign(s,n)}i.forEach(n=>{n.startX=n.endX,n.startY=n.endY}),s.action!==Ce&&this.$emit(hi,s,{cancelable:!1})}$handlePointerUp(t){const{$action:e,$pointers:i}=this;if(!(this.disabled||e===Ce)&&this.$emit(Re,{action:e,relatedEvent:t})!==!1){if(t.preventDefault(),t.changedTouches)Array.from(t.changedTouches).forEach(({identifier:s})=>{i.delete(s)});else{const{pointerId:s=0}=t;i.delete(s)}i.size===0&&(this.style.willChange="",this.$action=Ce)}}$handleWheel(t){if(this.disabled||(t.preventDefault(),this.$wheeling))return;this.$wheeling=!0,setTimeout(()=>{this.$wheeling=!1},50);const i=(t.deltaY>0?-1:1)*this.scaleStep;this.$emit(hi,{action:oi,scale:i,relatedEvent:t},{cancelable:!1})}$setAction(t){return os(t)&&(this.$action=t),this}$toCanvas(t){return new Promise((e,i)=>{if(!this.isConnected){i(new Error("The current element is not connected to the DOM."));return}const s=document.createElement("canvas");let n=this.offsetWidth,o=this.offsetHeight,a=1;as(t)&&(Ct(t.width)||Ct(t.height))&&({width:n,height:o}=hs({aspectRatio:n/o,width:t.width,height:t.height}),a=n/this.offsetWidth),s.width=n,s.height=o;const h=this.querySelector(this.$getTagNameOf(ri));if(!h){e(s);return}h.$ready().then(l=>{const c=s.getContext("2d");if(c){const[u,d,g,f,p,v]=h.$getTransform();let b=p,w=v,x=l.naturalWidth,_=l.naturalHeight;a!==1&&(b*=a,w*=a,x*=a,_*=a);const k=x/2,A=_/2;c.fillStyle="transparent",c.fillRect(0,0,n,o),as(t)&&jn(t.beforeDraw)&&t.beforeDraw.call(this,c,s),c.save(),c.translate(k,A),c.transform(u,d,g,f,b,w),c.translate(-k,-A),c.drawImage(l,0,0,x,_),c.restore()}e(s)}).catch(i)})}}$n.$name=ni,$n.$version="2.0.0";var lc=":host{display:block;height:0;left:0;outline:var(--theme-color) solid 1px;position:relative;top:0;width:0}:host([transparent]){outline-color:transparent}";const Fo=new WeakMap;class zn extends he{constructor(){super(...arguments),this.$onCanvasChange=null,this.$onCanvasActionEnd=null,this.$onCanvasActionStart=null,this.$style=lc,this.x=0,this.y=0,this.width=0,this.height=0,this.slottable=!1,this.themeColor="rgba(0, 0, 0, 0.65)"}set $canvas(t){Fo.set(this,t)}get $canvas(){return Fo.get(this)}static get observedAttributes(){return super.observedAttributes.concat(["height","width","x","y"])}connectedCallback(){super.connectedCallback();const t=this.closest(this.$getTagNameOf(ni));if(t){this.$canvas=t,this.style.position="absolute";const e=t.querySelector(this.$getTagNameOf(Qt));e&&(this.$onCanvasActionStart=i=>{e.hidden&&i.detail.action===ns&&(this.hidden=!1)},this.$onCanvasActionEnd=i=>{e.hidden&&i.detail.action===ns&&(this.hidden=!0)},this.$onCanvasChange=i=>{const{x:s,y:n,width:o,height:a}=i.detail;this.$change(s,n,o,a),(e.hidden||s===0&&n===0&&o===0&&a===0)&&(this.hidden=!0)},ot(t,Ie,this.$onCanvasActionStart),ot(t,Re,this.$onCanvasActionEnd),ot(t,we,this.$onCanvasChange))}this.$render()}disconnectedCallback(){const{$canvas:t}=this;t&&(this.$onCanvasActionStart&&(st(t,Ie,this.$onCanvasActionStart),this.$onCanvasActionStart=null),this.$onCanvasActionEnd&&(st(t,Re,this.$onCanvasActionEnd),this.$onCanvasActionEnd=null),this.$onCanvasChange&&(st(t,we,this.$onCanvasChange),this.$onCanvasChange=null)),super.disconnectedCallback()}$change(t,e,i=this.width,s=this.height){return!$(t)||!$(e)||!$(i)||!$(s)||t===this.x&&e===this.y&&i===this.width&&s===this.height?this:(this.hidden&&(this.hidden=!1),this.x=t,this.y=e,this.width=i,this.height=s,this.$render())}$reset(){return this.$change(0,0,0,0)}$render(){return this.$setStyles({transform:`translate(${this.x}px, ${this.y}px)`,width:this.width,height:this.height,outlineWidth:dt.innerWidth})}}zn.$name=Vl,zn.$version="2.0.0";var cc=':host{background-color:var(--theme-color);display:block}:host([action=move]),:host([action=select]){height:100%;left:0;position:absolute;top:0;width:100%}:host([action=move]){cursor:move}:host([action=select]){cursor:crosshair}:host([action$=-resize]){background-color:transparent;height:15px;position:absolute;width:15px}:host([action$=-resize]):after{background-color:var(--theme-color);content:"";display:block;height:5px;left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:5px}:host([action=n-resize]),:host([action=s-resize]){cursor:ns-resize;left:50%;transform:translateX(-50%);width:100%}:host([action=n-resize]){top:-8px}:host([action=s-resize]){bottom:-8px}:host([action=e-resize]),:host([action=w-resize]){cursor:ew-resize;height:100%;top:50%;transform:translateY(-50%)}:host([action=e-resize]){right:-8px}:host([action=w-resize]){left:-8px}:host([action=ne-resize]){cursor:nesw-resize;right:-8px;top:-8px}:host([action=nw-resize]){cursor:nwse-resize;left:-8px;top:-8px}:host([action=se-resize]){bottom:-8px;cursor:nwse-resize;right:-8px}:host([action=se-resize]):after{height:15px;width:15px}@media (pointer:coarse){:host([action=se-resize]):after{height:10px;width:10px}}@media (pointer:fine){:host([action=se-resize]):after{height:5px;width:5px}}:host([action=sw-resize]){bottom:-8px;cursor:nesw-resize;left:-8px}:host([plain]){background-color:transparent}';class Hn extends he{constructor(){super(...arguments),this.$onCanvasCropEnd=null,this.$onCanvasCropStart=null,this.$style=cc,this.action=Ce,this.plain=!1,this.slottable=!1,this.themeColor="rgba(51, 153, 255, 0.5)"}static get observedAttributes(){return super.observedAttributes.concat(["action","plain"])}}Hn.$name=Wl,Hn.$version="2.0.0";var uc=':host{display:block;left:0;position:relative;right:0}:host([outlined]){outline:1px solid var(--theme-color)}:host([multiple]){outline:1px dashed hsla(0,0%,100%,.5)}:host([multiple]):after{bottom:0;content:"";cursor:pointer;display:block;left:0;position:absolute;right:0;top:0}:host([multiple][active]){outline-color:var(--theme-color);z-index:1}:host([multiple])>*{visibility:hidden}:host([multiple][active])>*{visibility:visible}:host([multiple][active]):after{display:none}';const $o=new WeakMap;class Nn extends he{constructor(){super(...arguments),this.$onCanvasAction=null,this.$onCanvasActionStart=null,this.$onCanvasActionEnd=null,this.$onDocumentKeyDown=null,this.$action="",this.$actionStartTarget=null,this.$changing=!1,this.$style=uc,this.$initialSelection={x:0,y:0,width:0,height:0},this.x=0,this.y=0,this.width=0,this.height=0,this.aspectRatio=NaN,this.initialAspectRatio=NaN,this.initialCoverage=NaN,this.active=!1,this.linked=!1,this.dynamic=!1,this.movable=!1,this.resizable=!1,this.zoomable=!1,this.multiple=!1,this.keyboard=!1,this.outlined=!1,this.precise=!1}set $canvas(t){$o.set(this,t)}get $canvas(){return $o.get(this)}static get observedAttributes(){return super.observedAttributes.concat(["active","aspect-ratio","dynamic","height","initial-aspect-ratio","initial-coverage","keyboard","linked","movable","multiple","outlined","precise","resizable","width","x","y","zoomable"])}$propertyChangedCallback(t,e,i){if(!Object.is(i,e))switch(super.$propertyChangedCallback(t,e,i),t){case"x":case"y":case"width":case"height":this.$changing||this.$nextTick(()=>{this.$change(this.x,this.y,this.width,this.height,this.aspectRatio,!0)});break;case"aspectRatio":case"initialAspectRatio":this.$nextTick(()=>{this.$initSelection()});break;case"initialCoverage":this.$nextTick(()=>{Ct(i)&&i<=1&&this.$initSelection(!0,!0)});break;case"keyboard":this.$nextTick(()=>{this.$canvas&&(i?this.$onDocumentKeyDown||(this.$onDocumentKeyDown=this.$handleKeyDown.bind(this),ot(this.ownerDocument,wo,this.$onDocumentKeyDown)):this.$onDocumentKeyDown&&(st(this.ownerDocument,wo,this.$onDocumentKeyDown),this.$onDocumentKeyDown=null))});break;case"multiple":this.$nextTick(()=>{if(this.$canvas){const s=this.$getSelections();i?(s.forEach(n=>{n.active=!1}),this.active=!0,this.$emit(we,{x:this.x,y:this.y,width:this.width,height:this.height})):(this.active=!1,s.slice(1).forEach(n=>{this.$removeSelection(n)}))}});break;case"precise":this.$nextTick(()=>{this.$change(this.x,this.y)});break;case"linked":i&&(this.dynamic=!0);break}}connectedCallback(){super.connectedCallback();const t=this.closest(this.$getTagNameOf(ni));t?(this.$canvas=t,this.$setStyles({position:"absolute",transform:`translate(${this.x}px, ${this.y}px)`}),this.hidden||this.$render(),this.$initSelection(!0),this.$onCanvasActionStart=this.$handleActionStart.bind(this),this.$onCanvasActionEnd=this.$handleActionEnd.bind(this),this.$onCanvasAction=this.$handleAction.bind(this),ot(t,Ie,this.$onCanvasActionStart),ot(t,Re,this.$onCanvasActionEnd),ot(t,hi,this.$onCanvasAction)):this.$render()}disconnectedCallback(){const{$canvas:t}=this;t&&(this.$onCanvasActionStart&&(st(t,Ie,this.$onCanvasActionStart),this.$onCanvasActionStart=null),this.$onCanvasActionEnd&&(st(t,Re,this.$onCanvasActionEnd),this.$onCanvasActionEnd=null),this.$onCanvasAction&&(st(t,hi,this.$onCanvasAction),this.$onCanvasAction=null)),super.disconnectedCallback()}$getSelections(){let t=[];return this.parentElement&&(t=Array.from(this.parentElement.querySelectorAll(this.$getTagNameOf(Qt)))),t}$initSelection(t=!1,e=!1){const{initialCoverage:i,parentElement:s}=this;if(Ct(i)&&s){const n=this.aspectRatio||this.initialAspectRatio;let o=(e?0:this.width)||s.offsetWidth*i,a=(e?0:this.height)||s.offsetHeight*i;Ct(n)&&({width:o,height:a}=hs({aspectRatio:n,width:o,height:a})),this.$change(this.x,this.y,o,a),t&&this.$center(),this.$initialSelection={x:this.x,y:this.y,width:this.width,height:this.height}}}$createSelection(){const t=this.cloneNode(!0);return this.hasAttribute("id")&&t.removeAttribute("id"),t.initialCoverage=NaN,this.active=!1,this.parentElement&&this.parentElement.insertBefore(t,this.nextSibling),t}$removeSelection(t=this){if(this.parentElement){const e=this.$getSelections();if(e.length>1){const i=e.indexOf(t),s=e[i+1]||e[i-1];s&&(t.active=!1,this.parentElement.removeChild(t),s.active=!0,s.$emit(we,{x:s.x,y:s.y,width:s.width,height:s.height}))}else this.$clear()}}$handleActionStart(t){var e,i;const s=(i=(e=t.detail)===null||e===void 0?void 0:e.relatedEvent)===null||i===void 0?void 0:i.target;this.$action="",this.$actionStartTarget=s,!this.hidden&&this.multiple&&!this.active&&s===this&&this.parentElement&&(this.$getSelections().forEach(n=>{n.active=!1}),this.active=!0,this.$emit(we,{x:this.x,y:this.y,width:this.width,height:this.height}))}$handleAction(t){const{currentTarget:e,detail:i}=t;if(!e||!i)return;const{relatedEvent:s}=i;let{action:n}=i;if(!n&&this.multiple&&(n=this.$action||(s==null?void 0:s.target.action),this.$action=n),!n||this.hidden&&n!==ns||this.multiple&&!this.active&&n!==oi)return;const o=i.endX-i.startX,a=i.endY-i.startY,{width:h,height:l}=this;let{aspectRatio:c}=this;switch(!Ct(c)&&s.shiftKey&&(c=Ct(h)&&Ct(l)?h/l:1),n){case ns:if(o!==0&&a!==0){const{$canvas:u}=this,d=Do(e);(this.multiple&&!this.hidden?this.$createSelection():this).$change(i.startX-d.left,i.startY-d.top,Math.abs(o),Math.abs(a),c),o<0?a<0?n=Ai:a>0&&(n=Li):o>0&&(a<0?n=Di:a>0&&(n=Pi)),u&&(u.$action=n)}break;case uo:this.movable&&(this.dynamic||this.$actionStartTarget&&this.contains(this.$actionStartTarget))&&this.$move(o,a);break;case oi:if(s&&this.zoomable&&(this.dynamic||this.contains(s.target))){const u=Do(e);this.$zoom(i.scale,s.pageX-u.left,s.pageY-u.top)}break;default:this.$resize(n,o,a,c)}}$handleActionEnd(){this.$action="",this.$actionStartTarget=null}$handleKeyDown(t){if(this.hidden||!this.keyboard||this.multiple&&!this.active||t.defaultPrevented)return;const{activeElement:e}=document;if(!(e&&(["INPUT","TEXTAREA"].includes(e.tagName)||["true","plaintext-only"].includes(e.contentEditable))))switch(t.key){case"Backspace":t.metaKey&&(t.preventDefault(),this.$removeSelection());break;case"Delete":t.preventDefault(),this.$removeSelection();break;case"ArrowLeft":t.preventDefault(),this.$move(-1,0);break;case"ArrowRight":t.preventDefault(),this.$move(1,0);break;case"ArrowUp":t.preventDefault(),this.$move(0,-1);break;case"ArrowDown":t.preventDefault(),this.$move(0,1);break;case"+":t.preventDefault(),this.$zoom(.1);break;case"-":t.preventDefault(),this.$zoom(-.1);break}}$center(){const{parentElement:t}=this;if(!t)return this;const e=(t.offsetWidth-this.width)/2,i=(t.offsetHeight-this.height)/2;return this.$change(e,i)}$move(t,e=t){return this.$moveTo(this.x+t,this.y+e)}$moveTo(t,e=t){return this.movable?this.$change(t,e):this}$resize(t,e=0,i=0,s=this.aspectRatio){if(!this.resizable)return this;const n=Ct(s),{$canvas:o}=this;let{x:a,y:h,width:l,height:c}=this;switch(t){case go:h+=i,c-=i,c<0&&(t=po,c=-c,h-=c),n&&(e=i*s,a+=e/2,l-=e,l<0&&(l=-l,a-=l));break;case fo:l+=e,l<0&&(t=mo,l=-l,a-=l),n&&(i=e/s,h-=i/2,c+=i,c<0&&(c=-c,h-=c));break;case po:c+=i,c<0&&(t=go,c=-c,h-=c),n&&(e=i*s,a-=e/2,l+=e,l<0&&(l=-l,a-=l));break;case mo:a+=e,l-=e,l<0&&(t=fo,l=-l,a-=l),n&&(i=e/s,h+=i/2,c-=i,c<0&&(c=-c,h-=c));break;case Di:n&&(i=-e/s),h+=i,c-=i,l+=e,l<0&&c<0?(t=Li,l=-l,c=-c,a-=l,h-=c):l<0?(t=Ai,l=-l,a-=l):c<0&&(t=Pi,c=-c,h-=c);break;case Ai:n&&(i=e/s),a+=e,h+=i,l-=e,c-=i,l<0&&c<0?(t=Pi,l=-l,c=-c,a-=l,h-=c):l<0?(t=Di,l=-l,a-=l):c<0&&(t=Li,c=-c,h-=c);break;case Pi:n&&(i=e/s),l+=e,c+=i,l<0&&c<0?(t=Ai,l=-l,c=-c,a-=l,h-=c):l<0?(t=Li,l=-l,a-=l):c<0&&(t=Di,c=-c,h-=c);break;case Li:n&&(i=-e/s),a+=e,l-=e,c+=i,l<0&&c<0?(t=Di,l=-l,c=-c,a-=l,h-=c):l<0?(t=Pi,l=-l,a-=l):c<0&&(t=Ai,c=-c,h-=c);break}return o&&o.$setAction(t),this.$change(a,h,l,c)}$zoom(t,e,i){if(!this.zoomable||t===0)return this;t<0?t=1/(1-t):t+=1;const{width:s,height:n}=this,o=s*t,a=n*t;let h=this.x,l=this.y;return $(e)&&$(i)?(h-=(o-s)*((e-this.x)/s),l-=(a-n)*((i-this.y)/n)):(h-=(o-s)/2,l-=(a-n)/2),this.$change(h,l,o,a)}$change(t,e,i=this.width,s=this.height,n=this.aspectRatio,o=!1){return this.$changing||!$(t)||!$(e)||!$(i)||!$(s)||i<0||s<0?this:(Ct(n)&&({width:i,height:s}=hs({aspectRatio:n,width:i,height:s},"cover")),this.precise||(t=Math.round(t),e=Math.round(e),i=Math.round(i),s=Math.round(s)),t===this.x&&e===this.y&&i===this.width&&s===this.height&&Object.is(n,this.aspectRatio)&&!o?this:(this.hidden&&(this.hidden=!1),this.$emit(we,{x:t,y:e,width:i,height:s})===!1?this:(this.$changing=!0,this.x=t,this.y=e,this.width=i,this.height=s,this.$changing=!1,this.$render())))}$reset(){const{x:t,y:e,width:i,height:s}=this.$initialSelection;return this.$change(t,e,i,s)}$clear(){return this.$change(0,0,0,0,NaN,!0),this.hidden=!0,this}$render(){return this.$setStyles({transform:`translate(${this.x}px, ${this.y}px)`,width:this.width,height:this.height})}$toCanvas(t){return new Promise((e,i)=>{if(!this.isConnected){i(new Error("The current element is not connected to the DOM."));return}const s=document.createElement("canvas");let{width:n,height:o}=this,a=1;if(as(t)&&(Ct(t.width)||Ct(t.height))&&({width:n,height:o}=hs({aspectRatio:n/o,width:t.width,height:t.height}),a=n/this.width),s.width=n,s.height=o,!this.$canvas){e(s);return}const h=this.$canvas.querySelector(this.$getTagNameOf(ri));if(!h){e(s);return}h.$ready().then(l=>{const c=s.getContext("2d");if(c){const[u,d,g,f,p,v]=h.$getTransform(),b=-this.x,w=-this.y,x=(b*f-g*w)/(u*f-g*d),_=(w*u-d*b)/(u*f-g*d);let k=u*x+g*_+p,A=d*x+f*_+v,L=l.naturalWidth,O=l.naturalHeight;a!==1&&(k*=a,A*=a,L*=a,O*=a);const I=L/2,F=O/2;c.fillStyle="transparent",c.fillRect(0,0,n,o),as(t)&&jn(t.beforeDraw)&&t.beforeDraw.call(this,c,s),c.save(),c.translate(I,F),c.transform(u,d,g,f,k,A),c.translate(-I,-F),c.drawImage(l,0,0,L,O),c.restore()}e(s)}).catch(i)})}}Nn.$name=Qt,Nn.$version="2.0.0";var dc=":host{display:flex;flex-direction:column;position:relative;touch-action:none;-webkit-user-select:none;-moz-user-select:none;user-select:none}:host([bordered]){border:1px dashed var(--theme-color)}:host([covered]){bottom:0;left:0;position:absolute;right:0;top:0}:host>span{display:flex;flex:1}:host>span+span{border-top:1px dashed var(--theme-color)}:host>span>span{flex:1}:host>span>span+span{border-left:1px dashed var(--theme-color)}";class Wn extends he{constructor(){super(...arguments),this.$style=dc,this.bordered=!1,this.columns=3,this.covered=!1,this.rows=3,this.slottable=!1,this.themeColor="rgba(238, 238, 238, 0.5)"}static get observedAttributes(){return super.observedAttributes.concat(["bordered","columns","covered","rows"])}$propertyChangedCallback(t,e,i){Object.is(i,e)||(super.$propertyChangedCallback(t,e,i),(t==="rows"||t==="columns")&&this.$nextTick(()=>{this.$render()}))}connectedCallback(){super.connectedCallback(),this.$render()}$render(){const t=this.$getShadowRoot(),e=document.createDocumentFragment();for(let i=0;i<this.rows;i+=1){const s=document.createElement("span");s.setAttribute("role","row");for(let n=0;n<this.columns;n+=1){const o=document.createElement("span");o.setAttribute("role","gridcell"),s.appendChild(o)}e.appendChild(s)}t&&(t.innerHTML="",t.appendChild(e))}}Wn.$name=Nl,Wn.$version="2.0.0";var gc=':host{display:inline-block;height:1em;position:relative;touch-action:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;vertical-align:middle;width:1em}:host:after,:host:before{background-color:var(--theme-color);content:"";display:block;position:absolute}:host:before{height:1px;left:0;top:50%;transform:translateY(-50%);width:100%}:host:after{height:100%;left:50%;top:0;transform:translateX(-50%);width:1px}:host([centered]){left:50%;position:absolute;top:50%;transform:translate(-50%,-50%)}';class Vn extends he{constructor(){super(...arguments),this.$style=gc,this.centered=!1,this.slottable=!1,this.themeColor="rgba(238, 238, 238, 0.5)"}static get observedAttributes(){return super.observedAttributes.concat(["centered"])}}Vn.$name=Hl,Vn.$version="2.0.0";var fc=":host{display:block;height:100%;overflow:hidden;position:relative;width:100%}";const zo=new WeakMap,Ho=new WeakMap,No=new WeakMap,Wo=new WeakMap,pc="both",mc="horizontal",Vo="vertical",vc="none";class Xn extends he{constructor(){super(...arguments),this.$onSelectionChange=null,this.$onSourceImageLoad=null,this.$onSourceImageTransform=null,this.$scale=1,this.$style=fc,this.resize=Vo,this.selection="",this.slottable=!1}set $image(t){Ho.set(this,t)}get $image(){return Ho.get(this)}set $sourceImage(t){Wo.set(this,t)}get $sourceImage(){return Wo.get(this)}set $canvas(t){zo.set(this,t)}get $canvas(){return zo.get(this)}set $selection(t){No.set(this,t)}get $selection(){return No.get(this)}static get observedAttributes(){return super.observedAttributes.concat(["resize","selection"])}connectedCallback(){super.connectedCallback();let t=null;if(this.selection?t=this.ownerDocument.querySelector(this.selection):t=this.closest(this.$getTagNameOf(Qt)),Ri(t)){this.$selection=t,this.$onSelectionChange=this.$handleSelectionChange.bind(this),ot(t,we,this.$onSelectionChange);const e=t.closest(this.$getTagNameOf(ni));if(e){this.$canvas=e;const i=e.querySelector(this.$getTagNameOf(ri));i&&(this.$sourceImage=i,this.$image=i.cloneNode(!0),this.$getShadowRoot().appendChild(this.$image),this.$onSourceImageLoad=this.$handleSourceImageLoad.bind(this),this.$onSourceImageTransform=this.$handleSourceImageTransform.bind(this),ot(i.$image,ai,this.$onSourceImageLoad),ot(i,In,this.$onSourceImageTransform))}this.$render()}}disconnectedCallback(){const{$selection:t,$sourceImage:e}=this;t&&this.$onSelectionChange&&(st(t,we,this.$onSelectionChange),this.$onSelectionChange=null),e&&this.$onSourceImageLoad&&(st(e.$image,ai,this.$onSourceImageLoad),this.$onSourceImageLoad=null),e&&this.$onSourceImageTransform&&(st(e,In,this.$onSourceImageTransform),this.$onSourceImageTransform=null),super.disconnectedCallback()}$handleSelectionChange(t){this.$render(t.detail)}$handleSourceImageLoad(){const{$image:t,$sourceImage:e}=this,i=t.getAttribute("src"),s=e.getAttribute("src");s&&s!==i&&(t.setAttribute("src",s),t.$ready(()=>{setTimeout(()=>{this.$render()},50)}))}$handleSourceImageTransform(t){this.$render(void 0,t.detail.matrix)}$render(t,e){const{$canvas:i,$selection:s}=this;!t&&!s.hidden&&(t=s),(!t||t.x===0&&t.y===0&&t.width===0&&t.height===0)&&(t={x:0,y:0,width:i.offsetWidth,height:i.offsetHeight});const{x:n,y:o,width:a,height:h}=t,l={},{clientWidth:c,clientHeight:u}=this;let d=c,g=u,f=NaN;switch(this.resize){case pc:f=1,d=a,g=h,l.width=a,l.height=h;break;case mc:f=h>0?u/h:0,d=a*f,l.width=d;break;case Vo:f=a>0?c/a:0,g=h*f,l.height=g;break;case vc:default:c>0?f=a>0?c/a:0:u>0&&(f=h>0?u/h:0)}this.$scale=f,this.$setStyles(l),this.$sourceImage&&this.$transformImageByOffset(e??this.$sourceImage.$getTransform(),-n,-o)}$transformImageByOffset(t,e,i){const{$image:s,$scale:n,$sourceImage:o}=this;if(o&&s&&n>=0){const[a,h,l,c,u,d]=t,g=(e*c-l*i)/(a*c-l*h),f=(i*a-h*e)/(a*c-l*h),p=a*g+l*f+u,v=h*g+c*f+d;s.$ready(b=>{this.$setStyles.call(s,{width:b.naturalWidth*n,height:b.naturalHeight*n})}),s.$setTransform(a,h,l,c,p*n,v*n)}}}Xn.$name=Xl,Xn.$version="2.0.0";/*! Cropper.js v2.0.0 | (c) 2015-present Chen Fengyuan | MIT */var yc='<cropper-canvas background><cropper-image rotatable scalable skewable translatable></cropper-image><cropper-shade hidden></cropper-shade><cropper-handle action="select" plain></cropper-handle><cropper-selection initial-coverage="0.5" movable resizable><cropper-grid role="grid" bordered covered></cropper-grid><cropper-crosshair centered></cropper-crosshair><cropper-handle action="move" theme-color="rgba(255, 255, 255, 0.35)"></cropper-handle><cropper-handle action="n-resize"></cropper-handle><cropper-handle action="e-resize"></cropper-handle><cropper-handle action="s-resize"></cropper-handle><cropper-handle action="w-resize"></cropper-handle><cropper-handle action="ne-resize"></cropper-handle><cropper-handle action="nw-resize"></cropper-handle><cropper-handle action="se-resize"></cropper-handle><cropper-handle action="sw-resize"></cropper-handle></cropper-selection></cropper-canvas>';const bc=/^img|canvas$/,Cc=/<(\/?(?:script|style)[^>]*)>/gi,Xo={template:yc};$n.$define(),Vn.$define(),Wn.$define(),Hn.$define(),Fn.$define(),Nn.$define(),zn.$define(),Xn.$define();class Yo{constructor(t,e){if(this.options=Xo,os(t)&&(t=document.querySelector(t)),!Ri(t)||!bc.test(t.localName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=t,e=Object.assign(Object.assign({},Xo),e),this.options=e;const{ownerDocument:i}=t;let{container:s}=e;if(s&&(os(s)&&(s=i.querySelector(s)),!Ri(s)))throw new Error("The `container` option must be an element or a valid selector.");Ri(s)||(t.parentElement?s=t.parentElement:s=i.body),this.container=s;const n=t.localName;let o="";n==="img"?{src:o}=t:n==="canvas"&&window.HTMLCanvasElement&&(o=t.toDataURL());const{template:a}=e;if(a&&os(a)){const h=document.createElement("template"),l=document.createDocumentFragment();h.innerHTML=a.replace(Cc,"&lt;$1&gt;"),l.appendChild(h.content),Array.from(l.querySelectorAll(ri)).forEach(c=>{c.setAttribute("src",o),c.setAttribute("alt",t.alt||"The image to crop")}),t.parentElement?(t.style.display="none",s.insertBefore(l,t.nextSibling)):s.appendChild(l)}}getCropperCanvas(){return this.container.querySelector(ni)}getCropperImage(){return this.container.querySelector(ri)}getCropperSelection(){return this.container.querySelector(Qt)}getCropperSelections(){return this.container.querySelectorAll(Qt)}}Yo.version="2.0.0";const wc="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='iso-8859-1'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20fill='%23000000'%20version='1.1'%20id='Capa_1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='800px'%20height='800px'%20viewBox='0%200%20335.765%20335.765'%20xml:space='preserve'%3e%3cg%3e%3cg%3e%3cpolygon%20points='311.757,41.803%20107.573,245.96%2023.986,162.364%200,186.393%20107.573,293.962%20335.765,65.795%20'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e",xc="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20aria-hidden='true'%20role='img'%20class='iconify%20iconify--logos'%20width='31.88'%20height='32'%20preserveAspectRatio='xMidYMid%20meet'%20viewBox='0%200%20256%20257'%3e%3cdefs%3e%3clinearGradient%20id='IconifyId1813088fe1fbc01fb466'%20x1='-.828%25'%20x2='57.636%25'%20y1='7.652%25'%20y2='78.411%25'%3e%3cstop%20offset='0%25'%20stop-color='%2341D1FF'%3e%3c/stop%3e%3cstop%20offset='100%25'%20stop-color='%23BD34FE'%3e%3c/stop%3e%3c/linearGradient%3e%3clinearGradient%20id='IconifyId1813088fe1fbc01fb467'%20x1='43.376%25'%20x2='50.316%25'%20y1='2.242%25'%20y2='89.03%25'%3e%3cstop%20offset='0%25'%20stop-color='%23FFEA83'%3e%3c/stop%3e%3cstop%20offset='8.333%25'%20stop-color='%23FFDD35'%3e%3c/stop%3e%3cstop%20offset='100%25'%20stop-color='%23FFA800'%3e%3c/stop%3e%3c/linearGradient%3e%3c/defs%3e%3cpath%20fill='url(%23IconifyId1813088fe1fbc01fb466)'%20d='M255.153%2037.938L134.897%20252.976c-2.483%204.44-8.862%204.466-11.382.048L.875%2037.958c-2.746-4.814%201.371-10.646%206.827-9.67l120.385%2021.517a6.537%206.537%200%200%200%202.322-.004l117.867-21.483c5.438-.991%209.574%204.796%206.877%209.62Z'%3e%3c/path%3e%3cpath%20fill='url(%23IconifyId1813088fe1fbc01fb467)'%20d='M185.432.063L96.44%2017.501a3.268%203.268%200%200%200-2.634%203.014l-5.474%2092.456a3.268%203.268%200%200%200%203.997%203.378l24.777-5.718c2.318-.535%204.413%201.507%203.936%203.838l-7.361%2036.047c-.495%202.426%201.782%204.5%204.151%203.78l15.304-4.649c2.372-.72%204.652%201.36%204.15%203.788l-11.698%2056.621c-.732%203.542%203.979%205.473%205.943%202.437l1.313-2.028l72.516-144.72c1.215-2.423-.88-5.186-3.54-4.672l-25.505%204.922c-2.396.462-4.435-1.77-3.759-4.114l16.646-57.705c.677-2.35-1.37-4.583-3.769-4.113Z'%3e%3c/path%3e%3c/svg%3e",_c="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20width='800px'%20height='800px'%20viewBox='0%200%201024%201024'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill='%23000000'%20d='M224%20480h640a32%2032%200%201%201%200%2064H224a32%2032%200%200%201%200-64z'/%3e%3cpath%20fill='%23000000'%20d='m237.248%20512%20265.408%20265.344a32%2032%200%200%201-45.312%2045.312l-288-288a32%2032%200%200%201%200-45.312l288-288a32%2032%200%201%201%2045.312%2045.312L237.248%20512z'/%3e%3c/svg%3e";let Sc=Symbol("clean"),Wt=[],xe=0;const us=4;let Ii=r=>{let t=[],e={get(){return e.lc||e.listen(()=>{})(),e.value},lc:0,listen(i){return e.lc=t.push(i),()=>{for(let n=xe+us;n<Wt.length;)Wt[n]===i?Wt.splice(n,us):n+=us;let s=t.indexOf(i);~s&&(t.splice(s,1),--e.lc||e.off())}},notify(i,s){let n=!Wt.length;for(let o of t)Wt.push(o,e.value,i,s);if(n){for(xe=0;xe<Wt.length;xe+=us)Wt[xe](Wt[xe+1],Wt[xe+2],Wt[xe+3]);Wt.length=0}},off(){},set(i){let s=e.value;s!==i&&(e.value=i,e.notify(s))},subscribe(i){let s=e.listen(i);return i(e.value),s},value:r};return process.env.NODE_ENV!=="production"&&(e[Sc]=()=>{t=[],e.lc=0,e.off()}),e};function ds(r){return new Promise((t,e)=>{r.oncomplete=r.onsuccess=()=>t(r.result),r.onabort=r.onerror=()=>e(r.error)})}function Tc(r,t){let e;const i=()=>{if(e)return e;const s=indexedDB.open(r);return s.onupgradeneeded=()=>s.result.createObjectStore(t),e=ds(s),e.then(n=>{n.onclose=()=>e=void 0},()=>{}),e};return(s,n)=>i().then(o=>n(o.transaction(t,s).objectStore(t)))}let Yn;function Gn(){return Yn||(Yn=Tc("keyval-store","keyval")),Yn}function Ec(r,t=Gn()){return t("readonly",e=>ds(e.get(r)))}function Go(r,t,e=Gn()){return e("readwrite",i=>(i.put(t,r),ds(i.transaction)))}function Uo(r,t=Gn()){return t("readwrite",e=>(e.delete(r),ds(e.transaction)))}const kt=Ii("home"),kc=Ii(""),Un=Ii("Rectangle");var qo=(r=>(r.home="home",r.crop="crop",r.annotate="annotate",r))(qo||{});function Ko(){kt.set("crop")}function Zo(){kt.set("annotate")}function le(){kt.set("home")}function Oc(){kt.get()}const qn=Ii(null),Kn=Ii(null);async function Mc(r){const t="original-image";await Go(t,r),qn.set(t)}async function Dc(r){const t="modified-image";await Go(t,r),Kn.set(t)}async function Ac(r){return await Ec(r)}async function gs(){await Uo("original-image"),await Uo("modified-image"),qn.set(null),Kn.set(null)}const Pc="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='iso-8859-1'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20height='800px'%20width='800px'%20version='1.1'%20id='Layer_1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20viewBox='0%200%20512%20512'%20xml:space='preserve'%3e%3cpath%20style='fill:%23FF3F62;'%20d='M437.02,74.98C388.668,26.629,324.38,0,256,0S123.333,26.629,74.981,74.98%20c-99.814,99.814-99.815,262.223-0.002,362.038C123.331,485.371,187.62,512,256,512s132.668-26.629,181.02-74.982%20C536.833,337.203,536.833,174.794,437.02,74.98z'/%3e%3cpath%20id='SVGCleanerId_0'%20style='fill:%23830018;'%20d='M371.193,338.28L288.912,256l82.28-82.281c9.089-9.087,9.089-23.824,0-32.912%20c-9.087-9.089-23.824-9.087-32.912,0L256,223.088l-82.282-82.282c-9.087-9.089-23.824-9.089-32.912,0%20c-9.089,9.087-9.089,23.824,0,32.912L223.087,256l-82.282,82.282c-9.089,9.087-9.089,23.824,0,32.912%20c4.544,4.546,10.501,6.817,16.455,6.817c5.955,0,11.913-2.273,16.455-6.817L256,288.912l82.281,82.28%20c4.544,4.544,10.501,6.817,16.455,6.817s11.913-2.273,16.455-6.817C380.282,362.105,380.282,347.369,371.193,338.28z'/%3e%3cpath%20style='fill:%23FF3F62;'%20d='M256,0v223.088l82.28-82.282c9.087-9.087,23.824-9.089,32.912,0c9.089,9.087,9.089,23.824,0,32.912%20L288.912,256l82.281,82.28c9.089,9.087,9.089,23.824,0,32.912c-4.544,4.544-10.501,6.817-16.457,6.817%20c-5.956,0-11.913-2.273-16.455-6.817L256,288.912V512c68.38,0,132.668-26.629,181.02-74.982c99.814-99.815,99.814-262.225,0-362.038%20C388.668,26.629,324.38,0,256,0z'/%3e%3cpath%20id='SVGCleanerId_1'%20style='fill:%23FF0C38;'%20d='M173.718,371.194c-4.544,4.544-10.501,6.817-16.455,6.817%20c-5.956,0-11.913-2.271-16.457-6.817c-9.089-9.087-9.089-23.824,0-32.912L223.087,256l-82.282-82.281%20c-9.089-9.087-9.089-23.824,0-32.912c9.087-9.089,23.825-9.089,32.912,0L256,223.088V0C187.62,0,123.333,26.629,74.981,74.98%20c-99.814,99.814-99.815,262.223-0.002,362.038C123.331,485.371,187.62,512,256,512V288.912L173.718,371.194z'/%3e%3cg%3e%3cpath%20id='SVGCleanerId_0_1_'%20style='fill:%23830018;'%20d='M371.193,338.28L288.912,256l82.28-82.281%20c9.089-9.087,9.089-23.824,0-32.912c-9.087-9.089-23.824-9.087-32.912,0L256,223.088l-82.282-82.282%20c-9.087-9.089-23.824-9.089-32.912,0c-9.089,9.087-9.089,23.824,0,32.912L223.087,256l-82.282,82.282%20c-9.089,9.087-9.089,23.824,0,32.912c4.544,4.546,10.501,6.817,16.455,6.817c5.955,0,11.913-2.273,16.455-6.817L256,288.912%20l82.281,82.28c4.544,4.544,10.501,6.817,16.455,6.817s11.913-2.273,16.455-6.817C380.282,362.105,380.282,347.369,371.193,338.28z'%20/%3e%3c/g%3e%3cg%3e%3cpath%20id='SVGCleanerId_1_1_'%20style='fill:%23FF0C38;'%20d='M173.718,371.194c-4.544,4.544-10.501,6.817-16.455,6.817%20c-5.956,0-11.913-2.271-16.457-6.817c-9.089-9.087-9.089-23.824,0-32.912L223.087,256l-82.282-82.281%20c-9.089-9.087-9.089-23.824,0-32.912c9.087-9.089,23.825-9.089,32.912,0L256,223.088V0C187.62,0,123.333,26.629,74.981,74.98%20c-99.814,99.814-99.815,262.223-0.002,362.038C123.331,485.371,187.62,512,256,512V288.912L173.718,371.194z'/%3e%3c/g%3e%3c/svg%3e";function m(r,t,e){return(t=function(i){var s=function(n,o){if(typeof n!="object"||!n)return n;var a=n[Symbol.toPrimitive];if(a!==void 0){var h=a.call(n,o);if(typeof h!="object")return h;throw new TypeError("@@toPrimitive must return a primitive value.")}return(o==="string"?String:Number)(n)}(i,"string");return typeof s=="symbol"?s:s+""}(t))in r?Object.defineProperty(r,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):r[t]=e,r}function Jo(r,t){var e=Object.keys(r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(r);t&&(i=i.filter(function(s){return Object.getOwnPropertyDescriptor(r,s).enumerable})),e.push.apply(e,i)}return e}function y(r){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Jo(Object(e),!0).forEach(function(i){m(r,i,e[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(e)):Jo(Object(e)).forEach(function(i){Object.defineProperty(r,i,Object.getOwnPropertyDescriptor(e,i))})}return r}function U(r,t){if(r==null)return{};var e,i,s=function(o,a){if(o==null)return{};var h={};for(var l in o)if({}.hasOwnProperty.call(o,l)){if(a.indexOf(l)>=0)continue;h[l]=o[l]}return h}(r,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);for(i=0;i<n.length;i++)e=n[i],t.indexOf(e)>=0||{}.propertyIsEnumerable.call(r,e)&&(s[e]=r[e])}return s}function _e(r,t){return t||(t=r.slice(0)),Object.freeze(Object.defineProperties(r,{raw:{value:Object.freeze(t)}}))}let Qo=class{constructor(){m(this,"browserShadowBlurConstant",1),m(this,"DPI",96),m(this,"devicePixelRatio",typeof window<"u"?window.devicePixelRatio:1),m(this,"perfLimitSizeTotal",2097152),m(this,"maxCacheSideLimit",4096),m(this,"minCacheSideLimit",256),m(this,"disableStyleCopyPaste",!1),m(this,"enableGLFiltering",!0),m(this,"textureSize",4096),m(this,"forceGLPutImageData",!1),m(this,"cachesBoundsOfCurve",!1),m(this,"fontPaths",{}),m(this,"NUM_FRACTION_DIGITS",4)}};const W=new class extends Qo{constructor(r){super(),this.configure(r)}configure(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Object.assign(this,r)}addFonts(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.fontPaths=y(y({},this.fontPaths),r)}removeFonts(){(arguments.length>0&&arguments[0]!==void 0?arguments[0]:[]).forEach(r=>{delete this.fontPaths[r]})}clearFonts(){this.fontPaths={}}restoreDefaults(r){const t=new Qo,e=(r==null?void 0:r.reduce((i,s)=>(i[s]=t[s],i),{}))||t;this.configure(e)}},Se=function(r){for(var t=arguments.length,e=new Array(t>1?t-1:0),i=1;i<t;i++)e[i-1]=arguments[i];return console[r]("fabric",...e)};let te=class extends Error{constructor(t,e){super("fabric: ".concat(t),e)}},ta=class extends te{constructor(t){super("".concat(t," 'options.signal' is in 'aborted' state"))}};class Lc{}let Rc=class extends Lc{testPrecision(t,e){const i="precision ".concat(e,` float;
void main(){}`),s=t.createShader(t.FRAGMENT_SHADER);return!!s&&(t.shaderSource(s,i),t.compileShader(s),!!t.getShaderParameter(s,t.COMPILE_STATUS))}queryWebGL(t){const e=t.getContext("webgl");e&&(this.maxTextureSize=e.getParameter(e.MAX_TEXTURE_SIZE),this.GLPrecision=["highp","mediump","lowp"].find(i=>this.testPrecision(e,i)),e.getExtension("WEBGL_lose_context").loseContext(),Se("log","WebGL: max texture size ".concat(this.maxTextureSize)))}isSupported(t){return!!this.maxTextureSize&&this.maxTextureSize>=t}};const Ic={};let ea;const ee=()=>ea||(ea={document,window,isTouchSupported:"ontouchstart"in window||"ontouchstart"in document||window&&window.navigator&&window.navigator.maxTouchPoints>0,WebGLProbe:new Rc,dispose(){},copyPasteData:Ic}),li=()=>ee().document,fs=()=>ee().window,ia=()=>{var r;return Math.max((r=W.devicePixelRatio)!==null&&r!==void 0?r:fs().devicePixelRatio,1)},ji=new class{constructor(){m(this,"charWidthsCache",{}),m(this,"boundsOfCurveCache",{})}getFontCache(r){let{fontFamily:t,fontStyle:e,fontWeight:i}=r;t=t.toLowerCase(),this.charWidthsCache[t]||(this.charWidthsCache[t]={});const s=this.charWidthsCache[t],n="".concat(e.toLowerCase(),"_").concat((i+"").toLowerCase());return s[n]||(s[n]={}),s[n]}clearFontCache(r){(r=(r||"").toLowerCase())?this.charWidthsCache[r]&&delete this.charWidthsCache[r]:this.charWidthsCache={}}limitDimsByArea(r){const{perfLimitSizeTotal:t}=W,e=Math.sqrt(t*r);return[Math.floor(e),Math.floor(t/e)]}},Zn="6.6.4";function je(){}const Te=Math.PI/2,ce=2*Math.PI,Jn=Math.PI/180,pt=Object.freeze([1,0,0,1,0,0]),Qn=16,Ee=.4477152502,j="center",V="left",wt="top",tr="bottom",it="right",xt="none",er=/\r?\n/,sa="moving",ps="scaling",na="rotating",ir="rotate",ra="skewing",Bi="resizing",jc="modifyPoly",Bc="modifyPath",ms="changed",vs="scale",mt="scaleX",Ot="scaleY",ci="skewX",ui="skewY",at="fill",_t="stroke",ys="modified",di="json",sr="svg",D=new class{constructor(){this[di]=new Map,this[sr]=new Map}has(r){return this[di].has(r)}getClass(r){const t=this[di].get(r);if(!t)throw new te("No class registered for ".concat(r));return t}setClass(r,t){t?this[di].set(t,r):(this[di].set(r.type,r),this[di].set(r.type.toLowerCase(),r))}getSVGClass(r){return this[sr].get(r)}setSVGClass(r,t){this[sr].set(t??r.type.toLowerCase(),r)}},bs=new class extends Array{remove(r){const t=this.indexOf(r);t>-1&&this.splice(t,1)}cancelAll(){const r=this.splice(0);return r.forEach(t=>t.abort()),r}cancelByCanvas(r){if(!r)return[];const t=this.filter(e=>{var i;return e.target===r||typeof e.target=="object"&&((i=e.target)===null||i===void 0?void 0:i.canvas)===r});return t.forEach(e=>e.abort()),t}cancelByTarget(r){if(!r)return[];const t=this.filter(e=>e.target===r);return t.forEach(e=>e.abort()),t}};class Fc{constructor(){m(this,"__eventListeners",{})}on(t,e){if(this.__eventListeners||(this.__eventListeners={}),typeof t=="object")return Object.entries(t).forEach(i=>{let[s,n]=i;this.on(s,n)}),()=>this.off(t);if(e){const i=t;return this.__eventListeners[i]||(this.__eventListeners[i]=[]),this.__eventListeners[i].push(e),()=>this.off(i,e)}return()=>!1}once(t,e){if(typeof t=="object"){const i=[];return Object.entries(t).forEach(s=>{let[n,o]=s;i.push(this.once(n,o))}),()=>i.forEach(s=>s())}if(e){const i=this.on(t,function(){for(var s=arguments.length,n=new Array(s),o=0;o<s;o++)n[o]=arguments[o];e.call(this,...n),i()});return i}return()=>!1}_removeEventListener(t,e){if(this.__eventListeners[t])if(e){const i=this.__eventListeners[t],s=i.indexOf(e);s>-1&&i.splice(s,1)}else this.__eventListeners[t]=[]}off(t,e){if(this.__eventListeners)if(t===void 0)for(const i in this.__eventListeners)this._removeEventListener(i);else typeof t=="object"?Object.entries(t).forEach(i=>{let[s,n]=i;this._removeEventListener(s,n)}):this._removeEventListener(t,e)}fire(t,e){var i;if(!this.__eventListeners)return;const s=(i=this.__eventListeners[t])===null||i===void 0?void 0:i.concat();if(s)for(let n=0;n<s.length;n++)s[n].call(this,e||{})}}const Be=(r,t)=>{const e=r.indexOf(t);return e!==-1&&r.splice(e,1),r},Vt=r=>{if(r===0)return 1;switch(Math.abs(r)/Te){case 1:case 3:return 0;case 2:return-1}return Math.cos(r)},Xt=r=>{if(r===0)return 0;const t=r/Te,e=Math.sign(r);switch(t){case 1:return e;case 2:return 0;case 3:return-e}return Math.sin(r)};class C{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;typeof t=="object"?(this.x=t.x,this.y=t.y):(this.x=t,this.y=e)}add(t){return new C(this.x+t.x,this.y+t.y)}addEquals(t){return this.x+=t.x,this.y+=t.y,this}scalarAdd(t){return new C(this.x+t,this.y+t)}scalarAddEquals(t){return this.x+=t,this.y+=t,this}subtract(t){return new C(this.x-t.x,this.y-t.y)}subtractEquals(t){return this.x-=t.x,this.y-=t.y,this}scalarSubtract(t){return new C(this.x-t,this.y-t)}scalarSubtractEquals(t){return this.x-=t,this.y-=t,this}multiply(t){return new C(this.x*t.x,this.y*t.y)}scalarMultiply(t){return new C(this.x*t,this.y*t)}scalarMultiplyEquals(t){return this.x*=t,this.y*=t,this}divide(t){return new C(this.x/t.x,this.y/t.y)}scalarDivide(t){return new C(this.x/t,this.y/t)}scalarDivideEquals(t){return this.x/=t,this.y/=t,this}eq(t){return this.x===t.x&&this.y===t.y}lt(t){return this.x<t.x&&this.y<t.y}lte(t){return this.x<=t.x&&this.y<=t.y}gt(t){return this.x>t.x&&this.y>t.y}gte(t){return this.x>=t.x&&this.y>=t.y}lerp(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:.5;return e=Math.max(Math.min(1,e),0),new C(this.x+(t.x-this.x)*e,this.y+(t.y-this.y)*e)}distanceFrom(t){const e=this.x-t.x,i=this.y-t.y;return Math.sqrt(e*e+i*i)}midPointFrom(t){return this.lerp(t)}min(t){return new C(Math.min(this.x,t.x),Math.min(this.y,t.y))}max(t){return new C(Math.max(this.x,t.x),Math.max(this.y,t.y))}toString(){return"".concat(this.x,",").concat(this.y)}setXY(t,e){return this.x=t,this.y=e,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setFromPoint(t){return this.x=t.x,this.y=t.y,this}swap(t){const e=this.x,i=this.y;this.x=t.x,this.y=t.y,t.x=e,t.y=i}clone(){return new C(this.x,this.y)}rotate(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:nr;const i=Xt(t),s=Vt(t),n=this.subtract(e);return new C(n.x*s-n.y*i,n.x*i+n.y*s).add(e)}transform(t){let e=arguments.length>1&&arguments[1]!==void 0&&arguments[1];return new C(t[0]*this.x+t[2]*this.y+(e?0:t[4]),t[1]*this.x+t[3]*this.y+(e?0:t[5]))}}const nr=new C(0,0),Cs=r=>!!r&&Array.isArray(r._objects);function oa(r){class t extends r{constructor(){super(...arguments),m(this,"_objects",[])}_onObjectAdded(i){}_onObjectRemoved(i){}_onStackOrderChanged(i){}add(){for(var i=arguments.length,s=new Array(i),n=0;n<i;n++)s[n]=arguments[n];const o=this._objects.push(...s);return s.forEach(a=>this._onObjectAdded(a)),o}insertAt(i){for(var s=arguments.length,n=new Array(s>1?s-1:0),o=1;o<s;o++)n[o-1]=arguments[o];return this._objects.splice(i,0,...n),n.forEach(a=>this._onObjectAdded(a)),this._objects.length}remove(){const i=this._objects,s=[];for(var n=arguments.length,o=new Array(n),a=0;a<n;a++)o[a]=arguments[a];return o.forEach(h=>{const l=i.indexOf(h);l!==-1&&(i.splice(l,1),s.push(h),this._onObjectRemoved(h))}),s}forEachObject(i){this.getObjects().forEach((s,n,o)=>i(s,n,o))}getObjects(){for(var i=arguments.length,s=new Array(i),n=0;n<i;n++)s[n]=arguments[n];return s.length===0?[...this._objects]:this._objects.filter(o=>o.isType(...s))}item(i){return this._objects[i]}isEmpty(){return this._objects.length===0}size(){return this._objects.length}contains(i,s){return!!this._objects.includes(i)||!!s&&this._objects.some(n=>n instanceof t&&n.contains(i,!0))}complexity(){return this._objects.reduce((i,s)=>i+=s.complexity?s.complexity():0,0)}sendObjectToBack(i){return!(!i||i===this._objects[0])&&(Be(this._objects,i),this._objects.unshift(i),this._onStackOrderChanged(i),!0)}bringObjectToFront(i){return!(!i||i===this._objects[this._objects.length-1])&&(Be(this._objects,i),this._objects.push(i),this._onStackOrderChanged(i),!0)}sendObjectBackwards(i,s){if(!i)return!1;const n=this._objects.indexOf(i);if(n!==0){const o=this.findNewLowerIndex(i,n,s);return Be(this._objects,i),this._objects.splice(o,0,i),this._onStackOrderChanged(i),!0}return!1}bringObjectForward(i,s){if(!i)return!1;const n=this._objects.indexOf(i);if(n!==this._objects.length-1){const o=this.findNewUpperIndex(i,n,s);return Be(this._objects,i),this._objects.splice(o,0,i),this._onStackOrderChanged(i),!0}return!1}moveObjectTo(i,s){return i!==this._objects[s]&&(Be(this._objects,i),this._objects.splice(s,0,i),this._onStackOrderChanged(i),!0)}findNewLowerIndex(i,s,n){let o;if(n){o=s;for(let a=s-1;a>=0;--a)if(i.isOverlapping(this._objects[a])){o=a;break}}else o=s-1;return o}findNewUpperIndex(i,s,n){let o;if(n){o=s;for(let a=s+1;a<this._objects.length;++a)if(i.isOverlapping(this._objects[a])){o=a;break}}else o=s+1;return o}collectObjects(i){let{left:s,top:n,width:o,height:a}=i,{includeIntersecting:h=!0}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const l=[],c=new C(s,n),u=c.add(new C(o,a));for(let d=this._objects.length-1;d>=0;d--){const g=this._objects[d];g.selectable&&g.visible&&(h&&g.intersectsWithRect(c,u)||g.isContainedWithinRect(c,u)||h&&g.containsPoint(c)||h&&g.containsPoint(u))&&l.push(g)}return l}}return t}class aa extends Fc{_setOptions(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};for(const e in t)this.set(e,t[e])}_setObject(t){for(const e in t)this._set(e,t[e])}set(t,e){return typeof t=="object"?this._setObject(t):this._set(t,e),this}_set(t,e){this[t]=e}toggle(t){const e=this.get(t);return typeof e=="boolean"&&this.set(t,!e),this}get(t){return this[t]}}function Fi(r){return fs().requestAnimationFrame(r)}function ha(r){return fs().cancelAnimationFrame(r)}let $c=0;const ke=()=>$c++,jt=()=>{const r=li().createElement("canvas");if(!r||r.getContext===void 0)throw new te("Failed to create `canvas` element");return r},la=()=>li().createElement("img"),Mt=r=>{const t=jt();return t.width=r.width,t.height=r.height,t},rr=(r,t,e)=>r.toDataURL("image/".concat(t),e),or=(r,t,e)=>new Promise((i,s)=>{r.toBlob(i,"image/".concat(t),e)}),Q=r=>r*Jn,ie=r=>r/Jn,ca=r=>r.every((t,e)=>t===pt[e]),gt=(r,t,e)=>new C(r).transform(t,e),Dt=r=>{const t=1/(r[0]*r[3]-r[1]*r[2]),e=[t*r[3],-t*r[1],-t*r[2],t*r[0],0,0],{x:i,y:s}=new C(r[4],r[5]).transform(e,!0);return e[4]=-i,e[5]=-s,e},nt=(r,t,e)=>[r[0]*t[0]+r[2]*t[1],r[1]*t[0]+r[3]*t[1],r[0]*t[2]+r[2]*t[3],r[1]*t[2]+r[3]*t[3],e?0:r[0]*t[4]+r[2]*t[5]+r[4],e?0:r[1]*t[4]+r[3]*t[5]+r[5]],ws=(r,t)=>r.reduceRight((e,i)=>i&&e?nt(i,e,t):i||e,void 0)||pt.concat(),ua=r=>{let[t,e]=r;return Math.atan2(e,t)},gi=r=>{const t=ua(r),e=Math.pow(r[0],2)+Math.pow(r[1],2),i=Math.sqrt(e),s=(r[0]*r[3]-r[2]*r[1])/i,n=Math.atan2(r[0]*r[2]+r[1]*r[3],e);return{angle:ie(t),scaleX:i,scaleY:s,skewX:ie(n),skewY:0,translateX:r[4]||0,translateY:r[5]||0}},fi=function(r){return[1,0,0,1,r,arguments.length>1&&arguments[1]!==void 0?arguments[1]:0]};function Fe(){let{angle:r=0}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},{x:t=0,y:e=0}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const i=Q(r),s=Vt(i),n=Xt(i);return[s,n,-n,s,t?t-(s*t-n*e):0,e?e-(n*t+s*e):0]}const xs=function(r){return[r,0,0,arguments.length>1&&arguments[1]!==void 0?arguments[1]:r,0,0]},da=r=>Math.tan(Q(r)),ar=r=>[1,0,da(r),1,0,0],hr=r=>[1,da(r),0,1,0,0],$i=r=>{let{scaleX:t=1,scaleY:e=1,flipX:i=!1,flipY:s=!1,skewX:n=0,skewY:o=0}=r,a=xs(i?-t:t,s?-e:e);return n&&(a=nt(a,ar(n),!0)),o&&(a=nt(a,hr(o),!0)),a},ga=r=>{const{translateX:t=0,translateY:e=0,angle:i=0}=r;let s=fi(t,e);i&&(s=nt(s,Fe({angle:i})));const n=$i(r);return ca(n)||(s=nt(s,n)),s},zi=function(r){let{signal:t,crossOrigin:e=null}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return new Promise(function(i,s){if(t&&t.aborted)return s(new ta("loadImage"));const n=la();let o;t&&(o=function(h){n.src="",s(h)},t.addEventListener("abort",o,{once:!0}));const a=function(){n.onload=n.onerror=null,o&&(t==null||t.removeEventListener("abort",o)),i(n)};r?(n.onload=a,n.onerror=function(){o&&(t==null||t.removeEventListener("abort",o)),s(new te("Error loading ".concat(n.src)))},e&&(n.crossOrigin=e),n.src=r):a()})},pi=function(r){let{signal:t,reviver:e=je}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return new Promise((i,s)=>{const n=[];t&&t.addEventListener("abort",s,{once:!0}),Promise.all(r.map(o=>D.getClass(o.type).fromObject(o,{signal:t}).then(a=>(e(o,a),n.push(a),a)))).then(i).catch(o=>{n.forEach(a=>{a.dispose&&a.dispose()}),s(o)}).finally(()=>{t&&t.removeEventListener("abort",s)})})},Hi=function(r){let{signal:t}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return new Promise((e,i)=>{const s=[];t&&t.addEventListener("abort",i,{once:!0});const n=Object.values(r).map(a=>a&&a.type&&D.has(a.type)?pi([a],{signal:t}).then(h=>{let[l]=h;return s.push(l),l}):a),o=Object.keys(r);Promise.all(n).then(a=>a.reduce((h,l,c)=>(h[o[c]]=l,h),{})).then(e).catch(a=>{s.forEach(h=>{h.dispose&&h.dispose()}),i(a)}).finally(()=>{t&&t.removeEventListener("abort",i)})})},$e=function(r){return(arguments.length>1&&arguments[1]!==void 0?arguments[1]:[]).reduce((t,e)=>(e in r&&(t[e]=r[e]),t),{})},lr=(r,t)=>Object.keys(r).reduce((e,i)=>(t(r[i],i,r)&&(e[i]=r[i]),e),{}),G=(r,t)=>parseFloat(Number(r).toFixed(t)),mi=r=>"matrix("+r.map(t=>G(t,W.NUM_FRACTION_DIGITS)).join(" ")+")",Bt=r=>!!r&&r.toLive!==void 0,fa=r=>!!r&&typeof r.toObject=="function",pa=r=>!!r&&r.offsetX!==void 0&&"source"in r,ze=r=>!!r&&"multiSelectionStacking"in r;function ma(r){const t=r&&Yt(r);let e=0,i=0;if(!r||!t)return{left:e,top:i};let s=r;const n=t.documentElement,o=t.body||{scrollLeft:0,scrollTop:0};for(;s&&(s.parentNode||s.host)&&(s=s.parentNode||s.host,s===t?(e=o.scrollLeft||n.scrollLeft||0,i=o.scrollTop||n.scrollTop||0):(e+=s.scrollLeft||0,i+=s.scrollTop||0),s.nodeType!==1||s.style.position!=="fixed"););return{left:e,top:i}}const Yt=r=>r.ownerDocument||null,va=r=>{var t;return((t=r.ownerDocument)===null||t===void 0?void 0:t.defaultView)||null},ya=function(r,t,e){let{width:i,height:s}=e,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:1;r.width=i,r.height=s,n>1&&(r.setAttribute("width",(i*n).toString()),r.setAttribute("height",(s*n).toString()),t.scale(n,n))},cr=(r,t)=>{let{width:e,height:i}=t;e&&(r.style.width=typeof e=="number"?"".concat(e,"px"):e),i&&(r.style.height=typeof i=="number"?"".concat(i,"px"):i)};function ba(r){return r.onselectstart!==void 0&&(r.onselectstart=()=>!1),r.style.userSelect=xt,r}class Ca{constructor(t){m(this,"_originalCanvasStyle",void 0),m(this,"lower",void 0);const e=this.createLowerCanvas(t);this.lower={el:e,ctx:e.getContext("2d")}}createLowerCanvas(t){const e=(i=t)&&i.getContext!==void 0?t:t&&li().getElementById(t)||jt();var i;if(e.hasAttribute("data-fabric"))throw new te("Trying to initialize a canvas that has already been initialized. Did you forget to dispose the canvas?");return this._originalCanvasStyle=e.style.cssText,e.setAttribute("data-fabric","main"),e.classList.add("lower-canvas"),e}cleanupDOM(t){let{width:e,height:i}=t;const{el:s}=this.lower;s.classList.remove("lower-canvas"),s.removeAttribute("data-fabric"),s.setAttribute("width","".concat(e)),s.setAttribute("height","".concat(i)),s.style.cssText=this._originalCanvasStyle||"",this._originalCanvasStyle=void 0}setDimensions(t,e){const{el:i,ctx:s}=this.lower;ya(i,s,t,e)}setCSSDimensions(t){cr(this.lower.el,t)}calcOffset(){return function(t){var e;const i=t&&Yt(t),s={left:0,top:0};if(!i)return s;const n=((e=va(t))===null||e===void 0?void 0:e.getComputedStyle(t,null))||{};s.left+=parseInt(n.borderLeftWidth,10)||0,s.top+=parseInt(n.borderTopWidth,10)||0,s.left+=parseInt(n.paddingLeft,10)||0,s.top+=parseInt(n.paddingTop,10)||0;let o={left:0,top:0};const a=i.documentElement;t.getBoundingClientRect!==void 0&&(o=t.getBoundingClientRect());const h=ma(t);return{left:o.left+h.left-(a.clientLeft||0)+s.left,top:o.top+h.top-(a.clientTop||0)+s.top}}(this.lower.el)}dispose(){ee().dispose(this.lower.el),delete this.lower}}const zc={backgroundVpt:!0,backgroundColor:"",overlayVpt:!0,overlayColor:"",includeDefaultValues:!0,svgViewportTransformation:!0,renderOnAddRemove:!0,skipOffscreen:!0,enableRetinaScaling:!0,imageSmoothingEnabled:!0,controlsAboveOverlay:!1,allowTouchScrolling:!1,viewportTransform:[...pt]};class Ni extends oa(aa){get lowerCanvasEl(){var t;return(t=this.elements.lower)===null||t===void 0?void 0:t.el}get contextContainer(){var t;return(t=this.elements.lower)===null||t===void 0?void 0:t.ctx}static getDefaults(){return Ni.ownDefaults}constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),Object.assign(this,this.constructor.getDefaults()),this.set(e),this.initElements(t),this._setDimensionsImpl({width:this.width||this.elements.lower.el.width||0,height:this.height||this.elements.lower.el.height||0}),this.skipControlsDrawing=!1,this.viewportTransform=[...this.viewportTransform],this.calcViewportBoundaries()}initElements(t){this.elements=new Ca(t)}add(){const t=super.add(...arguments);return arguments.length>0&&this.renderOnAddRemove&&this.requestRenderAll(),t}insertAt(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),s=1;s<e;s++)i[s-1]=arguments[s];const n=super.insertAt(t,...i);return i.length>0&&this.renderOnAddRemove&&this.requestRenderAll(),n}remove(){const t=super.remove(...arguments);return t.length>0&&this.renderOnAddRemove&&this.requestRenderAll(),t}_onObjectAdded(t){t.canvas&&t.canvas!==this&&(Se("warn",`Canvas is trying to add an object that belongs to a different canvas.
Resulting to default behavior: removing object from previous canvas and adding to new canvas`),t.canvas.remove(t)),t._set("canvas",this),t.setCoords(),this.fire("object:added",{target:t}),t.fire("added",{target:this})}_onObjectRemoved(t){t._set("canvas",void 0),this.fire("object:removed",{target:t}),t.fire("removed",{target:this})}_onStackOrderChanged(){this.renderOnAddRemove&&this.requestRenderAll()}getRetinaScaling(){return this.enableRetinaScaling?ia():1}calcOffset(){return this._offset=this.elements.calcOffset()}getWidth(){return this.width}getHeight(){return this.height}setWidth(t,e){return this.setDimensions({width:t},e)}setHeight(t,e){return this.setDimensions({height:t},e)}_setDimensionsImpl(t){let{cssOnly:e=!1,backstoreOnly:i=!1}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!e){const s=y({width:this.width,height:this.height},t);this.elements.setDimensions(s,this.getRetinaScaling()),this.hasLostContext=!0,this.width=s.width,this.height=s.height}i||this.elements.setCSSDimensions(t),this.calcOffset()}setDimensions(t,e){this._setDimensionsImpl(t,e),e&&e.cssOnly||this.requestRenderAll()}getZoom(){return this.viewportTransform[0]}setViewportTransform(t){this.viewportTransform=t,this.calcViewportBoundaries(),this.renderOnAddRemove&&this.requestRenderAll()}zoomToPoint(t,e){const i=t,s=[...this.viewportTransform],n=gt(t,Dt(s));s[0]=e,s[3]=e;const o=gt(n,s);s[4]+=i.x-o.x,s[5]+=i.y-o.y,this.setViewportTransform(s)}setZoom(t){this.zoomToPoint(new C(0,0),t)}absolutePan(t){const e=[...this.viewportTransform];return e[4]=-t.x,e[5]=-t.y,this.setViewportTransform(e)}relativePan(t){return this.absolutePan(new C(-t.x-this.viewportTransform[4],-t.y-this.viewportTransform[5]))}getElement(){return this.elements.lower.el}clearContext(t){t.clearRect(0,0,this.width,this.height)}getContext(){return this.elements.lower.ctx}clear(){this.remove(...this.getObjects()),this.backgroundImage=void 0,this.overlayImage=void 0,this.backgroundColor="",this.overlayColor="",this.clearContext(this.getContext()),this.fire("canvas:cleared"),this.renderOnAddRemove&&this.requestRenderAll()}renderAll(){this.cancelRequestedRender(),this.destroyed||this.renderCanvas(this.getContext(),this._objects)}renderAndReset(){this.nextRenderHandle=0,this.renderAll()}requestRenderAll(){this.nextRenderHandle||this.disposed||this.destroyed||(this.nextRenderHandle=Fi(()=>this.renderAndReset()))}calcViewportBoundaries(){const t=this.width,e=this.height,i=Dt(this.viewportTransform),s=gt({x:0,y:0},i),n=gt({x:t,y:e},i),o=s.min(n),a=s.max(n);return this.vptCoords={tl:o,tr:new C(a.x,o.y),bl:new C(o.x,a.y),br:a}}cancelRequestedRender(){this.nextRenderHandle&&(ha(this.nextRenderHandle),this.nextRenderHandle=0)}drawControls(t){}renderCanvas(t,e){if(this.destroyed)return;const i=this.viewportTransform,s=this.clipPath;this.calcViewportBoundaries(),this.clearContext(t),t.imageSmoothingEnabled=this.imageSmoothingEnabled,t.patternQuality="best",this.fire("before:render",{ctx:t}),this._renderBackground(t),t.save(),t.transform(i[0],i[1],i[2],i[3],i[4],i[5]),this._renderObjects(t,e),t.restore(),this.controlsAboveOverlay||this.skipControlsDrawing||this.drawControls(t),s&&(s._set("canvas",this),s.shouldCache(),s._transformDone=!0,s.renderCache({forClipping:!0}),this.drawClipPathOnCanvas(t,s)),this._renderOverlay(t),this.controlsAboveOverlay&&!this.skipControlsDrawing&&this.drawControls(t),this.fire("after:render",{ctx:t}),this.__cleanupTask&&(this.__cleanupTask(),this.__cleanupTask=void 0)}drawClipPathOnCanvas(t,e){const i=this.viewportTransform;t.save(),t.transform(...i),t.globalCompositeOperation="destination-in",e.transform(t),t.scale(1/e.zoomX,1/e.zoomY),t.drawImage(e._cacheCanvas,-e.cacheTranslationX,-e.cacheTranslationY),t.restore()}_renderObjects(t,e){for(let i=0,s=e.length;i<s;++i)e[i]&&e[i].render(t)}_renderBackgroundOrOverlay(t,e){const i=this["".concat(e,"Color")],s=this["".concat(e,"Image")],n=this.viewportTransform,o=this["".concat(e,"Vpt")];if(!i&&!s)return;const a=Bt(i);if(i){if(t.save(),t.beginPath(),t.moveTo(0,0),t.lineTo(this.width,0),t.lineTo(this.width,this.height),t.lineTo(0,this.height),t.closePath(),t.fillStyle=a?i.toLive(t):i,o&&t.transform(...n),a){t.transform(1,0,0,1,i.offsetX||0,i.offsetY||0);const h=i.gradientTransform||i.patternTransform;h&&t.transform(...h)}t.fill(),t.restore()}if(s){t.save();const{skipOffscreen:h}=this;this.skipOffscreen=o,o&&t.transform(...n),s.render(t),this.skipOffscreen=h,t.restore()}}_renderBackground(t){this._renderBackgroundOrOverlay(t,"background")}_renderOverlay(t){this._renderBackgroundOrOverlay(t,"overlay")}getCenter(){return{top:this.height/2,left:this.width/2}}getCenterPoint(){return new C(this.width/2,this.height/2)}centerObjectH(t){return this._centerObject(t,new C(this.getCenterPoint().x,t.getCenterPoint().y))}centerObjectV(t){return this._centerObject(t,new C(t.getCenterPoint().x,this.getCenterPoint().y))}centerObject(t){return this._centerObject(t,this.getCenterPoint())}viewportCenterObject(t){return this._centerObject(t,this.getVpCenter())}viewportCenterObjectH(t){return this._centerObject(t,new C(this.getVpCenter().x,t.getCenterPoint().y))}viewportCenterObjectV(t){return this._centerObject(t,new C(t.getCenterPoint().x,this.getVpCenter().y))}getVpCenter(){return gt(this.getCenterPoint(),Dt(this.viewportTransform))}_centerObject(t,e){t.setXY(e,j,j),t.setCoords(),this.renderOnAddRemove&&this.requestRenderAll()}toDatalessJSON(t){return this.toDatalessObject(t)}toObject(t){return this._toObjectMethod("toObject",t)}toJSON(){return this.toObject()}toDatalessObject(t){return this._toObjectMethod("toDatalessObject",t)}_toObjectMethod(t,e){const i=this.clipPath,s=i&&!i.excludeFromExport?this._toObject(i,t,e):null;return y(y(y({version:Zn},$e(this,e)),{},{objects:this._objects.filter(n=>!n.excludeFromExport).map(n=>this._toObject(n,t,e))},this.__serializeBgOverlay(t,e)),s?{clipPath:s}:null)}_toObject(t,e,i){let s;this.includeDefaultValues||(s=t.includeDefaultValues,t.includeDefaultValues=!1);const n=t[e](i);return this.includeDefaultValues||(t.includeDefaultValues=!!s),n}__serializeBgOverlay(t,e){const i={},s=this.backgroundImage,n=this.overlayImage,o=this.backgroundColor,a=this.overlayColor;return Bt(o)?o.excludeFromExport||(i.background=o.toObject(e)):o&&(i.background=o),Bt(a)?a.excludeFromExport||(i.overlay=a.toObject(e)):a&&(i.overlay=a),s&&!s.excludeFromExport&&(i.backgroundImage=this._toObject(s,t,e)),n&&!n.excludeFromExport&&(i.overlayImage=this._toObject(n,t,e)),i}toSVG(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;t.reviver=e;const i=[];return this._setSVGPreamble(i,t),this._setSVGHeader(i,t),this.clipPath&&i.push('<g clip-path="url(#'.concat(this.clipPath.clipPathId,`)" >
`)),this._setSVGBgOverlayColor(i,"background"),this._setSVGBgOverlayImage(i,"backgroundImage",e),this._setSVGObjects(i,e),this.clipPath&&i.push(`</g>
`),this._setSVGBgOverlayColor(i,"overlay"),this._setSVGBgOverlayImage(i,"overlayImage",e),i.push("</svg>"),i.join("")}_setSVGPreamble(t,e){e.suppressPreamble||t.push('<?xml version="1.0" encoding="',e.encoding||"UTF-8",`" standalone="no" ?>
`,'<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" ',`"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
`)}_setSVGHeader(t,e){const i=e.width||"".concat(this.width),s=e.height||"".concat(this.height),n=W.NUM_FRACTION_DIGITS,o=e.viewBox;let a;if(o)a='viewBox="'.concat(o.x," ").concat(o.y," ").concat(o.width," ").concat(o.height,'" ');else if(this.svgViewportTransformation){const h=this.viewportTransform;a='viewBox="'.concat(G(-h[4]/h[0],n)," ").concat(G(-h[5]/h[3],n)," ").concat(G(this.width/h[0],n)," ").concat(G(this.height/h[3],n),'" ')}else a='viewBox="0 0 '.concat(this.width," ").concat(this.height,'" ');t.push("<svg ",'xmlns="http://www.w3.org/2000/svg" ','xmlns:xlink="http://www.w3.org/1999/xlink" ','version="1.1" ','width="',i,'" ','height="',s,'" ',a,`xml:space="preserve">
`,"<desc>Created with Fabric.js ",Zn,`</desc>
`,`<defs>
`,this.createSVGFontFacesMarkup(),this.createSVGRefElementsMarkup(),this.createSVGClipPathMarkup(e),`</defs>
`)}createSVGClipPathMarkup(t){const e=this.clipPath;return e?(e.clipPathId="CLIPPATH_".concat(ke()),'<clipPath id="'.concat(e.clipPathId,`" >
`).concat(e.toClipPathSVG(t.reviver),`</clipPath>
`)):""}createSVGRefElementsMarkup(){return["background","overlay"].map(t=>{const e=this["".concat(t,"Color")];if(Bt(e)){const i=this["".concat(t,"Vpt")],s=this.viewportTransform,n={isType:()=>!1,width:this.width/(i?s[0]:1),height:this.height/(i?s[3]:1)};return e.toSVG(n,{additionalTransform:i?mi(s):""})}}).join("")}createSVGFontFacesMarkup(){const t=[],e={},i=W.fontPaths;this._objects.forEach(function n(o){t.push(o),Cs(o)&&o._objects.forEach(n)}),t.forEach(n=>{if(!(o=n)||typeof o._renderText!="function")return;var o;const{styles:a,fontFamily:h}=n;!e[h]&&i[h]&&(e[h]=!0,a&&Object.values(a).forEach(l=>{Object.values(l).forEach(c=>{let{fontFamily:u=""}=c;!e[u]&&i[u]&&(e[u]=!0)})}))});const s=Object.keys(e).map(n=>`		@font-face {
			font-family: '`.concat(n,`';
			src: url('`).concat(i[n],`');
		}
`)).join("");return s?`	<style type="text/css"><![CDATA[
`.concat(s,`]]></style>
`):""}_setSVGObjects(t,e){this.forEachObject(i=>{i.excludeFromExport||this._setSVGObject(t,i,e)})}_setSVGObject(t,e,i){t.push(e.toSVG(i))}_setSVGBgOverlayImage(t,e,i){const s=this[e];s&&!s.excludeFromExport&&s.toSVG&&t.push(s.toSVG(i))}_setSVGBgOverlayColor(t,e){const i=this["".concat(e,"Color")];if(i)if(Bt(i)){const s=i.repeat||"",n=this.width,o=this.height,a=this["".concat(e,"Vpt")]?mi(Dt(this.viewportTransform)):"";t.push('<rect transform="'.concat(a," translate(").concat(n/2,",").concat(o/2,')" x="').concat(i.offsetX-n/2,'" y="').concat(i.offsetY-o/2,'" width="').concat(s!=="repeat-y"&&s!=="no-repeat"||!pa(i)?n:i.source.width,'" height="').concat(s!=="repeat-x"&&s!=="no-repeat"||!pa(i)?o:i.source.height,'" fill="url(#SVGID_').concat(i.id,`)"></rect>
`))}else t.push('<rect x="0" y="0" width="100%" height="100%" ','fill="',i,'"',`></rect>
`)}loadFromJSON(t,e){let{signal:i}=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(!t)return Promise.reject(new te("`json` is undefined"));const s=typeof t=="string"?JSON.parse(t):t,{objects:n=[],backgroundImage:o,background:a,overlayImage:h,overlay:l,clipPath:c}=s,u=this.renderOnAddRemove;return this.renderOnAddRemove=!1,Promise.all([pi(n,{reviver:e,signal:i}),Hi({backgroundImage:o,backgroundColor:a,overlayImage:h,overlayColor:l,clipPath:c},{signal:i})]).then(d=>{let[g,f]=d;return this.clear(),this.add(...g),this.set(s),this.set(f),this.renderOnAddRemove=u,this})}clone(t){const e=this.toObject(t);return this.cloneWithoutData().loadFromJSON(e)}cloneWithoutData(){const t=Mt(this);return new this.constructor(t)}toDataURL(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const{format:e="png",quality:i=1,multiplier:s=1,enableRetinaScaling:n=!1}=t,o=s*(n?this.getRetinaScaling():1);return rr(this.toCanvasElement(o,t),e,i)}toBlob(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const{format:e="png",quality:i=1,multiplier:s=1,enableRetinaScaling:n=!1}=t,o=s*(n?this.getRetinaScaling():1);return or(this.toCanvasElement(o,t),e,i)}toCanvasElement(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:1,{width:e,height:i,left:s,top:n,filter:o}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const a=(e||this.width)*t,h=(i||this.height)*t,l=this.getZoom(),c=this.width,u=this.height,d=this.skipControlsDrawing,g=l*t,f=this.viewportTransform,p=[g,0,0,g,(f[4]-(s||0))*t,(f[5]-(n||0))*t],v=this.enableRetinaScaling,b=Mt({width:a,height:h}),w=o?this._objects.filter(x=>o(x)):this._objects;return this.enableRetinaScaling=!1,this.viewportTransform=p,this.width=a,this.height=h,this.skipControlsDrawing=!0,this.calcViewportBoundaries(),this.renderCanvas(b.getContext("2d"),w),this.viewportTransform=f,this.width=c,this.height=u,this.calcViewportBoundaries(),this.enableRetinaScaling=v,this.skipControlsDrawing=d,b}dispose(){return!this.disposed&&this.elements.cleanupDOM({width:this.width,height:this.height}),bs.cancelByCanvas(this),this.disposed=!0,new Promise((t,e)=>{const i=()=>{this.destroy(),t(!0)};i.kill=e,this.__cleanupTask&&this.__cleanupTask.kill("aborted"),this.destroyed?t(!1):this.nextRenderHandle?this.__cleanupTask=i:i()})}destroy(){this.destroyed=!0,this.cancelRequestedRender(),this.forEachObject(t=>t.dispose()),this._objects=[],this.backgroundImage&&this.backgroundImage.dispose(),this.backgroundImage=void 0,this.overlayImage&&this.overlayImage.dispose(),this.overlayImage=void 0,this.elements.dispose()}toString(){return"#<Canvas (".concat(this.complexity(),"): { objects: ").concat(this._objects.length," }>")}}m(Ni,"ownDefaults",zc);const Hc=["touchstart","touchmove","touchend"],wa=r=>{const t=ma(r.target),e=function(i){const s=i.changedTouches;return s&&s[0]?s[0]:i}(r);return new C(e.clientX+t.left,e.clientY+t.top)},_s=r=>Hc.includes(r.type)||r.pointerType==="touch",xa=r=>{r.preventDefault(),r.stopPropagation()},se=r=>{let t=0,e=0,i=0,s=0;for(let n=0,o=r.length;n<o;n++){const{x:a,y:h}=r[n];(a>i||!n)&&(i=a),(a<t||!n)&&(t=a),(h>s||!n)&&(s=h),(h<e||!n)&&(e=h)}return{left:t,top:e,width:i-t,height:s-e}},Nc=["translateX","translateY","scaleX","scaleY"],_a=(r,t)=>vi(r,nt(t,r.calcOwnMatrix())),vi=(r,t)=>{const e=gi(t),{translateX:i,translateY:s,scaleX:n,scaleY:o}=e,a=U(e,Nc),h=new C(i,s);r.flipX=!1,r.flipY=!1,Object.assign(r,a),r.set({scaleX:n,scaleY:o}),r.setPositionByOrigin(h,j,j)},Sa=r=>{r.scaleX=1,r.scaleY=1,r.skewX=0,r.skewY=0,r.flipX=!1,r.flipY=!1,r.rotate(0)},ur=r=>({scaleX:r.scaleX,scaleY:r.scaleY,skewX:r.skewX,skewY:r.skewY,angle:r.angle,left:r.left,flipX:r.flipX,flipY:r.flipY,top:r.top}),Ss=(r,t,e)=>{const i=r/2,s=t/2,n=[new C(-i,-s),new C(i,-s),new C(-i,s),new C(i,s)].map(a=>a.transform(e)),o=se(n);return new C(o.width,o.height)},Wi=function(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:pt;return nt(Dt(arguments.length>1&&arguments[1]!==void 0?arguments[1]:pt),r)},He=function(r){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:pt,e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:pt;return r.transform(Wi(t,e))},Ta=function(r){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:pt,e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:pt;return r.transform(Wi(t,e),!0)},dr=(r,t,e)=>{const i=Wi(t,e);return vi(r,nt(i,r.calcOwnMatrix())),i},Ea=(r,t)=>{var e;const{transform:{target:i}}=t;(e=i.canvas)===null||e===void 0||e.fire("object:".concat(r),y(y({},t),{},{target:i})),i.fire(r,t)},Wc={left:-.5,top:-.5,center:0,bottom:.5,right:.5},rt=r=>typeof r=="string"?Wc[r]:r-.5,Ts="not-allowed";function ka(r){return rt(r.originX)===rt(j)&&rt(r.originY)===rt(j)}function Oa(r){return .5-rt(r)}const Gt=(r,t)=>r[t],Ma=(r,t,e,i)=>({e:r,transform:t,pointer:new C(e,i)});function Da(r,t){const e=r.getTotalAngle()+ie(Math.atan2(t.y,t.x))+360;return Math.round(e%360/45)}function gr(r,t,e,i,s){var n;let{target:o,corner:a}=r;const h=o.controls[a],l=((n=o.canvas)===null||n===void 0?void 0:n.getZoom())||1,c=o.padding/l,u=function(d,g,f,p){const v=d.getRelativeCenterPoint(),b=f!==void 0&&p!==void 0?d.translateToGivenOrigin(v,j,j,f,p):new C(d.left,d.top);return(d.angle?g.rotate(-Q(d.angle),v):g).subtract(b)}(o,new C(i,s),t,e);return u.x>=c&&(u.x-=c),u.x<=-c&&(u.x+=c),u.y>=c&&(u.y-=c),u.y<=c&&(u.y+=c),u.x-=h.offsetX,u.y-=h.offsetY,u}const Vc=(r,t,e,i)=>{const{target:s,offsetX:n,offsetY:o}=t,a=e-n,h=i-o,l=!Gt(s,"lockMovementX")&&s.left!==a,c=!Gt(s,"lockMovementY")&&s.top!==h;return l&&s.set(V,a),c&&s.set(wt,h),(l||c)&&Ea(sa,Ma(r,t,e,i)),l||c},Aa={aliceblue:"#F0F8FF",antiquewhite:"#FAEBD7",aqua:"#0FF",aquamarine:"#7FFFD4",azure:"#F0FFFF",beige:"#F5F5DC",bisque:"#FFE4C4",black:"#000",blanchedalmond:"#FFEBCD",blue:"#00F",blueviolet:"#8A2BE2",brown:"#A52A2A",burlywood:"#DEB887",cadetblue:"#5F9EA0",chartreuse:"#7FFF00",chocolate:"#D2691E",coral:"#FF7F50",cornflowerblue:"#6495ED",cornsilk:"#FFF8DC",crimson:"#DC143C",cyan:"#0FF",darkblue:"#00008B",darkcyan:"#008B8B",darkgoldenrod:"#B8860B",darkgray:"#A9A9A9",darkgrey:"#A9A9A9",darkgreen:"#006400",darkkhaki:"#BDB76B",darkmagenta:"#8B008B",darkolivegreen:"#556B2F",darkorange:"#FF8C00",darkorchid:"#9932CC",darkred:"#8B0000",darksalmon:"#E9967A",darkseagreen:"#8FBC8F",darkslateblue:"#483D8B",darkslategray:"#2F4F4F",darkslategrey:"#2F4F4F",darkturquoise:"#00CED1",darkviolet:"#9400D3",deeppink:"#FF1493",deepskyblue:"#00BFFF",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1E90FF",firebrick:"#B22222",floralwhite:"#FFFAF0",forestgreen:"#228B22",fuchsia:"#F0F",gainsboro:"#DCDCDC",ghostwhite:"#F8F8FF",gold:"#FFD700",goldenrod:"#DAA520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#ADFF2F",honeydew:"#F0FFF0",hotpink:"#FF69B4",indianred:"#CD5C5C",indigo:"#4B0082",ivory:"#FFFFF0",khaki:"#F0E68C",lavender:"#E6E6FA",lavenderblush:"#FFF0F5",lawngreen:"#7CFC00",lemonchiffon:"#FFFACD",lightblue:"#ADD8E6",lightcoral:"#F08080",lightcyan:"#E0FFFF",lightgoldenrodyellow:"#FAFAD2",lightgray:"#D3D3D3",lightgrey:"#D3D3D3",lightgreen:"#90EE90",lightpink:"#FFB6C1",lightsalmon:"#FFA07A",lightseagreen:"#20B2AA",lightskyblue:"#87CEFA",lightslategray:"#789",lightslategrey:"#789",lightsteelblue:"#B0C4DE",lightyellow:"#FFFFE0",lime:"#0F0",limegreen:"#32CD32",linen:"#FAF0E6",magenta:"#F0F",maroon:"#800000",mediumaquamarine:"#66CDAA",mediumblue:"#0000CD",mediumorchid:"#BA55D3",mediumpurple:"#9370DB",mediumseagreen:"#3CB371",mediumslateblue:"#7B68EE",mediumspringgreen:"#00FA9A",mediumturquoise:"#48D1CC",mediumvioletred:"#C71585",midnightblue:"#191970",mintcream:"#F5FFFA",mistyrose:"#FFE4E1",moccasin:"#FFE4B5",navajowhite:"#FFDEAD",navy:"#000080",oldlace:"#FDF5E6",olive:"#808000",olivedrab:"#6B8E23",orange:"#FFA500",orangered:"#FF4500",orchid:"#DA70D6",palegoldenrod:"#EEE8AA",palegreen:"#98FB98",paleturquoise:"#AFEEEE",palevioletred:"#DB7093",papayawhip:"#FFEFD5",peachpuff:"#FFDAB9",peru:"#CD853F",pink:"#FFC0CB",plum:"#DDA0DD",powderblue:"#B0E0E6",purple:"#800080",rebeccapurple:"#639",red:"#F00",rosybrown:"#BC8F8F",royalblue:"#4169E1",saddlebrown:"#8B4513",salmon:"#FA8072",sandybrown:"#F4A460",seagreen:"#2E8B57",seashell:"#FFF5EE",sienna:"#A0522D",silver:"#C0C0C0",skyblue:"#87CEEB",slateblue:"#6A5ACD",slategray:"#708090",slategrey:"#708090",snow:"#FFFAFA",springgreen:"#00FF7F",steelblue:"#4682B4",tan:"#D2B48C",teal:"#008080",thistle:"#D8BFD8",tomato:"#FF6347",turquoise:"#40E0D0",violet:"#EE82EE",wheat:"#F5DEB3",white:"#FFF",whitesmoke:"#F5F5F5",yellow:"#FF0",yellowgreen:"#9ACD32"},fr=(r,t,e)=>(e<0&&(e+=1),e>1&&(e-=1),e<1/6?r+6*(t-r)*e:e<.5?t:e<2/3?r+(t-r)*(2/3-e)*6:r),Pa=(r,t,e,i)=>{r/=255,t/=255,e/=255;const s=Math.max(r,t,e),n=Math.min(r,t,e);let o,a;const h=(s+n)/2;if(s===n)o=a=0;else{const l=s-n;switch(a=h>.5?l/(2-s-n):l/(s+n),s){case r:o=(t-e)/l+(t<e?6:0);break;case t:o=(e-r)/l+2;break;case e:o=(r-t)/l+4}o/=6}return[Math.round(360*o),Math.round(100*a),Math.round(100*h),i]},La=function(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"1";return parseFloat(r)/(r.endsWith("%")?100:1)},Es=r=>Math.min(Math.round(r),255).toString(16).toUpperCase().padStart(2,"0"),Ra=r=>{let[t,e,i,s=1]=r;const n=Math.round(.3*t+.59*e+.11*i);return[n,n,n,s]};class N{constructor(t){if(m(this,"isUnrecognised",!1),t)if(t instanceof N)this.setSource([...t._source]);else if(Array.isArray(t)){const[e,i,s,n=1]=t;this.setSource([e,i,s,n])}else this.setSource(this._tryParsingColor(t));else this.setSource([0,0,0,1])}_tryParsingColor(t){return(t=t.toLowerCase())in Aa&&(t=Aa[t]),t==="transparent"?[255,255,255,0]:N.sourceFromHex(t)||N.sourceFromRgb(t)||N.sourceFromHsl(t)||(this.isUnrecognised=!0)&&[0,0,0,1]}getSource(){return this._source}setSource(t){this._source=t}toRgb(){const[t,e,i]=this.getSource();return"rgb(".concat(t,",").concat(e,",").concat(i,")")}toRgba(){return"rgba(".concat(this.getSource().join(","),")")}toHsl(){const[t,e,i]=Pa(...this.getSource());return"hsl(".concat(t,",").concat(e,"%,").concat(i,"%)")}toHsla(){const[t,e,i,s]=Pa(...this.getSource());return"hsla(".concat(t,",").concat(e,"%,").concat(i,"%,").concat(s,")")}toHex(){return this.toHexa().slice(0,6)}toHexa(){const[t,e,i,s]=this.getSource();return"".concat(Es(t)).concat(Es(e)).concat(Es(i)).concat(Es(Math.round(255*s)))}getAlpha(){return this.getSource()[3]}setAlpha(t){return this._source[3]=t,this}toGrayscale(){return this.setSource(Ra(this.getSource())),this}toBlackWhite(t){const[e,,,i]=Ra(this.getSource()),s=e<(t||127)?0:255;return this.setSource([s,s,s,i]),this}overlayWith(t){t instanceof N||(t=new N(t));const e=this.getSource(),i=t.getSource(),[s,n,o]=e.map((a,h)=>Math.round(.5*a+.5*i[h]));return this.setSource([s,n,o,e[3]]),this}static fromRgb(t){return N.fromRgba(t)}static fromRgba(t){return new N(N.sourceFromRgb(t))}static sourceFromRgb(t){const e=t.match(/^rgba?\(\s*(\d{0,3}(?:\.\d+)?%?)\s*[\s|,]\s*(\d{0,3}(?:\.\d+)?%?)\s*[\s|,]\s*(\d{0,3}(?:\.\d+)?%?)\s*(?:\s*[,/]\s*(\d{0,3}(?:\.\d+)?%?)\s*)?\)$/i);if(e){const[i,s,n]=e.slice(1,4).map(o=>{const a=parseFloat(o);return o.endsWith("%")?Math.round(2.55*a):a});return[i,s,n,La(e[4])]}}static fromHsl(t){return N.fromHsla(t)}static fromHsla(t){return new N(N.sourceFromHsl(t))}static sourceFromHsl(t){const e=t.match(/^hsla?\(\s*([+-]?\d{0,3}(?:\.\d+)?(?:deg|turn|rad)?)\s*[\s|,]\s*(\d{0,3}(?:\.\d+)?%?)\s*[\s|,]\s*(\d{0,3}(?:\.\d+)?%?)\s*(?:\s*[,/]\s*(\d*(?:\.\d+)?%?)\s*)?\)$/i);if(!e)return;const i=(N.parseAngletoDegrees(e[1])%360+360)%360/360,s=parseFloat(e[2])/100,n=parseFloat(e[3])/100;let o,a,h;if(s===0)o=a=h=n;else{const l=n<=.5?n*(s+1):n+s-n*s,c=2*n-l;o=fr(c,l,i+1/3),a=fr(c,l,i),h=fr(c,l,i-1/3)}return[Math.round(255*o),Math.round(255*a),Math.round(255*h),La(e[4])]}static fromHex(t){return new N(N.sourceFromHex(t))}static sourceFromHex(t){if(t.match(/^#?(([0-9a-f]){3,4}|([0-9a-f]{2}){3,4})$/i)){const e=t.slice(t.indexOf("#")+1);let i;i=e.length<=4?e.split("").map(h=>h+h):e.match(/.{2}/g);const[s,n,o,a=255]=i.map(h=>parseInt(h,16));return[s,n,o,a/255]}}static parseAngletoDegrees(t){const e=t.toLowerCase(),i=parseFloat(e);return e.includes("rad")?ie(i):e.includes("turn")?360*i:i}}const Ne=function(r){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Qn;const e=/\D{0,2}$/.exec(r),i=parseFloat(r),s=W.DPI;switch(e==null?void 0:e[0]){case"mm":return i*s/25.4;case"cm":return i*s/2.54;case"in":return i*s;case"pt":return i*s/72;case"pc":return i*s/72*12;case"em":return i*t;default:return i}},Ia=r=>{const[t,e]=r.trim().split(" "),[i,s]=(n=t)&&n!==xt?[n.slice(1,4),n.slice(5,8)]:n===xt?[n,n]:["Mid","Mid"];var n;return{meetOrSlice:e||"meet",alignX:i,alignY:s}},Vi=function(r,t){let e,i,s=!(arguments.length>2&&arguments[2]!==void 0)||arguments[2];if(t)if(t.toLive)e="url(#SVGID_".concat(t.id,")");else{const n=new N(t),o=n.getAlpha();e=n.toRgb(),o!==1&&(i=o.toString())}else e="none";return s?"".concat(r,": ").concat(e,"; ").concat(i?"".concat(r,"-opacity: ").concat(i,"; "):""):"".concat(r,'="').concat(e,'" ').concat(i?"".concat(r,'-opacity="').concat(i,'" '):"")};class ja{getSvgStyles(t){const e=this.fillRule?this.fillRule:"nonzero",i=this.strokeWidth?this.strokeWidth:"0",s=this.strokeDashArray?this.strokeDashArray.join(" "):xt,n=this.strokeDashOffset?this.strokeDashOffset:"0",o=this.strokeLineCap?this.strokeLineCap:"butt",a=this.strokeLineJoin?this.strokeLineJoin:"miter",h=this.strokeMiterLimit?this.strokeMiterLimit:"4",l=this.opacity!==void 0?this.opacity:"1",c=this.visible?"":" visibility: hidden;",u=t?"":this.getSvgFilter(),d=Vi(at,this.fill);return[Vi(_t,this.stroke),"stroke-width: ",i,"; ","stroke-dasharray: ",s,"; ","stroke-linecap: ",o,"; ","stroke-dashoffset: ",n,"; ","stroke-linejoin: ",a,"; ","stroke-miterlimit: ",h,"; ",d,"fill-rule: ",e,"; ","opacity: ",l,";",u,c].join("")}getSvgFilter(){return this.shadow?"filter: url(#SVGID_".concat(this.shadow.id,");"):""}getSvgCommons(){return[this.id?'id="'.concat(this.id,'" '):"",this.clipPath?'clip-path="url(#'.concat(this.clipPath.clipPathId,')" '):""].join("")}getSvgTransform(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";const i=t?this.calcTransformMatrix():this.calcOwnMatrix(),s='transform="'.concat(mi(i));return"".concat(s).concat(e,'" ')}_toSVG(t){return[""]}toSVG(t){return this._createBaseSVGMarkup(this._toSVG(t),{reviver:t})}toClipPathSVG(t){return"	"+this._createBaseClipPathSVGMarkup(this._toSVG(t),{reviver:t})}_createBaseClipPathSVGMarkup(t){let{reviver:e,additionalTransform:i=""}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const s=[this.getSvgTransform(!0,i),this.getSvgCommons()].join(""),n=t.indexOf("COMMON_PARTS");return t[n]=s,e?e(t.join("")):t.join("")}_createBaseSVGMarkup(t){let{noStyle:e,reviver:i,withShadow:s,additionalTransform:n}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const o=e?"":'style="'.concat(this.getSvgStyles(),'" '),a=s?'style="'.concat(this.getSvgFilter(),'" '):"",h=this.clipPath,l=this.strokeUniform?'vector-effect="non-scaling-stroke" ':"",c=h&&h.absolutePositioned,u=this.stroke,d=this.fill,g=this.shadow,f=[],p=t.indexOf("COMMON_PARTS");let v;h&&(h.clipPathId="CLIPPATH_".concat(ke()),v='<clipPath id="'.concat(h.clipPathId,`" >
`).concat(h.toClipPathSVG(i),`</clipPath>
`)),c&&f.push("<g ",a,this.getSvgCommons(),` >
`),f.push("<g ",this.getSvgTransform(!1),c?"":a+this.getSvgCommons(),` >
`);const b=[o,l,e?"":this.addPaintOrder()," ",n?'transform="'.concat(n,'" '):""].join("");return t[p]=b,Bt(d)&&f.push(d.toSVG(this)),Bt(u)&&f.push(u.toSVG(this)),g&&f.push(g.toSVG(this)),h&&f.push(v),f.push(t.join("")),f.push(`</g>
`),c&&f.push(`</g>
`),i?i(f.join("")):f.join("")}addPaintOrder(){return this.paintFirst!==at?' paint-order="'.concat(this.paintFirst,'" '):""}}function ks(r){return new RegExp("^("+r.join("|")+")\\b","i")}var Ba;const We=String.raw(Ba||(Ba=_e(["(?:[-+]?(?:d*.d+|d+.?)(?:[eE][-+]?d+)?)"],["(?:[-+]?(?:\\d*\\.\\d+|\\d+\\.?)(?:[eE][-+]?\\d+)?)"]))),Xc=new RegExp("(normal|italic)?\\s*(normal|small-caps)?\\s*(normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900)?\\s*("+We+"(?:px|cm|mm|em|pt|pc|in)*)(?:\\/(normal|"+We+"))?\\s+(.*)"),Yc={cx:V,x:V,r:"radius",cy:wt,y:wt,display:"visible",visibility:"visible",transform:"transformMatrix","fill-opacity":"fillOpacity","fill-rule":"fillRule","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","letter-spacing":"charSpacing","paint-order":"paintFirst","stroke-dasharray":"strokeDashArray","stroke-dashoffset":"strokeDashOffset","stroke-linecap":"strokeLineCap","stroke-linejoin":"strokeLineJoin","stroke-miterlimit":"strokeMiterLimit","stroke-opacity":"strokeOpacity","stroke-width":"strokeWidth","text-decoration":"textDecoration","text-anchor":"textAnchor",opacity:"opacity","clip-path":"clipPath","clip-rule":"clipRule","vector-effect":"strokeUniform","image-rendering":"imageSmoothing"},pr="font-size",mr="clip-path";ks(["path","circle","polygon","polyline","ellipse","rect","line","image","text"]),ks(["symbol","image","marker","pattern","view","svg"]);const Fa=ks(["symbol","g","a","svg","clipPath","defs"]),Gc=new C(1,0),$a=new C,vr=(r,t)=>r.rotate(t),Os=(r,t)=>new C(t).subtract(r),Ms=r=>r.distanceFrom($a),Ds=(r,t)=>Math.atan2(yi(r,t),Ha(r,t)),za=r=>Ds(Gc,r),As=r=>r.eq($a)?r:r.scalarDivide(Ms(r)),yr=function(r){let t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1];return As(new C(-r.y,r.x).scalarMultiply(t?1:-1))},yi=(r,t)=>r.x*t.y-r.y*t.x,Ha=(r,t)=>r.x*t.x+r.y*t.y,br=(r,t,e)=>{if(r.eq(t)||r.eq(e))return!0;const i=yi(t,e),s=yi(t,r),n=yi(e,r);return i>=0?s>=0&&n<=0:!(s<=0&&n>=0)},Na="(-?\\d+(?:\\.\\d*)?(?:px)?(?:\\s?|$))?",Wa=new RegExp("(?:\\s|^)"+Na+Na+"("+We+"?(?:px)?)?(?:\\s?|$)(?:$|\\s)");class ne{constructor(t){const e=typeof t=="string"?ne.parseShadow(t):t;Object.assign(this,ne.ownDefaults,e),this.id=ke()}static parseShadow(t){const e=t.trim(),[,i=0,s=0,n=0]=(Wa.exec(e)||[]).map(o=>parseFloat(o)||0);return{color:(e.replace(Wa,"")||"rgb(0,0,0)").trim(),offsetX:i,offsetY:s,blur:n}}toString(){return[this.offsetX,this.offsetY,this.blur,this.color].join("px ")}toSVG(t){const e=vr(new C(this.offsetX,this.offsetY),Q(-t.angle)),i=new N(this.color);let s=40,n=40;return t.width&&t.height&&(s=100*G((Math.abs(e.x)+this.blur)/t.width,W.NUM_FRACTION_DIGITS)+20,n=100*G((Math.abs(e.y)+this.blur)/t.height,W.NUM_FRACTION_DIGITS)+20),t.flipX&&(e.x*=-1),t.flipY&&(e.y*=-1),'<filter id="SVGID_'.concat(this.id,'" y="-').concat(n,'%" height="').concat(100+2*n,'%" x="-').concat(s,'%" width="').concat(100+2*s,`%" >
	<feGaussianBlur in="SourceAlpha" stdDeviation="`).concat(G(this.blur?this.blur/2:0,W.NUM_FRACTION_DIGITS),`"></feGaussianBlur>
	<feOffset dx="`).concat(G(e.x,W.NUM_FRACTION_DIGITS),'" dy="').concat(G(e.y,W.NUM_FRACTION_DIGITS),`" result="oBlur" ></feOffset>
	<feFlood flood-color="`).concat(i.toRgb(),'" flood-opacity="').concat(i.getAlpha(),`"/>
	<feComposite in2="oBlur" operator="in" />
	<feMerge>
		<feMergeNode></feMergeNode>
		<feMergeNode in="SourceGraphic"></feMergeNode>
	</feMerge>
</filter>
`)}toObject(){const t={color:this.color,blur:this.blur,offsetX:this.offsetX,offsetY:this.offsetY,affectStroke:this.affectStroke,nonScaling:this.nonScaling,type:this.constructor.type},e=ne.ownDefaults;return this.includeDefaultValues?t:lr(t,(i,s)=>i!==e[s])}static async fromObject(t){return new this(t)}}m(ne,"ownDefaults",{color:"rgb(0,0,0)",blur:0,offsetX:0,offsetY:0,affectStroke:!1,includeDefaultValues:!0,nonScaling:!1}),m(ne,"type","shadow"),D.setClass(ne,"shadow");const Ve=(r,t,e)=>Math.max(r,Math.min(t,e)),Uc=[wt,V,mt,Ot,"flipX","flipY","originX","originY","angle","opacity","globalCompositeOperation","shadow","visible",ci,ui],ue=[at,_t,"strokeWidth","strokeDashArray","width","height","paintFirst","strokeUniform","strokeLineCap","strokeDashOffset","strokeLineJoin","strokeMiterLimit","backgroundColor","clipPath"],qc={top:0,left:0,width:0,height:0,angle:0,flipX:!1,flipY:!1,scaleX:1,scaleY:1,minScaleLimit:0,skewX:0,skewY:0,originX:V,originY:wt,strokeWidth:1,strokeUniform:!1,padding:0,opacity:1,paintFirst:at,fill:"rgb(0,0,0)",fillRule:"nonzero",stroke:null,strokeDashArray:null,strokeDashOffset:0,strokeLineCap:"butt",strokeLineJoin:"miter",strokeMiterLimit:4,globalCompositeOperation:"source-over",backgroundColor:"",shadow:null,visible:!0,includeDefaultValues:!0,excludeFromExport:!1,objectCaching:!0,clipPath:void 0,inverted:!1,absolutePositioned:!1,centeredRotation:!0,centeredScaling:!1,dirty:!0},Cr=(r,t,e,i)=>(r<Math.abs(t)?(r=t,i=e/4):i=t===0&&r===0?e/ce*Math.asin(1):e/ce*Math.asin(t/r),{a:r,c:t,p:e,s:i}),Va=(r,t,e,i,s)=>r*Math.pow(2,10*(i-=1))*Math.sin((i*s-t)*ce/e),Xa=(r,t,e,i)=>-e*Math.cos(r/i*Te)+e+t,wr=(r,t,e,i)=>(r/=i)<1/2.75?e*(7.5625*r*r)+t:r<2/2.75?e*(7.5625*(r-=1.5/2.75)*r+.75)+t:r<2.5/2.75?e*(7.5625*(r-=2.25/2.75)*r+.9375)+t:e*(7.5625*(r-=2.625/2.75)*r+.984375)+t,Ya=(r,t,e,i)=>e-wr(i-r,0,e,i)+t;var Kc=Object.freeze({__proto__:null,defaultEasing:Xa,easeInBack:function(r,t,e,i){let s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:1.70158;return e*(r/=i)*r*((s+1)*r-s)+t},easeInBounce:Ya,easeInCirc:(r,t,e,i)=>-e*(Math.sqrt(1-(r/=i)*r)-1)+t,easeInCubic:(r,t,e,i)=>e*(r/i)**3+t,easeInElastic:(r,t,e,i)=>{const s=e;let n=0;if(r===0)return t;if((r/=i)===1)return t+e;n||(n=.3*i);const{a:o,s:a,p:h}=Cr(s,e,n,1.70158);return-Va(o,a,h,r,i)+t},easeInExpo:(r,t,e,i)=>r===0?t:e*2**(10*(r/i-1))+t,easeInOutBack:function(r,t,e,i){let s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:1.70158;return(r/=i/2)<1?e/2*(r*r*((1+(s*=1.525))*r-s))+t:e/2*((r-=2)*r*((1+(s*=1.525))*r+s)+2)+t},easeInOutBounce:(r,t,e,i)=>r<i/2?.5*Ya(2*r,0,e,i)+t:.5*wr(2*r-i,0,e,i)+.5*e+t,easeInOutCirc:(r,t,e,i)=>(r/=i/2)<1?-e/2*(Math.sqrt(1-r**2)-1)+t:e/2*(Math.sqrt(1-(r-=2)*r)+1)+t,easeInOutCubic:(r,t,e,i)=>(r/=i/2)<1?e/2*r**3+t:e/2*((r-2)**3+2)+t,easeInOutElastic:(r,t,e,i)=>{const s=e;let n=0;if(r===0)return t;if((r/=i/2)===2)return t+e;n||(n=i*(.3*1.5));const{a:o,s:a,p:h,c:l}=Cr(s,e,n,1.70158);return r<1?-.5*Va(o,a,h,r,i)+t:o*Math.pow(2,-10*(r-=1))*Math.sin((r*i-a)*ce/h)*.5+l+t},easeInOutExpo:(r,t,e,i)=>r===0?t:r===i?t+e:(r/=i/2)<1?e/2*2**(10*(r-1))+t:e/2*-(2**(-10*--r)+2)+t,easeInOutQuad:(r,t,e,i)=>(r/=i/2)<1?e/2*r**2+t:-e/2*(--r*(r-2)-1)+t,easeInOutQuart:(r,t,e,i)=>(r/=i/2)<1?e/2*r**4+t:-e/2*((r-=2)*r**3-2)+t,easeInOutQuint:(r,t,e,i)=>(r/=i/2)<1?e/2*r**5+t:e/2*((r-2)**5+2)+t,easeInOutSine:(r,t,e,i)=>-e/2*(Math.cos(Math.PI*r/i)-1)+t,easeInQuad:(r,t,e,i)=>e*(r/=i)*r+t,easeInQuart:(r,t,e,i)=>e*(r/=i)*r**3+t,easeInQuint:(r,t,e,i)=>e*(r/i)**5+t,easeInSine:(r,t,e,i)=>-e*Math.cos(r/i*Te)+e+t,easeOutBack:function(r,t,e,i){let s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:1.70158;return e*((r=r/i-1)*r*((s+1)*r+s)+1)+t},easeOutBounce:wr,easeOutCirc:(r,t,e,i)=>e*Math.sqrt(1-(r=r/i-1)*r)+t,easeOutCubic:(r,t,e,i)=>e*((r/i-1)**3+1)+t,easeOutElastic:(r,t,e,i)=>{const s=e;let n=0;if(r===0)return t;if((r/=i)===1)return t+e;n||(n=.3*i);const{a:o,s:a,p:h,c:l}=Cr(s,e,n,1.70158);return o*2**(-10*r)*Math.sin((r*i-a)*ce/h)+l+t},easeOutExpo:(r,t,e,i)=>r===i?t+e:e*-(2**(-10*r/i)+1)+t,easeOutQuad:(r,t,e,i)=>-e*(r/=i)*(r-2)+t,easeOutQuart:(r,t,e,i)=>-e*((r=r/i-1)*r**3-1)+t,easeOutQuint:(r,t,e,i)=>e*((r/i-1)**5+1)+t,easeOutSine:(r,t,e,i)=>e*Math.sin(r/i*Te)+t});const Zc=()=>!1;class xr{constructor(t){let{startValue:e,byValue:i,duration:s=500,delay:n=0,easing:o=Xa,onStart:a=je,onChange:h=je,onComplete:l=je,abort:c=Zc,target:u}=t;m(this,"_state","pending"),m(this,"durationProgress",0),m(this,"valueProgress",0),this.tick=this.tick.bind(this),this.duration=s,this.delay=n,this.easing=o,this._onStart=a,this._onChange=h,this._onComplete=l,this._abort=c,this.target=u,this.startValue=e,this.byValue=i,this.value=this.startValue,this.endValue=Object.freeze(this.calculate(this.duration).value)}get state(){return this._state}isDone(){return this._state==="aborted"||this._state==="completed"}start(){const t=e=>{this._state==="pending"&&(this.startTime=e||+new Date,this._state="running",this._onStart(),this.tick(this.startTime))};this.register(),this.delay>0?setTimeout(()=>Fi(t),this.delay):Fi(t)}tick(t){const e=(t||+new Date)-this.startTime,i=Math.min(e,this.duration);this.durationProgress=i/this.duration;const{value:s,valueProgress:n}=this.calculate(i);this.value=Object.freeze(s),this.valueProgress=n,this._state!=="aborted"&&(this._abort(this.value,this.valueProgress,this.durationProgress)?(this._state="aborted",this.unregister()):e>=this.duration?(this.durationProgress=this.valueProgress=1,this._onChange(this.endValue,this.valueProgress,this.durationProgress),this._state="completed",this._onComplete(this.endValue,this.valueProgress,this.durationProgress),this.unregister()):(this._onChange(this.value,this.valueProgress,this.durationProgress),Fi(this.tick)))}register(){bs.push(this)}unregister(){bs.remove(this)}abort(){this._state="aborted",this.unregister()}}const Jc=["startValue","endValue"];class Qc extends xr{constructor(t){let{startValue:e=0,endValue:i=100}=t;super(y(y({},U(t,Jc)),{},{startValue:e,byValue:i-e}))}calculate(t){const e=this.easing(t,this.startValue,this.byValue,this.duration);return{value:e,valueProgress:Math.abs((e-this.startValue)/this.byValue)}}}const tu=["startValue","endValue"];class eu extends xr{constructor(t){let{startValue:e=[0],endValue:i=[100]}=t;super(y(y({},U(t,tu)),{},{startValue:e,byValue:i.map((s,n)=>s-e[n])}))}calculate(t){const e=this.startValue.map((i,s)=>this.easing(t,i,this.byValue[s],this.duration,s));return{value:e,valueProgress:Math.abs((e[0]-this.startValue[0])/this.byValue[0])}}}const iu=["startValue","endValue","easing","onChange","onComplete","abort"],su=(r,t,e,i)=>t+e*(1-Math.cos(r/i*Te)),_r=r=>r&&((t,e,i)=>r(new N(t).toRgba(),e,i));class nu extends xr{constructor(t){let{startValue:e,endValue:i,easing:s=su,onChange:n,onComplete:o,abort:a}=t,h=U(t,iu);const l=new N(e).getSource(),c=new N(i).getSource();super(y(y({},h),{},{startValue:l,byValue:c.map((u,d)=>u-l[d]),easing:s,onChange:_r(n),onComplete:_r(o),abort:_r(a)}))}calculate(t){const[e,i,s,n]=this.startValue.map((a,h)=>this.easing(t,a,this.byValue[h],this.duration,h)),o=[...[e,i,s].map(Math.round),Ve(0,n,1)];return{value:o,valueProgress:o.map((a,h)=>this.byValue[h]!==0?Math.abs((a-this.startValue[h])/this.byValue[h]):0).find(a=>a!==0)||0}}}function Sr(r){const t=(e=>Array.isArray(e.startValue)||Array.isArray(e.endValue))(r)?new eu(r):new Qc(r);return t.start(),t}function Ga(r){const t=new nu(r);return t.start(),t}class q{constructor(t){this.status=t,this.points=[]}includes(t){return this.points.some(e=>e.eq(t))}append(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return this.points=this.points.concat(e.filter(s=>!this.includes(s))),this}static isPointContained(t,e,i){let s=arguments.length>3&&arguments[3]!==void 0&&arguments[3];if(e.eq(i))return t.eq(e);if(e.x===i.x)return t.x===e.x&&(s||t.y>=Math.min(e.y,i.y)&&t.y<=Math.max(e.y,i.y));if(e.y===i.y)return t.y===e.y&&(s||t.x>=Math.min(e.x,i.x)&&t.x<=Math.max(e.x,i.x));{const n=Os(e,i),o=Os(e,t).divide(n);return s?Math.abs(o.x)===Math.abs(o.y):o.x===o.y&&o.x>=0&&o.x<=1}}static isPointInPolygon(t,e){const i=new C(t).setX(Math.min(t.x-1,...e.map(n=>n.x)));let s=0;for(let n=0;n<e.length;n++){const o=this.intersectSegmentSegment(e[n],e[(n+1)%e.length],t,i);if(o.includes(t))return!0;s+=+(o.status==="Intersection")}return s%2==1}static intersectLineLine(t,e,i,s){let n=!(arguments.length>4&&arguments[4]!==void 0)||arguments[4],o=!(arguments.length>5&&arguments[5]!==void 0)||arguments[5];const a=e.x-t.x,h=e.y-t.y,l=s.x-i.x,c=s.y-i.y,u=t.x-i.x,d=t.y-i.y,g=l*d-c*u,f=a*d-h*u,p=c*a-l*h;if(p!==0){const v=g/p,b=f/p;return(n||0<=v&&v<=1)&&(o||0<=b&&b<=1)?new q("Intersection").append(new C(t.x+v*a,t.y+v*h)):new q}if(g===0||f===0){const v=n||o||q.isPointContained(t,i,s)||q.isPointContained(e,i,s)||q.isPointContained(i,t,e)||q.isPointContained(s,t,e);return new q(v?"Coincident":void 0)}return new q("Parallel")}static intersectSegmentLine(t,e,i,s){return q.intersectLineLine(t,e,i,s,!1,!0)}static intersectSegmentSegment(t,e,i,s){return q.intersectLineLine(t,e,i,s,!1,!1)}static intersectLinePolygon(t,e,i){let s=!(arguments.length>3&&arguments[3]!==void 0)||arguments[3];const n=new q,o=i.length;for(let a,h,l,c=0;c<o;c++){if(a=i[c],h=i[(c+1)%o],l=q.intersectLineLine(t,e,a,h,s,!1),l.status==="Coincident")return l;n.append(...l.points)}return n.points.length>0&&(n.status="Intersection"),n}static intersectSegmentPolygon(t,e,i){return q.intersectLinePolygon(t,e,i,!1)}static intersectPolygonPolygon(t,e){const i=new q,s=t.length,n=[];for(let o=0;o<s;o++){const a=t[o],h=t[(o+1)%s],l=q.intersectSegmentPolygon(a,h,e);l.status==="Coincident"?(n.push(l),i.append(a,h)):i.append(...l.points)}return n.length>0&&n.length===t.length?new q("Coincident"):(i.points.length>0&&(i.status="Intersection"),i)}static intersectPolygonRectangle(t,e,i){const s=e.min(i),n=e.max(i),o=new C(n.x,s.y),a=new C(s.x,n.y);return q.intersectPolygonPolygon(t,[s,o,n,a])}}class ru extends aa{getX(){return this.getXY().x}setX(t){this.setXY(this.getXY().setX(t))}getY(){return this.getXY().y}setY(t){this.setXY(this.getXY().setY(t))}getRelativeX(){return this.left}setRelativeX(t){this.left=t}getRelativeY(){return this.top}setRelativeY(t){this.top=t}getXY(){const t=this.getRelativeXY();return this.group?gt(t,this.group.calcTransformMatrix()):t}setXY(t,e,i){this.group&&(t=gt(t,Dt(this.group.calcTransformMatrix()))),this.setRelativeXY(t,e,i)}getRelativeXY(){return new C(this.left,this.top)}setRelativeXY(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.originX,i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.originY;this.setPositionByOrigin(t,e,i)}isStrokeAccountedForInDimensions(){return!1}getCoords(){const{tl:t,tr:e,br:i,bl:s}=this.aCoords||(this.aCoords=this.calcACoords()),n=[t,e,i,s];if(this.group){const o=this.group.calcTransformMatrix();return n.map(a=>gt(a,o))}return n}intersectsWithRect(t,e){return q.intersectPolygonRectangle(this.getCoords(),t,e).status==="Intersection"}intersectsWithObject(t){const e=q.intersectPolygonPolygon(this.getCoords(),t.getCoords());return e.status==="Intersection"||e.status==="Coincident"||t.isContainedWithinObject(this)||this.isContainedWithinObject(t)}isContainedWithinObject(t){return this.getCoords().every(e=>t.containsPoint(e))}isContainedWithinRect(t,e){const{left:i,top:s,width:n,height:o}=this.getBoundingRect();return i>=t.x&&i+n<=e.x&&s>=t.y&&s+o<=e.y}isOverlapping(t){return this.intersectsWithObject(t)||this.isContainedWithinObject(t)||t.isContainedWithinObject(this)}containsPoint(t){return q.isPointInPolygon(t,this.getCoords())}isOnScreen(){if(!this.canvas)return!1;const{tl:t,br:e}=this.canvas.vptCoords;return!!this.getCoords().some(i=>i.x<=e.x&&i.x>=t.x&&i.y<=e.y&&i.y>=t.y)||!!this.intersectsWithRect(t,e)||this.containsPoint(t.midPointFrom(e))}isPartiallyOnScreen(){if(!this.canvas)return!1;const{tl:t,br:e}=this.canvas.vptCoords;return this.intersectsWithRect(t,e)?!0:this.getCoords().every(i=>(i.x>=e.x||i.x<=t.x)&&(i.y>=e.y||i.y<=t.y))&&this.containsPoint(t.midPointFrom(e))}getBoundingRect(){return se(this.getCoords())}getScaledWidth(){return this._getTransformedDimensions().x}getScaledHeight(){return this._getTransformedDimensions().y}scale(t){this._set(mt,t),this._set(Ot,t),this.setCoords()}scaleToWidth(t){const e=this.getBoundingRect().width/this.getScaledWidth();return this.scale(t/this.width/e)}scaleToHeight(t){const e=this.getBoundingRect().height/this.getScaledHeight();return this.scale(t/this.height/e)}getCanvasRetinaScaling(){var t;return((t=this.canvas)===null||t===void 0?void 0:t.getRetinaScaling())||1}getTotalAngle(){return this.group?ie(ua(this.calcTransformMatrix())):this.angle}getViewportTransform(){var t;return((t=this.canvas)===null||t===void 0?void 0:t.viewportTransform)||pt.concat()}calcACoords(){const t=Fe({angle:this.angle}),{x:e,y:i}=this.getRelativeCenterPoint(),s=fi(e,i),n=nt(s,t),o=this._getTransformedDimensions(),a=o.x/2,h=o.y/2;return{tl:gt({x:-a,y:-h},n),tr:gt({x:a,y:-h},n),bl:gt({x:-a,y:h},n),br:gt({x:a,y:h},n)}}setCoords(){this.aCoords=this.calcACoords()}transformMatrixKey(){let t=arguments.length>0&&arguments[0]!==void 0&&arguments[0],e=[];return!t&&this.group&&(e=this.group.transformMatrixKey(t)),e.push(this.top,this.left,this.width,this.height,this.scaleX,this.scaleY,this.angle,this.strokeWidth,this.skewX,this.skewY,+this.flipX,+this.flipY,rt(this.originX),rt(this.originY)),e}calcTransformMatrix(){let t=arguments.length>0&&arguments[0]!==void 0&&arguments[0],e=this.calcOwnMatrix();if(t||!this.group)return e;const i=this.transformMatrixKey(t),s=this.matrixCache;return s&&s.key.every((n,o)=>n===i[o])?s.value:(this.group&&(e=nt(this.group.calcTransformMatrix(!1),e)),this.matrixCache={key:i,value:e},e)}calcOwnMatrix(){const t=this.transformMatrixKey(!0),e=this.ownMatrixCache;if(e&&e.key===t)return e.value;const i=this.getRelativeCenterPoint(),s={angle:this.angle,translateX:i.x,translateY:i.y,scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,flipX:this.flipX,flipY:this.flipY},n=ga(s);return this.ownMatrixCache={key:t,value:n},n}_getNonTransformedDimensions(){return new C(this.width,this.height).scalarAdd(this.strokeWidth)}_calculateCurrentDimensions(t){return this._getTransformedDimensions(t).transform(this.getViewportTransform(),!0).scalarAdd(2*this.padding)}_getTransformedDimensions(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const e=y({scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,width:this.width,height:this.height,strokeWidth:this.strokeWidth},t),i=e.strokeWidth;let s=i,n=0;this.strokeUniform&&(s=0,n=i);const o=e.width+s,a=e.height+s;let h;return h=e.skewX===0&&e.skewY===0?new C(o*e.scaleX,a*e.scaleY):Ss(o,a,$i(e)),h.scalarAdd(n)}translateToGivenOrigin(t,e,i,s,n){let o=t.x,a=t.y;const h=rt(s)-rt(e),l=rt(n)-rt(i);if(h||l){const c=this._getTransformedDimensions();o+=h*c.x,a+=l*c.y}return new C(o,a)}translateToCenterPoint(t,e,i){if(e===j&&i===j)return t;const s=this.translateToGivenOrigin(t,e,i,j,j);return this.angle?s.rotate(Q(this.angle),t):s}translateToOriginPoint(t,e,i){const s=this.translateToGivenOrigin(t,j,j,e,i);return this.angle?s.rotate(Q(this.angle),t):s}getCenterPoint(){const t=this.getRelativeCenterPoint();return this.group?gt(t,this.group.calcTransformMatrix()):t}getRelativeCenterPoint(){return this.translateToCenterPoint(new C(this.left,this.top),this.originX,this.originY)}getPointByOrigin(t,e){return this.translateToOriginPoint(this.getRelativeCenterPoint(),t,e)}setPositionByOrigin(t,e,i){const s=this.translateToCenterPoint(t,e,i),n=this.translateToOriginPoint(s,this.originX,this.originY);this.set({left:n.x,top:n.y})}_getLeftTopCoords(){return this.translateToOriginPoint(this.getRelativeCenterPoint(),V,wt)}}const ou=["type"],au=["extraParam"];let de=class An extends ru{static getDefaults(){return An.ownDefaults}get type(){const t=this.constructor.type;return t==="FabricObject"?"object":t.toLowerCase()}set type(t){Se("warn","Setting type has no effect",t)}constructor(t){super(),m(this,"_cacheContext",null),Object.assign(this,An.ownDefaults),this.setOptions(t)}_createCacheCanvas(){this._cacheCanvas=jt(),this._cacheContext=this._cacheCanvas.getContext("2d"),this._updateCacheCanvas(),this.dirty=!0}_limitCacheSize(t){const e=t.width,i=t.height,s=W.maxCacheSideLimit,n=W.minCacheSideLimit;if(e<=s&&i<=s&&e*i<=W.perfLimitSizeTotal)return e<n&&(t.width=n),i<n&&(t.height=n),t;const o=e/i,[a,h]=ji.limitDimsByArea(o),l=Ve(n,a,s),c=Ve(n,h,s);return e>l&&(t.zoomX/=e/l,t.width=l,t.capped=!0),i>c&&(t.zoomY/=i/c,t.height=c,t.capped=!0),t}_getCacheCanvasDimensions(){const t=this.getTotalObjectScaling(),e=this._getTransformedDimensions({skewX:0,skewY:0}),i=e.x*t.x/this.scaleX,s=e.y*t.y/this.scaleY;return{width:Math.ceil(i+2),height:Math.ceil(s+2),zoomX:t.x,zoomY:t.y,x:i,y:s}}_updateCacheCanvas(){const t=this._cacheCanvas,e=this._cacheContext,{width:i,height:s,zoomX:n,zoomY:o,x:a,y:h}=this._limitCacheSize(this._getCacheCanvasDimensions()),l=i!==t.width||s!==t.height,c=this.zoomX!==n||this.zoomY!==o;if(!t||!e)return!1;if(l||c){i!==t.width||s!==t.height?(t.width=i,t.height=s):(e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,t.width,t.height));const u=a/2,d=h/2;return this.cacheTranslationX=Math.round(t.width/2-u)+u,this.cacheTranslationY=Math.round(t.height/2-d)+d,e.translate(this.cacheTranslationX,this.cacheTranslationY),e.scale(n,o),this.zoomX=n,this.zoomY=o,!0}return!1}setOptions(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this._setOptions(t)}transform(t){const e=this.group&&!this.group._transformDone||this.group&&this.canvas&&t===this.canvas.contextTop,i=this.calcTransformMatrix(!e);t.transform(i[0],i[1],i[2],i[3],i[4],i[5])}getObjectScaling(){if(!this.group)return new C(Math.abs(this.scaleX),Math.abs(this.scaleY));const t=gi(this.calcTransformMatrix());return new C(Math.abs(t.scaleX),Math.abs(t.scaleY))}getTotalObjectScaling(){const t=this.getObjectScaling();if(this.canvas){const e=this.canvas.getZoom(),i=this.getCanvasRetinaScaling();return t.scalarMultiply(e*i)}return t}getObjectOpacity(){let t=this.opacity;return this.group&&(t*=this.group.getObjectOpacity()),t}_constrainScale(t){return Math.abs(t)<this.minScaleLimit?t<0?-this.minScaleLimit:this.minScaleLimit:t===0?1e-4:t}_set(t,e){t!==mt&&t!==Ot||(e=this._constrainScale(e)),t===mt&&e<0?(this.flipX=!this.flipX,e*=-1):t==="scaleY"&&e<0?(this.flipY=!this.flipY,e*=-1):t!=="shadow"||!e||e instanceof ne||(e=new ne(e));const i=this[t]!==e;return this[t]=e,i&&this.constructor.cacheProperties.includes(t)&&(this.dirty=!0),this.parent&&(this.dirty||i&&this.constructor.stateProperties.includes(t))&&this.parent._set("dirty",!0),this}isNotVisible(){return this.opacity===0||!this.width&&!this.height&&this.strokeWidth===0||!this.visible}render(t){this.isNotVisible()||this.canvas&&this.canvas.skipOffscreen&&!this.group&&!this.isOnScreen()||(t.save(),this._setupCompositeOperation(t),this.drawSelectionBackground(t),this.transform(t),this._setOpacity(t),this._setShadow(t),this.shouldCache()?(this.renderCache(),this.drawCacheOnCanvas(t)):(this._removeCacheCanvas(),this.drawObject(t,!1,{}),this.dirty=!1),t.restore())}drawSelectionBackground(t){}renderCache(t){if(t=t||{},this._cacheCanvas&&this._cacheContext||this._createCacheCanvas(),this.isCacheDirty()&&this._cacheContext){const{zoomX:e,zoomY:i,cacheTranslationX:s,cacheTranslationY:n}=this,{width:o,height:a}=this._cacheCanvas;this.drawObject(this._cacheContext,t.forClipping,{zoomX:e,zoomY:i,cacheTranslationX:s,cacheTranslationY:n,width:o,height:a,parentClipPaths:[]}),this.dirty=!1}}_removeCacheCanvas(){this._cacheCanvas=void 0,this._cacheContext=null}hasStroke(){return this.stroke&&this.stroke!=="transparent"&&this.strokeWidth!==0}hasFill(){return this.fill&&this.fill!=="transparent"}needsItsOwnCache(){return!!(this.paintFirst===_t&&this.hasFill()&&this.hasStroke()&&this.shadow)||!!this.clipPath}shouldCache(){return this.ownCaching=this.objectCaching&&(!this.parent||!this.parent.isOnACache())||this.needsItsOwnCache(),this.ownCaching}willDrawShadow(){return!!this.shadow&&(this.shadow.offsetX!==0||this.shadow.offsetY!==0)}drawClipPathOnCache(t,e,i){t.save(),e.inverted?t.globalCompositeOperation="destination-out":t.globalCompositeOperation="destination-in",t.setTransform(1,0,0,1,0,0),t.drawImage(i,0,0),t.restore()}drawObject(t,e,i){const s=this.fill,n=this.stroke;e?(this.fill="black",this.stroke="",this._setClippingProperties(t)):this._renderBackground(t),this._render(t),this._drawClipPath(t,this.clipPath,i),this.fill=s,this.stroke=n}createClipPathLayer(t,e){const i=Mt(e),s=i.getContext("2d");if(s.translate(e.cacheTranslationX,e.cacheTranslationY),s.scale(e.zoomX,e.zoomY),t._cacheCanvas=i,e.parentClipPaths.forEach(n=>{n.transform(s)}),e.parentClipPaths.push(t),t.absolutePositioned){const n=Dt(this.calcTransformMatrix());s.transform(n[0],n[1],n[2],n[3],n[4],n[5])}return t.transform(s),t.drawObject(s,!0,e),i}_drawClipPath(t,e,i){if(!e)return;e._transformDone=!0;const s=this.createClipPathLayer(e,i);this.drawClipPathOnCache(t,e,s)}drawCacheOnCanvas(t){t.scale(1/this.zoomX,1/this.zoomY),t.drawImage(this._cacheCanvas,-this.cacheTranslationX,-this.cacheTranslationY)}isCacheDirty(){let t=arguments.length>0&&arguments[0]!==void 0&&arguments[0];if(this.isNotVisible())return!1;const e=this._cacheCanvas,i=this._cacheContext;return!(!e||!i||t||!this._updateCacheCanvas())||!!(this.dirty||this.clipPath&&this.clipPath.absolutePositioned)&&(e&&i&&!t&&(i.save(),i.setTransform(1,0,0,1,0,0),i.clearRect(0,0,e.width,e.height),i.restore()),!0)}_renderBackground(t){if(!this.backgroundColor)return;const e=this._getNonTransformedDimensions();t.fillStyle=this.backgroundColor,t.fillRect(-e.x/2,-e.y/2,e.x,e.y),this._removeShadow(t)}_setOpacity(t){this.group&&!this.group._transformDone?t.globalAlpha=this.getObjectOpacity():t.globalAlpha*=this.opacity}_setStrokeStyles(t,e){const i=e.stroke;i&&(t.lineWidth=e.strokeWidth,t.lineCap=e.strokeLineCap,t.lineDashOffset=e.strokeDashOffset,t.lineJoin=e.strokeLineJoin,t.miterLimit=e.strokeMiterLimit,Bt(i)?i.gradientUnits==="percentage"||i.gradientTransform||i.patternTransform?this._applyPatternForTransformedGradient(t,i):(t.strokeStyle=i.toLive(t),this._applyPatternGradientTransform(t,i)):t.strokeStyle=e.stroke)}_setFillStyles(t,e){let{fill:i}=e;i&&(Bt(i)?(t.fillStyle=i.toLive(t),this._applyPatternGradientTransform(t,i)):t.fillStyle=i)}_setClippingProperties(t){t.globalAlpha=1,t.strokeStyle="transparent",t.fillStyle="#000000"}_setLineDash(t,e){e&&e.length!==0&&t.setLineDash(e)}_setShadow(t){if(!this.shadow)return;const e=this.shadow,i=this.canvas,s=this.getCanvasRetinaScaling(),[n,,,o]=(i==null?void 0:i.viewportTransform)||pt,a=n*s,h=o*s,l=e.nonScaling?new C(1,1):this.getObjectScaling();t.shadowColor=e.color,t.shadowBlur=e.blur*W.browserShadowBlurConstant*(a+h)*(l.x+l.y)/4,t.shadowOffsetX=e.offsetX*a*l.x,t.shadowOffsetY=e.offsetY*h*l.y}_removeShadow(t){this.shadow&&(t.shadowColor="",t.shadowBlur=t.shadowOffsetX=t.shadowOffsetY=0)}_applyPatternGradientTransform(t,e){if(!Bt(e))return{offsetX:0,offsetY:0};const i=e.gradientTransform||e.patternTransform,s=-this.width/2+e.offsetX||0,n=-this.height/2+e.offsetY||0;return e.gradientUnits==="percentage"?t.transform(this.width,0,0,this.height,s,n):t.transform(1,0,0,1,s,n),i&&t.transform(i[0],i[1],i[2],i[3],i[4],i[5]),{offsetX:s,offsetY:n}}_renderPaintInOrder(t){this.paintFirst===_t?(this._renderStroke(t),this._renderFill(t)):(this._renderFill(t),this._renderStroke(t))}_render(t){}_renderFill(t){this.fill&&(t.save(),this._setFillStyles(t,this),this.fillRule==="evenodd"?t.fill("evenodd"):t.fill(),t.restore())}_renderStroke(t){if(this.stroke&&this.strokeWidth!==0){if(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(t),t.save(),this.strokeUniform){const e=this.getObjectScaling();t.scale(1/e.x,1/e.y)}this._setLineDash(t,this.strokeDashArray),this._setStrokeStyles(t,this),t.stroke(),t.restore()}}_applyPatternForTransformedGradient(t,e){var i;const s=this._limitCacheSize(this._getCacheCanvasDimensions()),n=this.getCanvasRetinaScaling(),o=s.x/this.scaleX/n,a=s.y/this.scaleY/n,h=Mt({width:Math.ceil(o),height:Math.ceil(a)}),l=h.getContext("2d");l&&(l.beginPath(),l.moveTo(0,0),l.lineTo(o,0),l.lineTo(o,a),l.lineTo(0,a),l.closePath(),l.translate(o/2,a/2),l.scale(s.zoomX/this.scaleX/n,s.zoomY/this.scaleY/n),this._applyPatternGradientTransform(l,e),l.fillStyle=e.toLive(t),l.fill(),t.translate(-this.width/2-this.strokeWidth/2,-this.height/2-this.strokeWidth/2),t.scale(n*this.scaleX/s.zoomX,n*this.scaleY/s.zoomY),t.strokeStyle=(i=l.createPattern(h,"no-repeat"))!==null&&i!==void 0?i:"")}_findCenterFromElement(){return new C(this.left+this.width/2,this.top+this.height/2)}clone(t){const e=this.toObject(t);return this.constructor.fromObject(e)}cloneAsImage(t){const e=this.toCanvasElement(t);return new(D.getClass("image"))(e)}toCanvasElement(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const e=ur(this),i=this.group,s=this.shadow,n=Math.abs,o=t.enableRetinaScaling?ia():1,a=(t.multiplier||1)*o,h=t.canvasProvider||(w=>new Ni(w,{enableRetinaScaling:!1,renderOnAddRemove:!1,skipOffscreen:!1}));delete this.group,t.withoutTransform&&Sa(this),t.withoutShadow&&(this.shadow=null),t.viewportTransform&&dr(this,this.getViewportTransform()),this.setCoords();const l=jt(),c=this.getBoundingRect(),u=this.shadow,d=new C;if(u){const w=u.blur,x=u.nonScaling?new C(1,1):this.getObjectScaling();d.x=2*Math.round(n(u.offsetX)+w)*n(x.x),d.y=2*Math.round(n(u.offsetY)+w)*n(x.y)}const g=c.width+d.x,f=c.height+d.y;l.width=Math.ceil(g),l.height=Math.ceil(f);const p=h(l);t.format==="jpeg"&&(p.backgroundColor="#fff"),this.setPositionByOrigin(new C(p.width/2,p.height/2),j,j);const v=this.canvas;p._objects=[this],this.set("canvas",p),this.setCoords();const b=p.toCanvasElement(a||1,t);return this.set("canvas",v),this.shadow=s,i&&(this.group=i),this.set(e),this.setCoords(),p._objects=[],p.destroy(),b}toDataURL(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return rr(this.toCanvasElement(t),t.format||"png",t.quality||1)}toBlob(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return or(this.toCanvasElement(t),t.format||"png",t.quality||1)}isType(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.includes(this.constructor.type)||e.includes(this.type)}complexity(){return 1}toJSON(){return this.toObject()}rotate(t){const{centeredRotation:e,originX:i,originY:s}=this;if(e){const{x:n,y:o}=this.getRelativeCenterPoint();this.originX=j,this.originY=j,this.left=n,this.top=o}if(this.set("angle",t),e){const{x:n,y:o}=this.translateToOriginPoint(this.getRelativeCenterPoint(),i,s);this.left=n,this.top=o,this.originX=i,this.originY=s}}setOnGroup(){}_setupCompositeOperation(t){this.globalCompositeOperation&&(t.globalCompositeOperation=this.globalCompositeOperation)}dispose(){bs.cancelByTarget(this),this.off(),this._set("canvas",void 0),this._cacheCanvas&&ee().dispose(this._cacheCanvas),this._cacheCanvas=void 0,this._cacheContext=null}animate(t,e){return Object.entries(t).reduce((i,s)=>{let[n,o]=s;return i[n]=this._animate(n,o,e),i},{})}_animate(t,e){let i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const s=t.split("."),n=this.constructor.colorProperties.includes(s[s.length-1]),{abort:o,startValue:a,onChange:h,onComplete:l}=i,c=y(y({},i),{},{target:this,startValue:a??s.reduce((u,d)=>u[d],this),endValue:e,abort:o==null?void 0:o.bind(this),onChange:(u,d,g)=>{s.reduce((f,p,v)=>(v===s.length-1&&(f[p]=u),f[p]),this),h&&h(u,d,g)},onComplete:(u,d,g)=>{this.setCoords(),l&&l(u,d,g)}});return n?Ga(c):Sr(c)}isDescendantOf(t){const{parent:e,group:i}=this;return e===t||i===t||!!e&&e.isDescendantOf(t)||!!i&&i!==e&&i.isDescendantOf(t)}getAncestors(){const t=[];let e=this;do e=e.parent,e&&t.push(e);while(e);return t}findCommonAncestors(t){if(this===t)return{fork:[],otherFork:[],common:[this,...this.getAncestors()]};const e=this.getAncestors(),i=t.getAncestors();if(e.length===0&&i.length>0&&this===i[i.length-1])return{fork:[],otherFork:[t,...i.slice(0,i.length-1)],common:[this]};for(let s,n=0;n<e.length;n++){if(s=e[n],s===t)return{fork:[this,...e.slice(0,n)],otherFork:[],common:e.slice(n)};for(let o=0;o<i.length;o++){if(this===i[o])return{fork:[],otherFork:[t,...i.slice(0,o)],common:[this,...e]};if(s===i[o])return{fork:[this,...e.slice(0,n)],otherFork:[t,...i.slice(0,o)],common:e.slice(n)}}}return{fork:[this,...e],otherFork:[t,...i],common:[]}}hasCommonAncestors(t){const e=this.findCommonAncestors(t);return e&&!!e.common.length}isInFrontOf(t){if(this===t)return;const e=this.findCommonAncestors(t);if(e.fork.includes(t))return!0;if(e.otherFork.includes(this))return!1;const i=e.common[0]||this.canvas;if(!i)return;const s=e.fork.pop(),n=e.otherFork.pop(),o=i._objects.indexOf(s),a=i._objects.indexOf(n);return o>-1&&o>a}toObject(){const t=(arguments.length>0&&arguments[0]!==void 0?arguments[0]:[]).concat(An.customProperties,this.constructor.customProperties||[]);let e;const i=W.NUM_FRACTION_DIGITS,{clipPath:s,fill:n,stroke:o,shadow:a,strokeDashArray:h,left:l,top:c,originX:u,originY:d,width:g,height:f,strokeWidth:p,strokeLineCap:v,strokeDashOffset:b,strokeLineJoin:w,strokeUniform:x,strokeMiterLimit:_,scaleX:k,scaleY:A,angle:L,flipX:O,flipY:I,opacity:F,visible:tt,backgroundColor:Z,fillRule:z,paintFirst:Y,globalCompositeOperation:ct,skewX:S,skewY:T}=this;s&&!s.excludeFromExport&&(e=s.toObject(t.concat("inverted","absolutePositioned")));const E=P=>G(P,i),R=y(y({},$e(this,t)),{},{type:this.constructor.type,version:Zn,originX:u,originY:d,left:E(l),top:E(c),width:E(g),height:E(f),fill:fa(n)?n.toObject():n,stroke:fa(o)?o.toObject():o,strokeWidth:E(p),strokeDashArray:h&&h.concat(),strokeLineCap:v,strokeDashOffset:b,strokeLineJoin:w,strokeUniform:x,strokeMiterLimit:E(_),scaleX:E(k),scaleY:E(A),angle:E(L),flipX:O,flipY:I,opacity:E(F),shadow:a&&a.toObject(),visible:tt,backgroundColor:Z,fillRule:z,paintFirst:Y,globalCompositeOperation:ct,skewX:E(S),skewY:E(T)},e?{clipPath:e}:null);return this.includeDefaultValues?R:this._removeDefaultValues(R)}toDatalessObject(t){return this.toObject(t)}_removeDefaultValues(t){const e=this.constructor.getDefaults(),i=Object.keys(e).length>0?e:Object.getPrototypeOf(this);return lr(t,(s,n)=>{if(n===V||n===wt||n==="type")return!0;const o=i[n];return s!==o&&!(Array.isArray(s)&&Array.isArray(o)&&s.length===0&&o.length===0)})}toString(){return"#<".concat(this.constructor.type,">")}static _fromObject(t){let e=U(t,ou),i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{extraParam:s}=i,n=U(i,au);return Hi(e,n).then(o=>s?(delete o[s],new this(e[s],o)):new this(o))}static fromObject(t,e){return this._fromObject(t,e)}};m(de,"stateProperties",Uc),m(de,"cacheProperties",ue),m(de,"ownDefaults",qc),m(de,"type","FabricObject"),m(de,"colorProperties",[at,_t,"backgroundColor"]),m(de,"customProperties",[]),D.setClass(de),D.setClass(de,"object");const bi=(r,t,e)=>(i,s,n,o)=>{const a=t(i,s,n,o);return a&&Ea(r,y(y({},Ma(i,s,n,o)),e)),a};function Ci(r){return(t,e,i,s)=>{const{target:n,originX:o,originY:a}=e,h=n.getRelativeCenterPoint(),l=n.translateToOriginPoint(h,o,a),c=r(t,e,i,s);return n.setPositionByOrigin(l,e.originX,e.originY),c}}const Ua=bi(Bi,Ci((r,t,e,i)=>{const s=gr(t,t.originX,t.originY,e,i);if(rt(t.originX)===rt(j)||rt(t.originX)===rt(it)&&s.x<0||rt(t.originX)===rt(V)&&s.x>0){const{target:n}=t,o=n.strokeWidth/(n.strokeUniform?n.scaleX:1),a=ka(t)?2:1,h=n.width,l=Math.abs(s.x*a/n.scaleX)-o;return n.set("width",Math.max(l,1)),h!==n.width}return!1}));function hu(r,t,e,i,s){i=i||{};const n=this.sizeX||i.cornerSize||s.cornerSize,o=this.sizeY||i.cornerSize||s.cornerSize,a=i.transparentCorners!==void 0?i.transparentCorners:s.transparentCorners,h=a?_t:at,l=!a&&(i.cornerStrokeColor||s.cornerStrokeColor);let c,u=t,d=e;r.save(),r.fillStyle=i.cornerColor||s.cornerColor||"",r.strokeStyle=i.cornerStrokeColor||s.cornerStrokeColor||"",n>o?(c=n,r.scale(1,o/n),d=e*n/o):o>n?(c=o,r.scale(n/o,1),u=t*o/n):c=n,r.beginPath(),r.arc(u,d,c/2,0,ce,!1),r[h](),l&&r.stroke(),r.restore()}function lu(r,t,e,i,s){i=i||{};const n=this.sizeX||i.cornerSize||s.cornerSize,o=this.sizeY||i.cornerSize||s.cornerSize,a=i.transparentCorners!==void 0?i.transparentCorners:s.transparentCorners,h=a?_t:at,l=!a&&(i.cornerStrokeColor||s.cornerStrokeColor),c=n/2,u=o/2;r.save(),r.fillStyle=i.cornerColor||s.cornerColor||"",r.strokeStyle=i.cornerStrokeColor||s.cornerStrokeColor||"",r.translate(t,e);const d=s.getTotalAngle();r.rotate(Q(d)),r["".concat(h,"Rect")](-c,-u,n,o),l&&r.strokeRect(-c,-u,n,o),r.restore()}class At{constructor(t){m(this,"visible",!0),m(this,"actionName",vs),m(this,"angle",0),m(this,"x",0),m(this,"y",0),m(this,"offsetX",0),m(this,"offsetY",0),m(this,"sizeX",0),m(this,"sizeY",0),m(this,"touchSizeX",0),m(this,"touchSizeY",0),m(this,"cursorStyle","crosshair"),m(this,"withConnection",!1),Object.assign(this,t)}shouldActivate(t,e,i,s){var n;let{tl:o,tr:a,br:h,bl:l}=s;return((n=e.canvas)===null||n===void 0?void 0:n.getActiveObject())===e&&e.isControlVisible(t)&&q.isPointInPolygon(i,[o,a,h,l])}getActionHandler(t,e,i){return this.actionHandler}getMouseDownHandler(t,e,i){return this.mouseDownHandler}getMouseUpHandler(t,e,i){return this.mouseUpHandler}cursorStyleHandler(t,e,i){return e.cursorStyle}getActionName(t,e,i){return e.actionName}getVisibility(t,e){var i,s;return(i=(s=t._controlsVisibility)===null||s===void 0?void 0:s[e])!==null&&i!==void 0?i:this.visible}setVisibility(t,e,i){this.visible=t}positionHandler(t,e,i,s){return new C(this.x*t.x+this.offsetX,this.y*t.y+this.offsetY).transform(e)}calcCornerCoords(t,e,i,s,n,o){const a=ws([fi(i,s),Fe({angle:t}),xs((n?this.touchSizeX:this.sizeX)||e,(n?this.touchSizeY:this.sizeY)||e)]);return{tl:new C(-.5,-.5).transform(a),tr:new C(.5,-.5).transform(a),br:new C(.5,.5).transform(a),bl:new C(-.5,.5).transform(a)}}render(t,e,i,s,n){((s=s||{}).cornerStyle||n.cornerStyle)==="circle"?hu.call(this,t,e,i,s,n):lu.call(this,t,e,i,s,n)}}const cu=(r,t,e)=>e.lockRotation?Ts:t.cursorStyle,uu=bi(na,Ci((r,t,e,i)=>{let{target:s,ex:n,ey:o,theta:a,originX:h,originY:l}=t;const c=s.translateToOriginPoint(s.getRelativeCenterPoint(),h,l);if(Gt(s,"lockRotation"))return!1;const u=Math.atan2(o-c.y,n-c.x),d=Math.atan2(i-c.y,e-c.x);let g=ie(d-u+a);if(s.snapAngle&&s.snapAngle>0){const p=s.snapAngle,v=s.snapThreshold||p,b=Math.ceil(g/p)*p,w=Math.floor(g/p)*p;Math.abs(g-w)<v?g=w:Math.abs(g-b)<v&&(g=b)}g<0&&(g=360+g),g%=360;const f=s.angle!==g;return s.angle=g,f}));function qa(r,t){const e=t.canvas,i=r[e.uniScaleKey];return e.uniformScaling&&!i||!e.uniformScaling&&i}function Ka(r,t,e){const i=Gt(r,"lockScalingX"),s=Gt(r,"lockScalingY");if(i&&s||!t&&(i||s)&&e||i&&t==="x"||s&&t==="y")return!0;const{width:n,height:o,strokeWidth:a}=r;return n===0&&a===0&&t!=="y"||o===0&&a===0&&t!=="x"}const du=["e","se","s","sw","w","nw","n","ne","e"],Xi=(r,t,e)=>{const i=qa(r,e);if(Ka(e,t.x!==0&&t.y===0?"x":t.x===0&&t.y!==0?"y":"",i))return Ts;const s=Da(e,t);return"".concat(du[s],"-resize")};function Tr(r,t,e,i){let s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{};const n=t.target,o=s.by,a=qa(r,n);let h,l,c,u,d,g;if(Ka(n,o,a))return!1;if(t.gestureScale)l=t.scaleX*t.gestureScale,c=t.scaleY*t.gestureScale;else{if(h=gr(t,t.originX,t.originY,e,i),d=o!=="y"?Math.sign(h.x||t.signX||1):1,g=o!=="x"?Math.sign(h.y||t.signY||1):1,t.signX||(t.signX=d),t.signY||(t.signY=g),Gt(n,"lockScalingFlip")&&(t.signX!==d||t.signY!==g))return!1;if(u=n._getTransformedDimensions(),a&&!o){const v=Math.abs(h.x)+Math.abs(h.y),{original:b}=t,w=v/(Math.abs(u.x*b.scaleX/n.scaleX)+Math.abs(u.y*b.scaleY/n.scaleY));l=b.scaleX*w,c=b.scaleY*w}else l=Math.abs(h.x*n.scaleX/u.x),c=Math.abs(h.y*n.scaleY/u.y);ka(t)&&(l*=2,c*=2),t.signX!==d&&o!=="y"&&(t.originX=Oa(t.originX),l*=-1,t.signX=d),t.signY!==g&&o!=="x"&&(t.originY=Oa(t.originY),c*=-1,t.signY=g)}const f=n.scaleX,p=n.scaleY;return o?(o==="x"&&n.set(mt,l),o==="y"&&n.set(Ot,c)):(!Gt(n,"lockScalingX")&&n.set(mt,l),!Gt(n,"lockScalingY")&&n.set(Ot,c)),f!==n.scaleX||p!==n.scaleY}const Ps=bi(ps,Ci((r,t,e,i)=>Tr(r,t,e,i))),gu=bi(ps,Ci((r,t,e,i)=>Tr(r,t,e,i,{by:"x"}))),fu=bi(ps,Ci((r,t,e,i)=>Tr(r,t,e,i,{by:"y"}))),pu=["target","ex","ey","skewingSide"],Er={x:{counterAxis:"y",scale:mt,skew:ci,lockSkewing:"lockSkewingX",origin:"originX",flip:"flipX"},y:{counterAxis:"x",scale:Ot,skew:ui,lockSkewing:"lockSkewingY",origin:"originY",flip:"flipY"}},mu=["ns","nesw","ew","nwse"],vu=(r,t,e)=>{if(t.x!==0&&Gt(e,"lockSkewingY")||t.y!==0&&Gt(e,"lockSkewingX"))return Ts;const i=Da(e,t)%4;return"".concat(mu[i],"-resize")};function Za(r,t,e,i,s){const{target:n}=e,{counterAxis:o,origin:a,lockSkewing:h,skew:l,flip:c}=Er[r];if(Gt(n,h))return!1;const{origin:u,flip:d}=Er[o],g=rt(e[u])*(n[d]?-1:1),f=-Math.sign(g)*(n[c]?-1:1),p=.5*-((n[l]===0&&gr(e,j,j,i,s)[r]>0||n[l]>0?1:-1)*f)+.5;return bi(ra,Ci((b,w,x,_)=>function(k,A,L){let{target:O,ex:I,ey:F,skewingSide:tt}=A,Z=U(A,pu);const{skew:z}=Er[k],Y=L.subtract(new C(I,F)).divide(new C(O.scaleX,O.scaleY))[k],ct=O[z],S=Z[z],T=Math.tan(Q(S)),E=k==="y"?O._getTransformedDimensions({scaleX:1,scaleY:1,skewX:0}).x:O._getTransformedDimensions({scaleX:1,scaleY:1}).y,R=2*Y*tt/Math.max(E,1)+T,P=ie(Math.atan(R));O.set(z,P);const H=ct!==O[z];if(H&&k==="y"){const{skewX:J,scaleX:lt}=O,et=O._getTransformedDimensions({skewY:ct}),Jt=O._getTransformedDimensions(),Rt=J!==0?et.x/Jt.x:1;Rt!==1&&O.set(mt,Rt*lt)}return H}(r,w,new C(x,_))))(t,y(y({},e),{},{[a]:p,skewingSide:f}),i,s)}const yu=(r,t,e,i)=>Za("x",r,t,e,i),bu=(r,t,e,i)=>Za("y",r,t,e,i);function Ls(r,t){return r[t.canvas.altActionKey]}const Rs=(r,t,e)=>{const i=Ls(r,e);return t.x===0?i?ci:Ot:t.y===0?i?ui:mt:""},wi=(r,t,e)=>Ls(r,e)?vu(0,t,e):Xi(r,t,e),Ja=(r,t,e,i)=>Ls(r,t.target)?bu(r,t,e,i):gu(r,t,e,i),Qa=(r,t,e,i)=>Ls(r,t.target)?yu(r,t,e,i):fu(r,t,e,i),th=()=>({ml:new At({x:-.5,y:0,cursorStyleHandler:wi,actionHandler:Ja,getActionName:Rs}),mr:new At({x:.5,y:0,cursorStyleHandler:wi,actionHandler:Ja,getActionName:Rs}),mb:new At({x:0,y:.5,cursorStyleHandler:wi,actionHandler:Qa,getActionName:Rs}),mt:new At({x:0,y:-.5,cursorStyleHandler:wi,actionHandler:Qa,getActionName:Rs}),tl:new At({x:-.5,y:-.5,cursorStyleHandler:Xi,actionHandler:Ps}),tr:new At({x:.5,y:-.5,cursorStyleHandler:Xi,actionHandler:Ps}),bl:new At({x:-.5,y:.5,cursorStyleHandler:Xi,actionHandler:Ps}),br:new At({x:.5,y:.5,cursorStyleHandler:Xi,actionHandler:Ps}),mtr:new At({x:0,y:-.5,actionHandler:uu,cursorStyleHandler:cu,offsetY:-40,withConnection:!0,actionName:ir})}),Cu=()=>({mr:new At({x:.5,y:0,actionHandler:Ua,cursorStyleHandler:wi,actionName:Bi}),ml:new At({x:-.5,y:0,actionHandler:Ua,cursorStyleHandler:wi,actionName:Bi})}),wu=()=>y(y({},th()),Cu());class Yi extends de{static getDefaults(){return y(y({},super.getDefaults()),Yi.ownDefaults)}constructor(t){super(),Object.assign(this,this.constructor.createControls(),Yi.ownDefaults),this.setOptions(t)}static createControls(){return{controls:th()}}_updateCacheCanvas(){const t=this.canvas;if(this.noScaleCache&&t&&t._currentTransform){const e=t._currentTransform,i=e.target,s=e.action;if(this===i&&s&&s.startsWith(vs))return!1}return super._updateCacheCanvas()}getActiveControl(){const t=this.__corner;return t?{key:t,control:this.controls[t],coord:this.oCoords[t]}:void 0}findControl(t){let e=arguments.length>1&&arguments[1]!==void 0&&arguments[1];if(!this.hasControls||!this.canvas)return;this.__corner=void 0;const i=Object.entries(this.oCoords);for(let s=i.length-1;s>=0;s--){const[n,o]=i[s],a=this.controls[n];if(a.shouldActivate(n,this,t,e?o.touchCorner:o.corner))return this.__corner=n,{key:n,control:a,coord:this.oCoords[n]}}}calcOCoords(){const t=this.getViewportTransform(),e=this.getCenterPoint(),i=fi(e.x,e.y),s=Fe({angle:this.getTotalAngle()-(this.group&&this.flipX?180:0)}),n=nt(i,s),o=nt(t,n),a=nt(o,[1/t[0],0,0,1/t[3],0,0]),h=this.group?gi(this.calcTransformMatrix()):void 0;h&&(h.scaleX=Math.abs(h.scaleX),h.scaleY=Math.abs(h.scaleY));const l=this._calculateCurrentDimensions(h),c={};return this.forEachControl((u,d)=>{const g=u.positionHandler(l,a,this,u);c[d]=Object.assign(g,this._calcCornerCoords(u,g))}),c}_calcCornerCoords(t,e){const i=this.getTotalAngle();return{corner:t.calcCornerCoords(i,this.cornerSize,e.x,e.y,!1,this),touchCorner:t.calcCornerCoords(i,this.touchCornerSize,e.x,e.y,!0,this)}}setCoords(){super.setCoords(),this.canvas&&(this.oCoords=this.calcOCoords())}forEachControl(t){for(const e in this.controls)t(this.controls[e],e,this)}drawSelectionBackground(t){if(!this.selectionBackgroundColor||this.canvas&&this.canvas._activeObject!==this)return;t.save();const e=this.getRelativeCenterPoint(),i=this._calculateCurrentDimensions(),s=this.getViewportTransform();t.translate(e.x,e.y),t.scale(1/s[0],1/s[3]),t.rotate(Q(this.angle)),t.fillStyle=this.selectionBackgroundColor,t.fillRect(-i.x/2,-i.y/2,i.x,i.y),t.restore()}strokeBorders(t,e){t.strokeRect(-e.x/2,-e.y/2,e.x,e.y)}_drawBorders(t,e){let i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const s=y({hasControls:this.hasControls,borderColor:this.borderColor,borderDashArray:this.borderDashArray},i);t.save(),t.strokeStyle=s.borderColor,this._setLineDash(t,s.borderDashArray),this.strokeBorders(t,e),s.hasControls&&this.drawControlsConnectingLines(t,e),t.restore()}_renderControls(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const{hasBorders:i,hasControls:s}=this,n=y({hasBorders:i,hasControls:s},e),o=this.getViewportTransform(),a=n.hasBorders,h=n.hasControls,l=nt(o,this.calcTransformMatrix()),c=gi(l);t.save(),t.translate(c.translateX,c.translateY),t.lineWidth=this.borderScaleFactor,this.group===this.parent&&(t.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1),this.flipX&&(c.angle-=180),t.rotate(Q(this.group?c.angle:this.angle)),a&&this.drawBorders(t,c,e),h&&this.drawControls(t,e),t.restore()}drawBorders(t,e,i){let s;if(i&&i.forActiveSelection||this.group){const n=Ss(this.width,this.height,$i(e)),o=this.isStrokeAccountedForInDimensions()?nr:(this.strokeUniform?new C().scalarAdd(this.canvas?this.canvas.getZoom():1):new C(e.scaleX,e.scaleY)).scalarMultiply(this.strokeWidth);s=n.add(o).scalarAdd(this.borderScaleFactor).scalarAdd(2*this.padding)}else s=this._calculateCurrentDimensions().scalarAdd(this.borderScaleFactor);this._drawBorders(t,s,i)}drawControlsConnectingLines(t,e){let i=!1;t.beginPath(),this.forEachControl((s,n)=>{s.withConnection&&s.getVisibility(this,n)&&(i=!0,t.moveTo(s.x*e.x,s.y*e.y),t.lineTo(s.x*e.x+s.offsetX,s.y*e.y+s.offsetY))}),i&&t.stroke()}drawControls(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};t.save();const i=this.getCanvasRetinaScaling(),{cornerStrokeColor:s,cornerDashArray:n,cornerColor:o}=this,a=y({cornerStrokeColor:s,cornerDashArray:n,cornerColor:o},e);t.setTransform(i,0,0,i,0,0),t.strokeStyle=t.fillStyle=a.cornerColor,this.transparentCorners||(t.strokeStyle=a.cornerStrokeColor),this._setLineDash(t,a.cornerDashArray),this.forEachControl((h,l)=>{if(h.getVisibility(this,l)){const c=this.oCoords[l];h.render(t,c.x,c.y,a,this)}}),t.restore()}isControlVisible(t){return this.controls[t]&&this.controls[t].getVisibility(this,t)}setControlVisible(t,e){this._controlsVisibility||(this._controlsVisibility={}),this._controlsVisibility[t]=e}setControlsVisibility(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Object.entries(t).forEach(e=>{let[i,s]=e;return this.setControlVisible(i,s)})}clearContextTop(t){if(!this.canvas)return;const e=this.canvas.contextTop;if(!e)return;const i=this.canvas.viewportTransform;e.save(),e.transform(i[0],i[1],i[2],i[3],i[4],i[5]),this.transform(e);const s=this.width+4,n=this.height+4;return e.clearRect(-s/2,-n/2,s,n),t||e.restore(),e}onDeselect(t){return!1}onSelect(t){return!1}shouldStartDragging(t){return!1}onDragStart(t){return!1}canDrop(t){return!1}renderDragSourceEffect(t){}renderDropTargetEffect(t){}}function eh(r,t){return t.forEach(e=>{Object.getOwnPropertyNames(e.prototype).forEach(i=>{i!=="constructor"&&Object.defineProperty(r.prototype,i,Object.getOwnPropertyDescriptor(e.prototype,i)||Object.create(null))})}),r}m(Yi,"ownDefaults",{noScaleCache:!0,lockMovementX:!1,lockMovementY:!1,lockRotation:!1,lockScalingX:!1,lockScalingY:!1,lockSkewingX:!1,lockSkewingY:!1,lockScalingFlip:!1,cornerSize:13,touchCornerSize:24,transparentCorners:!0,cornerColor:"rgb(178,204,255)",cornerStrokeColor:"",cornerStyle:"rect",cornerDashArray:null,hasControls:!0,borderColor:"rgb(178,204,255)",borderDashArray:null,borderOpacityWhenMoving:.4,borderScaleFactor:1,hasBorders:!0,selectionBackgroundColor:"",selectable:!0,evented:!0,perPixelTargetFind:!1,activeOn:"down",hoverCursor:null,moveCursor:null});class ft extends Yi{}eh(ft,[ja]),D.setClass(ft),D.setClass(ft,"object");const ih=(r,t,e,i)=>{const s=2*(i=Math.round(i))+1,{data:n}=r.getImageData(t-i,e-i,s,s);for(let o=3;o<n.length;o+=4)if(n[o]>0)return!1;return!0};class sh{constructor(t){this.options=t,this.strokeProjectionMagnitude=this.options.strokeWidth/2,this.scale=new C(this.options.scaleX,this.options.scaleY),this.strokeUniformScalar=this.options.strokeUniform?new C(1/this.options.scaleX,1/this.options.scaleY):new C(1,1)}createSideVector(t,e){const i=Os(t,e);return this.options.strokeUniform?i.multiply(this.scale):i}projectOrthogonally(t,e,i){return this.applySkew(t.add(this.calcOrthogonalProjection(t,e,i)))}isSkewed(){return this.options.skewX!==0||this.options.skewY!==0}applySkew(t){const e=new C(t);return e.y+=e.x*Math.tan(Q(this.options.skewY)),e.x+=e.y*Math.tan(Q(this.options.skewX)),e}scaleUnitVector(t,e){return t.multiply(this.strokeUniformScalar).scalarMultiply(e)}}const xu=new C;class xi extends sh{static getOrthogonalRotationFactor(t,e){const i=e?Ds(t,e):za(t);return Math.abs(i)<Te?-1:1}constructor(t,e,i,s){super(s),m(this,"AB",void 0),m(this,"AC",void 0),m(this,"alpha",void 0),m(this,"bisector",void 0),this.A=new C(t),this.B=new C(e),this.C=new C(i),this.AB=this.createSideVector(this.A,this.B),this.AC=this.createSideVector(this.A,this.C),this.alpha=Ds(this.AB,this.AC),this.bisector=As(vr(this.AB.eq(xu)?this.AC:this.AB,this.alpha/2))}calcOrthogonalProjection(t,e){let i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.strokeProjectionMagnitude;const s=this.createSideVector(t,e),n=yr(s),o=xi.getOrthogonalRotationFactor(n,this.bisector);return this.scaleUnitVector(n,i*o)}projectBevel(){const t=[];return(this.alpha%ce==0?[this.B]:[this.B,this.C]).forEach(e=>{t.push(this.projectOrthogonally(this.A,e)),t.push(this.projectOrthogonally(this.A,e,-this.strokeProjectionMagnitude))}),t}projectMiter(){const t=[],e=Math.abs(this.alpha),i=1/Math.sin(e/2),s=this.scaleUnitVector(this.bisector,-this.strokeProjectionMagnitude*i),n=this.options.strokeUniform?Ms(this.scaleUnitVector(this.bisector,this.options.strokeMiterLimit)):this.options.strokeMiterLimit;return Ms(s)/this.strokeProjectionMagnitude<=n&&t.push(this.applySkew(this.A.add(s))),t.push(...this.projectBevel()),t}projectRoundNoSkew(t,e){const i=[],s=new C(xi.getOrthogonalRotationFactor(this.bisector),xi.getOrthogonalRotationFactor(new C(this.bisector.y,this.bisector.x)));return[new C(1,0).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar).multiply(s),new C(0,1).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar).multiply(s)].forEach(n=>{br(n,t,e)&&i.push(this.A.add(n))}),i}projectRoundWithSkew(t,e){const i=[],{skewX:s,skewY:n,scaleX:o,scaleY:a,strokeUniform:h}=this.options,l=new C(Math.tan(Q(s)),Math.tan(Q(n))),c=this.strokeProjectionMagnitude,u=h?c/a/Math.sqrt(1/a**2+1/o**2*l.y**2):c/Math.sqrt(1+l.y**2),d=new C(Math.sqrt(Math.max(c**2-u**2,0)),u),g=h?c/Math.sqrt(1+l.x**2*(1/a)**2/(1/o+1/o*l.x*l.y)**2):c/Math.sqrt(1+l.x**2/(1+l.x*l.y)**2),f=new C(g,Math.sqrt(Math.max(c**2-g**2,0)));return[f,f.scalarMultiply(-1),d,d.scalarMultiply(-1)].map(p=>this.applySkew(h?p.multiply(this.strokeUniformScalar):p)).forEach(p=>{br(p,t,e)&&i.push(this.applySkew(this.A).add(p))}),i}projectRound(){const t=[];t.push(...this.projectBevel());const e=this.alpha%ce==0,i=this.applySkew(this.A),s=t[e?0:2].subtract(i),n=t[e?1:0].subtract(i),o=e?this.applySkew(this.AB.scalarMultiply(-1)):this.applySkew(this.bisector.multiply(this.strokeUniformScalar).scalarMultiply(-1)),a=yi(s,o)>0,h=a?s:n,l=a?n:s;return this.isSkewed()?t.push(...this.projectRoundWithSkew(h,l)):t.push(...this.projectRoundNoSkew(h,l)),t}projectPoints(){switch(this.options.strokeLineJoin){case"miter":return this.projectMiter();case"round":return this.projectRound();default:return this.projectBevel()}}project(){return this.projectPoints().map(t=>({originPoint:this.A,projectedPoint:t,angle:this.alpha,bisector:this.bisector}))}}class nh extends sh{constructor(t,e,i){super(i),this.A=new C(t),this.T=new C(e)}calcOrthogonalProjection(t,e){let i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.strokeProjectionMagnitude;const s=this.createSideVector(t,e);return this.scaleUnitVector(yr(s),i)}projectButt(){return[this.projectOrthogonally(this.A,this.T,this.strokeProjectionMagnitude),this.projectOrthogonally(this.A,this.T,-this.strokeProjectionMagnitude)]}projectRound(){const t=[];if(!this.isSkewed()&&this.A.eq(this.T)){const e=new C(1,1).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar);t.push(this.applySkew(this.A.add(e)),this.applySkew(this.A.subtract(e)))}else t.push(...new xi(this.A,this.T,this.T,this.options).projectRound());return t}projectSquare(){const t=[];if(this.A.eq(this.T)){const e=new C(1,1).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar);t.push(this.A.add(e),this.A.subtract(e))}else{const e=this.calcOrthogonalProjection(this.A,this.T,this.strokeProjectionMagnitude),i=this.scaleUnitVector(As(this.createSideVector(this.A,this.T)),-this.strokeProjectionMagnitude),s=this.A.add(i);t.push(s.add(e),s.subtract(e))}return t.map(e=>this.applySkew(e))}projectPoints(){switch(this.options.strokeLineCap){case"round":return this.projectRound();case"square":return this.projectSquare();default:return this.projectButt()}}project(){return this.projectPoints().map(t=>({originPoint:this.A,projectedPoint:t}))}}const rh=function(r,t){let e=arguments.length>2&&arguments[2]!==void 0&&arguments[2];const i=[];if(r.length===0)return i;const s=r.reduce((n,o)=>(n[n.length-1].eq(o)||n.push(new C(o)),n),[new C(r[0])]);if(s.length===1)e=!0;else if(!e){const n=s[0],o=((a,h)=>{for(let l=a.length-1;l>=0;l--)if(h(a[l],l,a))return l;return-1})(s,a=>!a.eq(n));s.splice(o+1)}return s.forEach((n,o,a)=>{let h,l;o===0?(l=a[1],h=e?n:a[a.length-1]):o===a.length-1?(h=a[o-1],l=e?n:a[0]):(h=a[o-1],l=a[o+1]),e&&a.length===1?i.push(...new nh(n,n,t).project()):!e||o!==0&&o!==a.length-1?i.push(...new xi(n,h,l,t).project()):i.push(...new nh(n,o===0?l:h,t).project())}),i},kr=r=>{const t={};return Object.keys(r).forEach(e=>{t[e]={},Object.keys(r[e]).forEach(i=>{t[e][i]=y({},r[e][i])})}),t},oh=r=>r.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),Is=r=>{const t=[];for(let e,i=0;i<r.length;i++)(e=_u(r,i))!==!1&&t.push(e);return t},_u=(r,t)=>{const e=r.charCodeAt(t);if(isNaN(e))return"";if(e<55296||e>57343)return r.charAt(t);if(55296<=e&&e<=56319){if(r.length<=t+1)throw"High surrogate without following low surrogate";const s=r.charCodeAt(t+1);if(56320>s||s>57343)throw"High surrogate without following low surrogate";return r.charAt(t)+r.charAt(t+1)}if(t===0)throw"Low surrogate without preceding high surrogate";const i=r.charCodeAt(t-1);if(55296>i||i>56319)throw"Low surrogate without preceding high surrogate";return!1};var Su=Object.freeze({__proto__:null,capitalize:function(r){let t=arguments.length>1&&arguments[1]!==void 0&&arguments[1];return"".concat(r.charAt(0).toUpperCase()).concat(t?r.slice(1):r.slice(1).toLowerCase())},escapeXml:oh,graphemeSplit:Is});const js=function(r,t){let e=arguments.length>2&&arguments[2]!==void 0&&arguments[2];return r.fill!==t.fill||r.stroke!==t.stroke||r.strokeWidth!==t.strokeWidth||r.fontSize!==t.fontSize||r.fontFamily!==t.fontFamily||r.fontWeight!==t.fontWeight||r.fontStyle!==t.fontStyle||r.textBackgroundColor!==t.textBackgroundColor||r.deltaY!==t.deltaY||e&&(r.overline!==t.overline||r.underline!==t.underline||r.linethrough!==t.linethrough)},ah=(r,t)=>{const e=t.split(`
`),i=[];let s=-1,n={};r=kr(r);for(let o=0;o<e.length;o++){const a=Is(e[o]);if(r[o])for(let h=0;h<a.length;h++){s++;const l=r[o][h];l&&Object.keys(l).length>0&&(js(n,l,!0)?i.push({start:s,end:s+1,style:l}):i[i.length-1].end++),n=l||{}}else s+=a.length,n={}}return i},hh=(r,t)=>{if(!Array.isArray(r))return kr(r);const e=t.split(er),i={};let s=-1,n=0;for(let o=0;o<e.length;o++){const a=Is(e[o]);for(let h=0;h<a.length;h++)s++,r[n]&&r[n].start<=s&&s<r[n].end&&(i[o]=i[o]||{},i[o][h]=y({},r[n].style),s===r[n].end-1&&n++)}return i},Oe=["display","transform",at,"fill-opacity","fill-rule","opacity",_t,"stroke-dasharray","stroke-linecap","stroke-dashoffset","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","id","paint-order","vector-effect","instantiated_by_use","clip-path"];function lh(r,t){const e=r.nodeName,i=r.getAttribute("class"),s=r.getAttribute("id"),n="(?![a-zA-Z\\-]+)";let o;if(o=new RegExp("^"+e,"i"),t=t.replace(o,""),s&&t.length&&(o=new RegExp("#"+s+n,"i"),t=t.replace(o,"")),i&&t.length){const a=i.split(" ");for(let h=a.length;h--;)o=new RegExp("\\."+a[h]+n,"i"),t=t.replace(o,"")}return t.length===0}function Tu(r,t){let e=!0;const i=lh(r,t.pop());return i&&t.length&&(e=function(s,n){let o,a=!0;for(;s.parentElement&&s.parentElement.nodeType===1&&n.length;)a&&(o=n.pop()),a=lh(s=s.parentElement,o);return n.length===0}(r,t)),i&&e&&t.length===0}const Eu=r=>{var t;return(t=Yc[r])!==null&&t!==void 0?t:r},ku=new RegExp("(".concat(We,")"),"gi"),Ou=r=>r.replace(ku," $1 ").replace(/,/gi," ").replace(/\s+/gi," ");var ch,uh,dh,gh,fh,ph,mh;const vt="(".concat(We,")"),Mu=String.raw(ch||(ch=_e(["(skewX)(",")"],["(skewX)\\(","\\)"])),vt),Du=String.raw(uh||(uh=_e(["(skewY)(",")"],["(skewY)\\(","\\)"])),vt),Au=String.raw(dh||(dh=_e(["(rotate)(","(?: "," ",")?)"],["(rotate)\\(","(?: "," ",")?\\)"])),vt,vt,vt),Pu=String.raw(gh||(gh=_e(["(scale)(","(?: ",")?)"],["(scale)\\(","(?: ",")?\\)"])),vt,vt),Lu=String.raw(fh||(fh=_e(["(translate)(","(?: ",")?)"],["(translate)\\(","(?: ",")?\\)"])),vt,vt),Ru=String.raw(ph||(ph=_e(["(matrix)("," "," "," "," "," ",")"],["(matrix)\\("," "," "," "," "," ","\\)"])),vt,vt,vt,vt,vt,vt),Or="(?:".concat(Ru,"|").concat(Lu,"|").concat(Au,"|").concat(Pu,"|").concat(Mu,"|").concat(Du,")"),Iu="(?:".concat(Or,"*)"),ju=String.raw(mh||(mh=_e(["^s*(?:","?)s*$"],["^\\s*(?:","?)\\s*$"])),Iu),Bu=new RegExp(ju),Fu=new RegExp(Or),$u=new RegExp(Or,"g");function Mr(r){const t=[];if(!(r=Ou(r).replace(/\s*([()])\s*/gi,"$1"))||r&&!Bu.test(r))return[...pt];for(const e of r.matchAll($u)){const i=Fu.exec(e[0]);if(!i)continue;let s=pt;const n=i.filter(f=>!!f),[,o,...a]=n,[h,l,c,u,d,g]=a.map(f=>parseFloat(f));switch(o){case"translate":s=fi(h,l);break;case ir:s=Fe({angle:h},{x:l,y:c});break;case vs:s=xs(h,l);break;case ci:s=ar(h);break;case ui:s=hr(h);break;case"matrix":s=[h,l,c,u,d,g]}t.push(s)}return ws(t)}function zu(r,t,e,i){const s=Array.isArray(t);let n,o=t;if(r!==at&&r!==_t||t!==xt){if(r==="strokeUniform")return t==="non-scaling-stroke";if(r==="strokeDashArray")o=t===xt?null:t.replace(/,/g," ").split(/\s+/).map(parseFloat);else if(r==="transformMatrix")o=e&&e.transformMatrix?nt(e.transformMatrix,Mr(t)):Mr(t);else if(r==="visible")o=t!==xt&&t!=="hidden",e&&e.visible===!1&&(o=!1);else if(r==="opacity")o=parseFloat(t),e&&e.opacity!==void 0&&(o*=e.opacity);else if(r==="textAnchor")o=t==="start"?V:t==="end"?it:j;else if(r==="charSpacing")n=Ne(t,i)/i*1e3;else if(r==="paintFirst"){const a=t.indexOf(at),h=t.indexOf(_t);o=at,(a>-1&&h>-1&&h<a||a===-1&&h>-1)&&(o=_t)}else{if(r==="href"||r==="xlink:href"||r==="font"||r==="id")return t;if(r==="imageSmoothing")return t==="optimizeQuality";n=s?t.map(Ne):Ne(t,i)}}else o="";return!s&&isNaN(n)?o:n}function Hu(r,t){const e=r.match(Xc);if(!e)return;const i=e[1],s=e[3],n=e[4],o=e[5],a=e[6];i&&(t.fontStyle=i),s&&(t.fontWeight=isNaN(parseFloat(s))?s:parseFloat(s)),n&&(t.fontSize=Ne(n)),a&&(t.fontFamily=a),o&&(t.lineHeight=o==="normal"?1:o)}function Nu(r,t){r.replace(/;\s*$/,"").split(";").forEach(e=>{if(!e)return;const[i,s]=e.split(":");t[i.trim().toLowerCase()]=s.trim()})}function Wu(r){const t={},e=r.getAttribute("style");return e&&(typeof e=="string"?Nu(e,t):function(i,s){Object.entries(i).forEach(n=>{let[o,a]=n;a!==void 0&&(s[o.toLowerCase()]=a)})}(e,t)),t}const Vu={stroke:"strokeOpacity",fill:"fillOpacity"};function ge(r,t,e){if(!r)return{};let i,s={},n=Qn;r.parentNode&&Fa.test(r.parentNode.nodeName)&&(s=ge(r.parentElement,t,e),s.fontSize&&(i=n=Ne(s.fontSize)));const o=y(y(y({},t.reduce((l,c)=>{const u=r.getAttribute(c);return u&&(l[c]=u),l},{})),function(l){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},u={};for(const d in c)Tu(l,d.split(" "))&&(u=y(y({},u),c[d]));return u}(r,e)),Wu(r));o[mr]&&r.setAttribute(mr,o[mr]),o[pr]&&(i=Ne(o[pr],n),o[pr]="".concat(i));const a={};for(const l in o){const c=Eu(l),u=zu(c,o[l],s,i);a[c]=u}a&&a.font&&Hu(a.font,a);const h=y(y({},s),a);return Fa.test(r.nodeName)?h:function(l){const c=ft.getDefaults();return Object.entries(Vu).forEach(u=>{let[d,g]=u;if(l[g]===void 0||l[d]==="")return;if(l[d]===void 0){if(!c[d])return;l[d]=c[d]}if(l[d].indexOf("url(")===0)return;const f=new N(l[d]);l[d]=f.setAlpha(G(f.getAlpha()*l[g],2)).toRgba()}),l}(h)}const Xu=["left","top","width","height","visible"],vh=["rx","ry"];class Ut extends ft{static getDefaults(){return y(y({},super.getDefaults()),Ut.ownDefaults)}constructor(t){super(),Object.assign(this,Ut.ownDefaults),this.setOptions(t),this._initRxRy()}_initRxRy(){const{rx:t,ry:e}=this;t&&!e?this.ry=t:e&&!t&&(this.rx=e)}_render(t){const{width:e,height:i}=this,s=-e/2,n=-i/2,o=this.rx?Math.min(this.rx,e/2):0,a=this.ry?Math.min(this.ry,i/2):0,h=o!==0||a!==0;t.beginPath(),t.moveTo(s+o,n),t.lineTo(s+e-o,n),h&&t.bezierCurveTo(s+e-Ee*o,n,s+e,n+Ee*a,s+e,n+a),t.lineTo(s+e,n+i-a),h&&t.bezierCurveTo(s+e,n+i-Ee*a,s+e-Ee*o,n+i,s+e-o,n+i),t.lineTo(s+o,n+i),h&&t.bezierCurveTo(s+Ee*o,n+i,s,n+i-Ee*a,s,n+i-a),t.lineTo(s,n+a),h&&t.bezierCurveTo(s,n+Ee*a,s+Ee*o,n,s+o,n),t.closePath(),this._renderPaintInOrder(t)}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return super.toObject([...vh,...t])}_toSVG(){const{width:t,height:e,rx:i,ry:s}=this;return["<rect ","COMMON_PARTS",'x="'.concat(-t/2,'" y="').concat(-e/2,'" rx="').concat(i,'" ry="').concat(s,'" width="').concat(t,'" height="').concat(e,`" />
`)]}static async fromElement(t,e,i){const s=ge(t,this.ATTRIBUTE_NAMES,i),{left:n=0,top:o=0,width:a=0,height:h=0,visible:l=!0}=s,c=U(s,Xu);return new this(y(y(y({},e),c),{},{left:n,top:o,width:a,height:h,visible:!!(l&&a&&h)}))}}m(Ut,"type","Rect"),m(Ut,"cacheProperties",[...ue,...vh]),m(Ut,"ownDefaults",{rx:0,ry:0}),m(Ut,"ATTRIBUTE_NAMES",[...Oe,"x","y","rx","ry","width","height"]),D.setClass(Ut),D.setSVGClass(Ut);const fe="initialization",Bs="added",Dr="removed",Fs="imperative",yh=(r,t)=>{const{strokeUniform:e,strokeWidth:i,width:s,height:n,group:o}=t,a=o&&o!==r?Wi(o.calcTransformMatrix(),r.calcTransformMatrix()):null,h=a?t.getRelativeCenterPoint().transform(a):t.getRelativeCenterPoint(),l=!t.isStrokeAccountedForInDimensions(),c=e&&l?Ta(new C(i,i),void 0,r.calcTransformMatrix()):nr,u=!e&&l?i:0,d=Ss(s+u,n+u,ws([a,t.calcOwnMatrix()],!0)).add(c).scalarDivide(2);return[h.subtract(d),h.add(d)]};class $s{calcLayoutResult(t,e){if(this.shouldPerformLayout(t))return this.calcBoundingBox(e,t)}shouldPerformLayout(t){let{type:e,prevStrategy:i,strategy:s}=t;return e===fe||e===Fs||!!i&&s!==i}shouldLayoutClipPath(t){let{type:e,target:{clipPath:i}}=t;return e!==fe&&i&&!i.absolutePositioned}getInitialSize(t,e){return e.size}calcBoundingBox(t,e){const{type:i,target:s}=e;if(i===Fs&&e.overrides)return e.overrides;if(t.length===0)return;const{left:n,top:o,width:a,height:h}=se(t.map(u=>yh(s,u)).reduce((u,d)=>u.concat(d),[])),l=new C(a,h),c=new C(n,o).add(l.scalarDivide(2));if(i===fe){const u=this.getInitialSize(e,{size:l,center:c});return{center:c,relativeCorrection:new C(0,0),size:u}}return{center:c.transform(s.calcOwnMatrix()),size:l}}}m($s,"type","strategy");class Ar extends $s{shouldPerformLayout(t){return!0}}m(Ar,"type","fit-content"),D.setClass(Ar);const Yu=["strategy"],Gu=["target","strategy","bubbles","prevStrategy"],bh="layoutManager";class _i{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:new Ar;m(this,"strategy",void 0),this.strategy=t,this._subscriptions=new Map}performLayout(t){const e=y(y({bubbles:!0,strategy:this.strategy},t),{},{prevStrategy:this._prevLayoutStrategy,stopPropagation(){this.bubbles=!1}});this.onBeforeLayout(e);const i=this.getLayoutResult(e);i&&this.commitLayout(e,i),this.onAfterLayout(e,i),this._prevLayoutStrategy=e.strategy}attachHandlers(t,e){const{target:i}=e;return[ys,sa,Bi,na,ps,ra,ms,jc,Bc].map(s=>t.on(s,n=>this.performLayout(s===ys?{type:"object_modified",trigger:s,e:n,target:i}:{type:"object_modifying",trigger:s,e:n,target:i})))}subscribe(t,e){this.unsubscribe(t,e);const i=this.attachHandlers(t,e);this._subscriptions.set(t,i)}unsubscribe(t,e){(this._subscriptions.get(t)||[]).forEach(i=>i()),this._subscriptions.delete(t)}unsubscribeTargets(t){t.targets.forEach(e=>this.unsubscribe(e,t))}subscribeTargets(t){t.targets.forEach(e=>this.subscribe(e,t))}onBeforeLayout(t){const{target:e,type:i}=t,{canvas:s}=e;if(i===fe||i===Bs?this.subscribeTargets(t):i===Dr&&this.unsubscribeTargets(t),e.fire("layout:before",{context:t}),s&&s.fire("object:layout:before",{target:e,context:t}),i===Fs&&t.deep){const n=U(t,Yu);e.forEachObject(o=>o.layoutManager&&o.layoutManager.performLayout(y(y({},n),{},{bubbles:!1,target:o})))}}getLayoutResult(t){const{target:e,strategy:i,type:s}=t,n=i.calcLayoutResult(t,e.getObjects());if(!n)return;const o=s===fe?new C:e.getRelativeCenterPoint(),{center:a,correction:h=new C,relativeCorrection:l=new C}=n,c=o.subtract(a).add(h).transform(s===fe?pt:Dt(e.calcOwnMatrix()),!0).add(l);return{result:n,prevCenter:o,nextCenter:a,offset:c}}commitLayout(t,e){const{target:i}=t,{result:{size:s},nextCenter:n}=e;var o,a;i.set({width:s.x,height:s.y}),this.layoutObjects(t,e),t.type===fe?i.set({left:(o=t.x)!==null&&o!==void 0?o:n.x+s.x*rt(i.originX),top:(a=t.y)!==null&&a!==void 0?a:n.y+s.y*rt(i.originY)}):(i.setPositionByOrigin(n,j,j),i.setCoords(),i.set("dirty",!0))}layoutObjects(t,e){const{target:i}=t;i.forEachObject(s=>{s.group===i&&this.layoutObject(t,e,s)}),t.strategy.shouldLayoutClipPath(t)&&this.layoutObject(t,e,i.clipPath)}layoutObject(t,e,i){let{offset:s}=e;i.set({left:i.left+s.x,top:i.top+s.y})}onAfterLayout(t,e){const{target:i,strategy:s,bubbles:n,prevStrategy:o}=t,a=U(t,Gu),{canvas:h}=i;i.fire("layout:after",{context:t,result:e}),h&&h.fire("object:layout:after",{context:t,result:e,target:i});const l=i.parent;n&&l!=null&&l.layoutManager&&((a.path||(a.path=[])).push(i),l.layoutManager.performLayout(y(y({},a),{},{target:l}))),i.set("dirty",!0)}dispose(){const{_subscriptions:t}=this;t.forEach(e=>e.forEach(i=>i())),t.clear()}toObject(){return{type:bh,strategy:this.strategy.constructor.type}}toJSON(){return this.toObject()}}D.setClass(_i,bh);const Uu=["type","objects","layoutManager"];class qu extends _i{performLayout(){}}class Ft extends oa(ft){static getDefaults(){return y(y({},super.getDefaults()),Ft.ownDefaults)}constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),m(this,"_activeObjects",[]),m(this,"__objectSelectionTracker",void 0),m(this,"__objectSelectionDisposer",void 0),Object.assign(this,Ft.ownDefaults),this.setOptions(e),this.groupInit(t,e)}groupInit(t,e){var i;this._objects=[...t],this.__objectSelectionTracker=this.__objectSelectionMonitor.bind(this,!0),this.__objectSelectionDisposer=this.__objectSelectionMonitor.bind(this,!1),this.forEachObject(s=>{this.enterGroup(s,!1)}),this.layoutManager=(i=e.layoutManager)!==null&&i!==void 0?i:new _i,this.layoutManager.performLayout({type:fe,target:this,targets:[...t],x:e.left,y:e.top})}canEnterGroup(t){return t===this||this.isDescendantOf(t)?(Se("error","Group: circular object trees are not supported, this call has no effect"),!1):this._objects.indexOf(t)===-1||(Se("error","Group: duplicate objects are not supported inside group, this call has no effect"),!1)}_filterObjectsBeforeEnteringGroup(t){return t.filter((e,i,s)=>this.canEnterGroup(e)&&s.indexOf(e)===i)}add(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];const s=this._filterObjectsBeforeEnteringGroup(e),n=super.add(...s);return this._onAfterObjectsChange(Bs,s),n}insertAt(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),s=1;s<e;s++)i[s-1]=arguments[s];const n=this._filterObjectsBeforeEnteringGroup(i),o=super.insertAt(t,...n);return this._onAfterObjectsChange(Bs,n),o}remove(){const t=super.remove(...arguments);return this._onAfterObjectsChange(Dr,t),t}_onObjectAdded(t){this.enterGroup(t,!0),this.fire("object:added",{target:t}),t.fire("added",{target:this})}_onObjectRemoved(t,e){this.exitGroup(t,e),this.fire("object:removed",{target:t}),t.fire("removed",{target:this})}_onAfterObjectsChange(t,e){this.layoutManager.performLayout({type:t,targets:e,target:this})}_onStackOrderChanged(){this._set("dirty",!0)}_set(t,e){const i=this[t];return super._set(t,e),t==="canvas"&&i!==e&&(this._objects||[]).forEach(s=>{s._set(t,e)}),this}_shouldSetNestedCoords(){return this.subTargetCheck}removeAll(){return this._activeObjects=[],this.remove(...this._objects)}__objectSelectionMonitor(t,e){let{target:i}=e;const s=this._activeObjects;if(t)s.push(i),this._set("dirty",!0);else if(s.length>0){const n=s.indexOf(i);n>-1&&(s.splice(n,1),this._set("dirty",!0))}}_watchObject(t,e){t&&this._watchObject(!1,e),t?(e.on("selected",this.__objectSelectionTracker),e.on("deselected",this.__objectSelectionDisposer)):(e.off("selected",this.__objectSelectionTracker),e.off("deselected",this.__objectSelectionDisposer))}enterGroup(t,e){t.group&&t.group.remove(t),t._set("parent",this),this._enterGroup(t,e)}_enterGroup(t,e){e&&vi(t,nt(Dt(this.calcTransformMatrix()),t.calcTransformMatrix())),this._shouldSetNestedCoords()&&t.setCoords(),t._set("group",this),t._set("canvas",this.canvas),this._watchObject(!0,t);const i=this.canvas&&this.canvas.getActiveObject&&this.canvas.getActiveObject();i&&(i===t||t.isDescendantOf(i))&&this._activeObjects.push(t)}exitGroup(t,e){this._exitGroup(t,e),t._set("parent",void 0),t._set("canvas",void 0)}_exitGroup(t,e){t._set("group",void 0),e||(vi(t,nt(this.calcTransformMatrix(),t.calcTransformMatrix())),t.setCoords()),this._watchObject(!1,t);const i=this._activeObjects.length>0?this._activeObjects.indexOf(t):-1;i>-1&&this._activeObjects.splice(i,1)}shouldCache(){const t=ft.prototype.shouldCache.call(this);if(t){for(let e=0;e<this._objects.length;e++)if(this._objects[e].willDrawShadow())return this.ownCaching=!1,!1}return t}willDrawShadow(){if(super.willDrawShadow())return!0;for(let t=0;t<this._objects.length;t++)if(this._objects[t].willDrawShadow())return!0;return!1}isOnACache(){return this.ownCaching||!!this.parent&&this.parent.isOnACache()}drawObject(t,e,i){this._renderBackground(t);for(let n=0;n<this._objects.length;n++){var s;const o=this._objects[n];(s=this.canvas)!==null&&s!==void 0&&s.preserveObjectStacking&&o.group!==this?(t.save(),t.transform(...Dt(this.calcTransformMatrix())),o.render(t),t.restore()):o.group===this&&o.render(t)}this._drawClipPath(t,this.clipPath,i)}setCoords(){super.setCoords(),this._shouldSetNestedCoords()&&this.forEachObject(t=>t.setCoords())}triggerLayout(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.layoutManager.performLayout(y({target:this,type:Fs},t))}render(t){this._transformDone=!0,super.render(t),this._transformDone=!1}__serializeObjects(t,e){const i=this.includeDefaultValues;return this._objects.filter(function(s){return!s.excludeFromExport}).map(function(s){const n=s.includeDefaultValues;s.includeDefaultValues=i;const o=s[t||"toObject"](e);return s.includeDefaultValues=n,o})}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];const e=this.layoutManager.toObject();return y(y(y({},super.toObject(["subTargetCheck","interactive",...t])),e.strategy!=="fit-content"||this.includeDefaultValues?{layoutManager:e}:{}),{},{objects:this.__serializeObjects("toObject",t)})}toString(){return"#<Group: (".concat(this.complexity(),")>")}dispose(){this.layoutManager.unsubscribeTargets({targets:this.getObjects(),target:this}),this._activeObjects=[],this.forEachObject(t=>{this._watchObject(!1,t),t.dispose()}),super.dispose()}_createSVGBgRect(t){if(!this.backgroundColor)return"";const e=Ut.prototype._toSVG.call(this),i=e.indexOf("COMMON_PARTS");e[i]='for="group" ';const s=e.join("");return t?t(s):s}_toSVG(t){const e=["<g ","COMMON_PARTS",` >
`],i=this._createSVGBgRect(t);i&&e.push("		",i);for(let s=0;s<this._objects.length;s++)e.push("		",this._objects[s].toSVG(t));return e.push(`</g>
`),e}getSvgStyles(){const t=this.opacity!==void 0&&this.opacity!==1?"opacity: ".concat(this.opacity,";"):"",e=this.visible?"":" visibility: hidden;";return[t,this.getSvgFilter(),e].join("")}toClipPathSVG(t){const e=[],i=this._createSVGBgRect(t);i&&e.push("	",i);for(let s=0;s<this._objects.length;s++)e.push("	",this._objects[s].toClipPathSVG(t));return this._createBaseClipPathSVGMarkup(e,{reviver:t})}static fromObject(t,e){let{type:i,objects:s=[],layoutManager:n}=t,o=U(t,Uu);return Promise.all([pi(s,e),Hi(o,e)]).then(a=>{let[h,l]=a;const c=new this(h,y(y(y({},o),l),{},{layoutManager:new qu}));if(n){const u=D.getClass(n.type),d=D.getClass(n.strategy);c.layoutManager=new u(new d)}else c.layoutManager=new _i;return c.layoutManager.subscribeTargets({type:fe,target:c,targets:c.getObjects()}),c.setCoords(),c})}}m(Ft,"type","Group"),m(Ft,"ownDefaults",{strokeWidth:0,subTargetCheck:!1,interactive:!1}),D.setClass(Ft);const Ch=(r,t)=>Math.min(t.width/r.width,t.height/r.height),wh=(r,t)=>Math.max(t.width/r.width,t.height/r.height),Pr="\\s*,?\\s*",Gi="".concat(Pr,"(").concat(We,")"),Ku="".concat(Gi).concat(Gi).concat(Gi).concat(Pr,"([01])").concat(Pr,"([01])").concat(Gi).concat(Gi),Zu={m:"l",M:"L"},Ju=(r,t,e,i,s,n,o,a,h,l,c)=>{const u=Vt(r),d=Xt(r),g=Vt(t),f=Xt(t),p=e*s*g-i*n*f+o,v=i*s*g+e*n*f+a;return["C",l+h*(-e*s*d-i*n*u),c+h*(-i*s*d+e*n*u),p+h*(e*s*f+i*n*g),v+h*(i*s*f-e*n*g),p,v]},xh=(r,t,e,i)=>{const s=Math.atan2(t,r),n=Math.atan2(i,e);return n>=s?n-s:2*Math.PI-(s-n)};function Lr(r,t,e,i,s,n,o,a){let h;if(W.cachesBoundsOfCurve&&(h=[...arguments].join(),ji.boundsOfCurveCache[h]))return ji.boundsOfCurveCache[h];const l=Math.sqrt,c=Math.abs,u=[],d=[[0,0],[0,0]];let g=6*r-12*e+6*s,f=-3*r+9*e-9*s+3*o,p=3*e-3*r;for(let _=0;_<2;++_){if(_>0&&(g=6*t-12*i+6*n,f=-3*t+9*i-9*n+3*a,p=3*i-3*t),c(f)<1e-12){if(c(g)<1e-12)continue;const I=-p/g;0<I&&I<1&&u.push(I);continue}const k=g*g-4*p*f;if(k<0)continue;const A=l(k),L=(-g+A)/(2*f);0<L&&L<1&&u.push(L);const O=(-g-A)/(2*f);0<O&&O<1&&u.push(O)}let v=u.length;const b=v,w=Sh(r,t,e,i,s,n,o,a);for(;v--;){const{x:_,y:k}=w(u[v]);d[0][v]=_,d[1][v]=k}d[0][b]=r,d[1][b]=t,d[0][b+1]=o,d[1][b+1]=a;const x=[new C(Math.min(...d[0]),Math.min(...d[1])),new C(Math.max(...d[0]),Math.max(...d[1]))];return W.cachesBoundsOfCurve&&(ji.boundsOfCurveCache[h]=x),x}const Qu=(r,t,e)=>{let[i,s,n,o,a,h,l,c]=e;const u=((d,g,f,p,v,b,w)=>{if(f===0||p===0)return[];let x=0,_=0,k=0;const A=Math.PI,L=w*Jn,O=Xt(L),I=Vt(L),F=.5*(-I*d-O*g),tt=.5*(-I*g+O*d),Z=f**2,z=p**2,Y=tt**2,ct=F**2,S=Z*z-Z*Y-z*ct;let T=Math.abs(f),E=Math.abs(p);if(S<0){const Le=Math.sqrt(1-S/(Z*z));T*=Le,E*=Le}else k=(v===b?-1:1)*Math.sqrt(S/(Z*Y+z*ct));const R=k*T*tt/E,P=-k*E*F/T,H=I*R-O*P+.5*d,J=O*R+I*P+.5*g;let lt=xh(1,0,(F-R)/T,(tt-P)/E),et=xh((F-R)/T,(tt-P)/E,(-F-R)/T,(-tt-P)/E);b===0&&et>0?et-=2*A:b===1&&et<0&&(et+=2*A);const Jt=Math.ceil(Math.abs(et/A*2)),Rt=[],It=et/Jt,_0=8/3*Math.sin(It/4)*Math.sin(It/4)/Math.sin(It/2);let co=lt+It;for(let Le=0;Le<Jt;Le++)Rt[Le]=Ju(lt,co,I,O,T,E,H,J,_0,x,_),x=Rt[Le][5],_=Rt[Le][6],lt=co,co+=It;return Rt})(l-r,c-t,s,n,a,h,o);for(let d=0,g=u.length;d<g;d++)u[d][1]+=r,u[d][2]+=t,u[d][3]+=r,u[d][4]+=t,u[d][5]+=r,u[d][6]+=t;return u},_h=r=>{let t=0,e=0,i=0,s=0;const n=[];let o,a=0,h=0;for(const l of r){const c=[...l];let u;switch(c[0]){case"l":c[1]+=t,c[2]+=e;case"L":t=c[1],e=c[2],u=["L",t,e];break;case"h":c[1]+=t;case"H":t=c[1],u=["L",t,e];break;case"v":c[1]+=e;case"V":e=c[1],u=["L",t,e];break;case"m":c[1]+=t,c[2]+=e;case"M":t=c[1],e=c[2],i=c[1],s=c[2],u=["M",t,e];break;case"c":c[1]+=t,c[2]+=e,c[3]+=t,c[4]+=e,c[5]+=t,c[6]+=e;case"C":a=c[3],h=c[4],t=c[5],e=c[6],u=["C",c[1],c[2],a,h,t,e];break;case"s":c[1]+=t,c[2]+=e,c[3]+=t,c[4]+=e;case"S":o==="C"?(a=2*t-a,h=2*e-h):(a=t,h=e),t=c[3],e=c[4],u=["C",a,h,c[1],c[2],t,e],a=u[3],h=u[4];break;case"q":c[1]+=t,c[2]+=e,c[3]+=t,c[4]+=e;case"Q":a=c[1],h=c[2],t=c[3],e=c[4],u=["Q",a,h,t,e];break;case"t":c[1]+=t,c[2]+=e;case"T":o==="Q"?(a=2*t-a,h=2*e-h):(a=t,h=e),t=c[1],e=c[2],u=["Q",a,h,t,e];break;case"a":c[6]+=t,c[7]+=e;case"A":Qu(t,e,c).forEach(d=>n.push(d)),t=c[6],e=c[7];break;case"z":case"Z":t=i,e=s,u=["Z"]}u?(n.push(u),o=u[0]):o=""}return n},zs=(r,t,e,i)=>Math.sqrt((e-r)**2+(i-t)**2),Sh=(r,t,e,i,s,n,o,a)=>h=>{const l=h**3,c=(g=>3*g**2*(1-g))(h),u=(g=>3*g*(1-g)**2)(h),d=(g=>(1-g)**3)(h);return new C(o*l+s*c+e*u+r*d,a*l+n*c+i*u+t*d)},Th=r=>r**2,Eh=r=>2*r*(1-r),kh=r=>(1-r)**2,td=(r,t,e,i,s,n,o,a)=>h=>{const l=Th(h),c=Eh(h),u=kh(h),d=3*(u*(e-r)+c*(s-e)+l*(o-s)),g=3*(u*(i-t)+c*(n-i)+l*(a-n));return Math.atan2(g,d)},ed=(r,t,e,i,s,n)=>o=>{const a=Th(o),h=Eh(o),l=kh(o);return new C(s*a+e*h+r*l,n*a+i*h+t*l)},id=(r,t,e,i,s,n)=>o=>{const a=1-o,h=2*(a*(e-r)+o*(s-e)),l=2*(a*(i-t)+o*(n-i));return Math.atan2(l,h)},Oh=(r,t,e)=>{let i=new C(t,e),s=0;for(let n=1;n<=100;n+=1){const o=r(n/100);s+=zs(i.x,i.y,o.x,o.y),i=o}return s},sd=(r,t)=>{let e,i=0,s=0,n={x:r.x,y:r.y},o=y({},n),a=.01,h=0;const l=r.iterator,c=r.angleFinder;for(;s<t&&a>1e-4;)o=l(i),h=i,e=zs(n.x,n.y,o.x,o.y),e+s>t?(i-=a,a/=2):(n=o,i+=a,s+=e);return y(y({},o),{},{angle:c(h)})},Rr=r=>{let t,e,i=0,s=0,n=0,o=0,a=0;const h=[];for(const l of r){const c={x:s,y:n,command:l[0],length:0};switch(l[0]){case"M":e=c,e.x=o=s=l[1],e.y=a=n=l[2];break;case"L":e=c,e.length=zs(s,n,l[1],l[2]),s=l[1],n=l[2];break;case"C":t=Sh(s,n,l[1],l[2],l[3],l[4],l[5],l[6]),e=c,e.iterator=t,e.angleFinder=td(s,n,l[1],l[2],l[3],l[4],l[5],l[6]),e.length=Oh(t,s,n),s=l[5],n=l[6];break;case"Q":t=ed(s,n,l[1],l[2],l[3],l[4]),e=c,e.iterator=t,e.angleFinder=id(s,n,l[1],l[2],l[3],l[4]),e.length=Oh(t,s,n),s=l[3],n=l[4];break;case"Z":e=c,e.destX=o,e.destY=a,e.length=zs(s,n,o,a),s=o,n=a}i+=e.length,h.push(e)}return h.push({length:i,x:s,y:n}),h},Mh=function(r,t){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Rr(r),i=0;for(;t-e[i].length>0&&i<e.length-2;)t-=e[i].length,i++;const s=e[i],n=t/s.length,o=r[i];switch(s.command){case"M":return{x:s.x,y:s.y,angle:0};case"Z":return y(y({},new C(s.x,s.y).lerp(new C(s.destX,s.destY),n)),{},{angle:Math.atan2(s.destY-s.y,s.destX-s.x)});case"L":return y(y({},new C(s.x,s.y).lerp(new C(o[1],o[2]),n)),{},{angle:Math.atan2(o[2]-s.y,o[1]-s.x)});case"C":case"Q":return sd(s,t)}},nd=new RegExp("[mzlhvcsqta][^mzlhvcsqta]*","gi"),Dh=new RegExp(Ku,"g"),rd=new RegExp(We,"gi"),od={m:2,l:2,h:1,v:1,c:6,s:4,q:4,t:2,a:7},Ah=r=>{var t;const e=[],i=(t=r.match(nd))!==null&&t!==void 0?t:[];for(const s of i){const n=s[0];if(n==="z"||n==="Z"){e.push([n]);continue}const o=od[n.toLowerCase()];let a=[];if(n==="a"||n==="A"){Dh.lastIndex=0;for(let h=null;h=Dh.exec(s);)a.push(...h.slice(1))}else a=s.match(rd)||[];for(let h=0;h<a.length;h+=o){const l=new Array(o),c=Zu[n];l[0]=h>0&&c?c:n;for(let u=0;u<o;u++)l[u+1]=parseFloat(a[h+u]);e.push(l)}}return e},Ph=function(r){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,e=new C(r[0]),i=new C(r[1]),s=1,n=0;const o=[],a=r.length,h=a>2;let l;for(h&&(s=r[2].x<i.x?-1:r[2].x===i.x?0:1,n=r[2].y<i.y?-1:r[2].y===i.y?0:1),o.push(["M",e.x-s*t,e.y-n*t]),l=1;l<a;l++){if(!e.eq(i)){const c=e.midPointFrom(i);o.push(["Q",e.x,e.y,c.x,c.y])}e=r[l],l+1<r.length&&(i=r[l+1])}return h&&(s=e.x>r[l-2].x?1:e.x===r[l-2].x?0:-1,n=e.y>r[l-2].y?1:e.y===r[l-2].y?0:-1),o.push(["L",e.x+s*t,e.y+n*t]),o},Ir=(r,t)=>r.map(e=>e.map((i,s)=>s===0||t===void 0?i:G(i,t)).join(" ")).join(" ");function Hs(r,t){const e=r.style;e&&t&&(typeof t=="string"?e.cssText+=";"+t:Object.entries(t).forEach(i=>{let[s,n]=i;return e.setProperty(s,n)}))}const ad=(r,t)=>Math.floor(Math.random()*(t-r+1))+r;function hd(r){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const e=t.onComplete||je,i=new(fs()).XMLHttpRequest,s=t.signal,n=function(){i.abort()},o=function(){s&&s.removeEventListener("abort",n),i.onerror=i.ontimeout=je};if(s&&s.aborted)throw new ta("request");return s&&s.addEventListener("abort",n,{once:!0}),i.onreadystatechange=function(){i.readyState===4&&(o(),e(i),i.onreadystatechange=je)},i.onerror=i.ontimeout=o,i.open("get",r,!0),i.send(),i}var Ui=Object.freeze({__proto__:null,addTransformToObject:_a,animate:Sr,animateColor:Ga,applyTransformToObject:vi,calcAngleBetweenVectors:Ds,calcDimensionsMatrix:$i,calcPlaneChangeMatrix:Wi,calcVectorRotation:za,cancelAnimFrame:ha,capValue:Ve,composeMatrix:ga,copyCanvasElement:r=>{var t;const e=Mt(r);return(t=e.getContext("2d"))===null||t===void 0||t.drawImage(r,0,0),e},cos:Vt,createCanvasElement:jt,createImage:la,createRotateMatrix:Fe,createScaleMatrix:xs,createSkewXMatrix:ar,createSkewYMatrix:hr,createTranslateMatrix:fi,createVector:Os,crossProduct:yi,degreesToRadians:Q,dotProduct:Ha,ease:Kc,enlivenObjectEnlivables:Hi,enlivenObjects:pi,findScaleToCover:wh,findScaleToFit:Ch,getBoundsOfCurve:Lr,getOrthonormalVector:yr,getPathSegmentsInfo:Rr,getPointOnPath:Mh,getPointer:wa,getRandomInt:ad,getRegularPolygonPath:(r,t)=>{const e=2*Math.PI/r;let i=-Te;r%2==0&&(i+=e/2);const s=new Array(r+1);for(let n=0;n<r;n++){const o=n*e+i,{x:a,y:h}=new C(Vt(o),Xt(o)).scalarMultiply(t);s[n]=[n===0?"M":"L",a,h]}return s[r]=["Z"],s},getSmoothPathFromPoints:Ph,getSvgAttributes:r=>{const t=["instantiated_by_use","style","id","class"];switch(r){case"linearGradient":return t.concat(["x1","y1","x2","y2","gradientUnits","gradientTransform"]);case"radialGradient":return t.concat(["gradientUnits","gradientTransform","cx","cy","r","fx","fy","fr"]);case"stop":return t.concat(["offset","stop-color","stop-opacity"])}return t},getUnitVector:As,groupSVGElements:(r,t)=>r&&r.length===1?r[0]:new Ft(r,t),hasStyleChanged:js,invertTransform:Dt,isBetweenVectors:br,isIdentityMatrix:ca,isTouchEvent:_s,isTransparent:ih,joinPath:Ir,loadImage:zi,magnitude:Ms,makeBoundingBoxFromPoints:se,makePathSimpler:_h,matrixToSVG:mi,mergeClipPaths:(r,t)=>{var e;let i=r,s=t;i.inverted&&!s.inverted&&(i=t,s=r),dr(s,(e=s.group)===null||e===void 0?void 0:e.calcTransformMatrix(),i.calcTransformMatrix());const n=i.inverted&&s.inverted;return n&&(i.inverted=s.inverted=!1),new Ft([i],{clipPath:s,inverted:n})},multiplyTransformMatrices:nt,multiplyTransformMatrixArray:ws,parsePath:Ah,parsePreserveAspectRatioAttribute:Ia,parseUnit:Ne,pick:$e,projectStrokeOnPoints:rh,qrDecompose:gi,radiansToDegrees:ie,removeFromArray:Be,removeTransformFromObject:(r,t)=>{const e=Dt(t),i=nt(e,r.calcOwnMatrix());vi(r,i)},removeTransformMatrixForSvgParsing:(r,t)=>{let e=r._findCenterFromElement();r.transformMatrix&&((i=>{if(i.transformMatrix){const{scaleX:s,scaleY:n,angle:o,skewX:a}=gi(i.transformMatrix);i.flipX=!1,i.flipY=!1,i.set(mt,s),i.set(Ot,n),i.angle=o,i.skewX=a,i.skewY=0}})(r),e=e.transform(r.transformMatrix)),delete r.transformMatrix,t&&(r.scaleX*=t.scaleX,r.scaleY*=t.scaleY,r.cropX=t.cropX,r.cropY=t.cropY,e.x+=t.offsetLeft,e.y+=t.offsetTop,r.width=t.width,r.height=t.height),r.setPositionByOrigin(e,j,j)},request:hd,requestAnimFrame:Fi,resetObjectTransform:Sa,rotatePoint:(r,t,e)=>r.rotate(e,t),rotateVector:vr,saveObjectTransform:ur,sendObjectToPlane:dr,sendPointToPlane:He,sendVectorToPlane:Ta,setStyle:Hs,sin:Xt,sizeAfterTransform:Ss,string:Su,stylesFromArray:hh,stylesToArray:ah,toBlob:or,toDataURL:rr,toFixed:G,transformPath:(r,t,e)=>(e&&(t=nt(t,[1,0,0,1,-e.x,-e.y])),r.map(i=>{const s=[...i];for(let n=1;n<i.length-1;n+=2){const{x:o,y:a}=gt({x:i[n],y:i[n+1]},t);s[n]=o,s[n+1]=a}return s})),transformPoint:gt});class ld extends Ca{constructor(t){let{allowTouchScrolling:e=!1,containerClass:i=""}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(t),m(this,"upper",void 0),m(this,"container",void 0);const{el:s}=this.lower,n=this.createUpperCanvas();this.upper={el:n,ctx:n.getContext("2d")},this.applyCanvasStyle(s,{allowTouchScrolling:e}),this.applyCanvasStyle(n,{allowTouchScrolling:e,styles:{position:"absolute",left:"0",top:"0"}});const o=this.createContainerElement();o.classList.add(i),s.parentNode&&s.parentNode.replaceChild(o,s),o.append(s,n),this.container=o}createUpperCanvas(){const{el:t}=this.lower,e=jt();return e.className=t.className,e.classList.remove("lower-canvas"),e.classList.add("upper-canvas"),e.setAttribute("data-fabric","top"),e.style.cssText=t.style.cssText,e.setAttribute("draggable","true"),e}createContainerElement(){const t=li().createElement("div");return t.setAttribute("data-fabric","wrapper"),Hs(t,{position:"relative"}),ba(t),t}applyCanvasStyle(t,e){const{styles:i,allowTouchScrolling:s}=e;Hs(t,y(y({},i),{},{"touch-action":s?"manipulation":xt})),ba(t)}setDimensions(t,e){super.setDimensions(t,e);const{el:i,ctx:s}=this.upper;ya(i,s,t,e)}setCSSDimensions(t){super.setCSSDimensions(t),cr(this.upper.el,t),cr(this.container,t)}cleanupDOM(t){const e=this.container,{el:i}=this.lower,{el:s}=this.upper;super.cleanupDOM(t),e.removeChild(s),e.removeChild(i),e.parentNode&&e.parentNode.replaceChild(i,e)}dispose(){super.dispose(),ee().dispose(this.upper.el),delete this.upper,delete this.container}}class Ns extends Ni{constructor(){super(...arguments),m(this,"targets",[]),m(this,"_hoveredTargets",[]),m(this,"_objectsToRender",void 0),m(this,"_currentTransform",null),m(this,"_groupSelector",null),m(this,"contextTopDirty",!1)}static getDefaults(){return y(y({},super.getDefaults()),Ns.ownDefaults)}get upperCanvasEl(){var t;return(t=this.elements.upper)===null||t===void 0?void 0:t.el}get contextTop(){var t;return(t=this.elements.upper)===null||t===void 0?void 0:t.ctx}get wrapperEl(){return this.elements.container}initElements(t){this.elements=new ld(t,{allowTouchScrolling:this.allowTouchScrolling,containerClass:this.containerClass}),this._createCacheCanvas()}_onObjectAdded(t){this._objectsToRender=void 0,super._onObjectAdded(t)}_onObjectRemoved(t){this._objectsToRender=void 0,t===this._activeObject&&(this.fire("before:selection:cleared",{deselected:[t]}),this._discardActiveObject(),this.fire("selection:cleared",{deselected:[t]}),t.fire("deselected",{target:t})),t===this._hoveredTarget&&(this._hoveredTarget=void 0,this._hoveredTargets=[]),super._onObjectRemoved(t)}_onStackOrderChanged(){this._objectsToRender=void 0,super._onStackOrderChanged()}_chooseObjectsToRender(){const t=this._activeObject;return!this.preserveObjectStacking&&t?this._objects.filter(e=>!e.group&&e!==t).concat(t):this._objects}renderAll(){this.cancelRequestedRender(),this.destroyed||(!this.contextTopDirty||this._groupSelector||this.isDrawingMode||(this.clearContext(this.contextTop),this.contextTopDirty=!1),this.hasLostContext&&(this.renderTopLayer(this.contextTop),this.hasLostContext=!1),!this._objectsToRender&&(this._objectsToRender=this._chooseObjectsToRender()),this.renderCanvas(this.getContext(),this._objectsToRender))}renderTopLayer(t){t.save(),this.isDrawingMode&&this._isCurrentlyDrawing&&(this.freeDrawingBrush&&this.freeDrawingBrush._render(),this.contextTopDirty=!0),this.selection&&this._groupSelector&&(this._drawSelection(t),this.contextTopDirty=!0),t.restore()}renderTop(){const t=this.contextTop;this.clearContext(t),this.renderTopLayer(t),this.fire("after:render",{ctx:t})}setTargetFindTolerance(t){t=Math.round(t),this.targetFindTolerance=t;const e=this.getRetinaScaling(),i=Math.ceil((2*t+1)*e);this.pixelFindCanvasEl.width=this.pixelFindCanvasEl.height=i,this.pixelFindContext.scale(e,e)}isTargetTransparent(t,e,i){const s=this.targetFindTolerance,n=this.pixelFindContext;this.clearContext(n),n.save(),n.translate(-e+s,-i+s),n.transform(...this.viewportTransform);const o=t.selectionBackgroundColor;t.selectionBackgroundColor="",t.render(n),t.selectionBackgroundColor=o,n.restore();const a=Math.round(s*this.getRetinaScaling());return ih(n,a,a,a)}_isSelectionKeyPressed(t){const e=this.selectionKey;return!!e&&(Array.isArray(e)?!!e.find(i=>!!i&&t[i]===!0):t[e])}_shouldClearSelection(t,e){const i=this.getActiveObjects(),s=this._activeObject;return!!(!e||e&&s&&i.length>1&&i.indexOf(e)===-1&&s!==e&&!this._isSelectionKeyPressed(t)||e&&!e.evented||e&&!e.selectable&&s&&s!==e)}_shouldCenterTransform(t,e,i){if(!t)return;let s;return e===vs||e===mt||e===Ot||e===Bi?s=this.centeredScaling||t.centeredScaling:e===ir&&(s=this.centeredRotation||t.centeredRotation),s?!i:i}_getOriginFromCorner(t,e){const i={x:t.originX,y:t.originY};return e&&(["ml","tl","bl"].includes(e)?i.x=it:["mr","tr","br"].includes(e)&&(i.x=V),["tl","mt","tr"].includes(e)?i.y=tr:["bl","mb","br"].includes(e)&&(i.y=wt)),i}_setupCurrentTransform(t,e,i){var s;const n=e.group?He(this.getScenePoint(t),void 0,e.group.calcTransformMatrix()):this.getScenePoint(t),{key:o="",control:a}=e.getActiveControl()||{},h=i&&a?(s=a.getActionHandler(t,e,a))===null||s===void 0?void 0:s.bind(a):Vc,l=((g,f,p,v)=>{if(!f||!g)return"drag";const b=v.controls[f];return b.getActionName(p,b,v)})(i,o,t,e),c=t[this.centeredKey],u=this._shouldCenterTransform(e,l,c)?{x:j,y:j}:this._getOriginFromCorner(e,o),d={target:e,action:l,actionHandler:h,actionPerformed:!1,corner:o,scaleX:e.scaleX,scaleY:e.scaleY,skewX:e.skewX,skewY:e.skewY,offsetX:n.x-e.left,offsetY:n.y-e.top,originX:u.x,originY:u.y,ex:n.x,ey:n.y,lastX:n.x,lastY:n.y,theta:Q(e.angle),width:e.width,height:e.height,shiftKey:t.shiftKey,altKey:c,original:y(y({},ur(e)),{},{originX:u.x,originY:u.y})};this._currentTransform=d,this.fire("before:transform",{e:t,transform:d})}setCursor(t){this.upperCanvasEl.style.cursor=t}_drawSelection(t){const{x:e,y:i,deltaX:s,deltaY:n}=this._groupSelector,o=new C(e,i).transform(this.viewportTransform),a=new C(e+s,i+n).transform(this.viewportTransform),h=this.selectionLineWidth/2;let l=Math.min(o.x,a.x),c=Math.min(o.y,a.y),u=Math.max(o.x,a.x),d=Math.max(o.y,a.y);this.selectionColor&&(t.fillStyle=this.selectionColor,t.fillRect(l,c,u-l,d-c)),this.selectionLineWidth&&this.selectionBorderColor&&(t.lineWidth=this.selectionLineWidth,t.strokeStyle=this.selectionBorderColor,l+=h,c+=h,u-=h,d-=h,ft.prototype._setLineDash.call(this,t,this.selectionDashArray),t.strokeRect(l,c,u-l,d-c))}findTarget(t){if(this.skipTargetFind)return;const e=this.getViewportPoint(t),i=this._activeObject,s=this.getActiveObjects();if(this.targets=[],i&&s.length>=1){if(i.findControl(e,_s(t))||s.length>1&&this.searchPossibleTargets([i],e))return i;if(i===this.searchPossibleTargets([i],e)){if(this.preserveObjectStacking){const n=this.targets;this.targets=[];const o=this.searchPossibleTargets(this._objects,e);return t[this.altSelectionKey]&&o&&o!==i?(this.targets=n,i):o}return i}}return this.searchPossibleTargets(this._objects,e)}_pointIsInObjectSelectionArea(t,e){let i=t.getCoords();const s=this.getZoom(),n=t.padding/s;if(n){const[o,a,h,l]=i,c=Math.atan2(a.y-o.y,a.x-o.x),u=Vt(c)*n,d=Xt(c)*n,g=u+d,f=u-d;i=[new C(o.x-f,o.y-g),new C(a.x+g,a.y-f),new C(h.x+f,h.y+g),new C(l.x-g,l.y+f)]}return q.isPointInPolygon(e,i)}_checkTarget(t,e){return!!(t&&t.visible&&t.evented&&this._pointIsInObjectSelectionArea(t,He(e,void 0,this.viewportTransform))&&(!this.perPixelTargetFind&&!t.perPixelTargetFind||t.isEditing||!this.isTargetTransparent(t,e.x,e.y)))}_searchPossibleTargets(t,e){let i=t.length;for(;i--;){const s=t[i];if(this._checkTarget(s,e)){if(Cs(s)&&s.subTargetCheck){const n=this._searchPossibleTargets(s._objects,e);n&&this.targets.push(n)}return s}}}searchPossibleTargets(t,e){const i=this._searchPossibleTargets(t,e);if(i&&Cs(i)&&i.interactive&&this.targets[0]){const s=this.targets;for(let n=s.length-1;n>0;n--){const o=s[n];if(!Cs(o)||!o.interactive)return o}return s[0]}return i}getViewportPoint(t){return this._pointer?this._pointer:this.getPointer(t,!0)}getScenePoint(t){return this._absolutePointer?this._absolutePointer:this.getPointer(t)}getPointer(t){let e=arguments.length>1&&arguments[1]!==void 0&&arguments[1];const i=this.upperCanvasEl,s=i.getBoundingClientRect();let n=wa(t),o=s.width||0,a=s.height||0;o&&a||(wt in s&&tr in s&&(a=Math.abs(s.top-s.bottom)),it in s&&V in s&&(o=Math.abs(s.right-s.left))),this.calcOffset(),n.x=n.x-this._offset.left,n.y=n.y-this._offset.top,e||(n=He(n,void 0,this.viewportTransform));const h=this.getRetinaScaling();h!==1&&(n.x/=h,n.y/=h);const l=o===0||a===0?new C(1,1):new C(i.width/o,i.height/a);return n.multiply(l)}_setDimensionsImpl(t,e){this._resetTransformEventData(),super._setDimensionsImpl(t,e),this._isCurrentlyDrawing&&this.freeDrawingBrush&&this.freeDrawingBrush._setBrushStyles(this.contextTop)}_createCacheCanvas(){this.pixelFindCanvasEl=jt(),this.pixelFindContext=this.pixelFindCanvasEl.getContext("2d",{willReadFrequently:!0}),this.setTargetFindTolerance(this.targetFindTolerance)}getTopContext(){return this.elements.upper.ctx}getSelectionContext(){return this.elements.upper.ctx}getSelectionElement(){return this.elements.upper.el}getActiveObject(){return this._activeObject}getActiveObjects(){const t=this._activeObject;return ze(t)?t.getObjects():t?[t]:[]}_fireSelectionEvents(t,e){let i=!1,s=!1;const n=this.getActiveObjects(),o=[],a=[];t.forEach(h=>{n.includes(h)||(i=!0,h.fire("deselected",{e,target:h}),a.push(h))}),n.forEach(h=>{t.includes(h)||(i=!0,h.fire("selected",{e,target:h}),o.push(h))}),t.length>0&&n.length>0?(s=!0,i&&this.fire("selection:updated",{e,selected:o,deselected:a})):n.length>0?(s=!0,this.fire("selection:created",{e,selected:o})):t.length>0&&(s=!0,this.fire("selection:cleared",{e,deselected:a})),s&&(this._objectsToRender=void 0)}setActiveObject(t,e){const i=this.getActiveObjects(),s=this._setActiveObject(t,e);return this._fireSelectionEvents(i,e),s}_setActiveObject(t,e){const i=this._activeObject;return i!==t&&!(!this._discardActiveObject(e,t)&&this._activeObject)&&!t.onSelect({e})&&(this._activeObject=t,ze(t)&&i!==t&&t.set("canvas",this),t.setCoords(),!0)}_discardActiveObject(t,e){const i=this._activeObject;return!!i&&!i.onDeselect({e:t,object:e})&&(this._currentTransform&&this._currentTransform.target===i&&this.endCurrentTransform(t),ze(i)&&i===this._hoveredTarget&&(this._hoveredTarget=void 0),this._activeObject=void 0,!0)}discardActiveObject(t){const e=this.getActiveObjects(),i=this.getActiveObject();e.length&&this.fire("before:selection:cleared",{e:t,deselected:[i]});const s=this._discardActiveObject(t);return this._fireSelectionEvents(e,t),s}endCurrentTransform(t){const e=this._currentTransform;this._finalizeCurrentTransform(t),e&&e.target&&(e.target.isMoving=!1),this._currentTransform=null}_finalizeCurrentTransform(t){const e=this._currentTransform,i=e.target,s={e:t,target:i,transform:e,action:e.action};i._scaling&&(i._scaling=!1),i.setCoords(),e.actionPerformed&&(this.fire("object:modified",s),i.fire(ys,s))}setViewportTransform(t){super.setViewportTransform(t);const e=this._activeObject;e&&e.setCoords()}destroy(){const t=this._activeObject;ze(t)&&(t.removeAll(),t.dispose()),delete this._activeObject,super.destroy(),this.pixelFindContext=null,this.pixelFindCanvasEl=void 0}clear(){this.discardActiveObject(),this._activeObject=void 0,this.clearContext(this.contextTop),super.clear()}drawControls(t){const e=this._activeObject;e&&e._renderControls(t)}_toObject(t,e,i){const s=this._realizeGroupTransformOnObject(t),n=super._toObject(t,e,i);return t.set(s),n}_realizeGroupTransformOnObject(t){const{group:e}=t;if(e&&ze(e)&&this._activeObject===e){const i=$e(t,["angle","flipX","flipY",V,mt,Ot,ci,ui,wt]);return _a(t,e.calcOwnMatrix()),i}return{}}_setSVGObject(t,e,i){const s=this._realizeGroupTransformOnObject(e);super._setSVGObject(t,e,i),e.set(s)}}m(Ns,"ownDefaults",{uniformScaling:!0,uniScaleKey:"shiftKey",centeredScaling:!1,centeredRotation:!1,centeredKey:"altKey",altActionKey:"shiftKey",selection:!0,selectionKey:"shiftKey",selectionColor:"rgba(100, 100, 255, 0.3)",selectionDashArray:[],selectionBorderColor:"rgba(255, 255, 255, 0.3)",selectionLineWidth:1,selectionFullyContained:!1,hoverCursor:"move",moveCursor:"move",defaultCursor:"default",freeDrawingCursor:"crosshair",notAllowedCursor:"not-allowed",perPixelTargetFind:!1,targetFindTolerance:0,skipTargetFind:!1,stopContextMenu:!1,fireRightClick:!1,fireMiddleClick:!1,enablePointerEvents:!1,containerClass:"canvas-container",preserveObjectStacking:!1});class cd{constructor(t){m(this,"targets",[]),m(this,"__disposer",void 0);const e=()=>{const{hiddenTextarea:s}=t.getActiveObject()||{};s&&s.focus()},i=t.upperCanvasEl;i.addEventListener("click",e),this.__disposer=()=>i.removeEventListener("click",e)}exitTextEditing(){this.target=void 0,this.targets.forEach(t=>{t.isEditing&&t.exitEditing()})}add(t){this.targets.push(t)}remove(t){this.unregister(t),Be(this.targets,t)}register(t){this.target=t}unregister(t){t===this.target&&(this.target=void 0)}onMouseMove(t){var e;!((e=this.target)===null||e===void 0)&&e.isEditing&&this.target.updateSelectionOnMouseMove(t)}clear(){this.targets=[],this.target=void 0}dispose(){this.clear(),this.__disposer(),delete this.__disposer}}const ud=["target","oldTarget","fireCanvas","e"],Pt={passive:!1},Si=(r,t)=>{const e=r.getViewportPoint(t),i=r.getScenePoint(t);return{viewportPoint:e,scenePoint:i,pointer:e,absolutePointer:i}},Me=function(r){for(var t=arguments.length,e=new Array(t>1?t-1:0),i=1;i<t;i++)e[i-1]=arguments[i];return r.addEventListener(...e)},$t=function(r){for(var t=arguments.length,e=new Array(t>1?t-1:0),i=1;i<t;i++)e[i-1]=arguments[i];return r.removeEventListener(...e)},dd={mouse:{in:"over",out:"out",targetIn:"mouseover",targetOut:"mouseout",canvasIn:"mouse:over",canvasOut:"mouse:out"},drag:{in:"enter",out:"leave",targetIn:"dragenter",targetOut:"dragleave",canvasIn:"drag:enter",canvasOut:"drag:leave"}};class jr extends Ns{constructor(t){super(t,arguments.length>1&&arguments[1]!==void 0?arguments[1]:{}),m(this,"_isClick",void 0),m(this,"textEditingManager",new cd(this)),["_onMouseDown","_onTouchStart","_onMouseMove","_onMouseUp","_onTouchEnd","_onResize","_onMouseWheel","_onMouseOut","_onMouseEnter","_onContextMenu","_onClick","_onDragStart","_onDragEnd","_onDragProgress","_onDragOver","_onDragEnter","_onDragLeave","_onDrop"].forEach(e=>{this[e]=this[e].bind(this)}),this.addOrRemove(Me,"add")}_getEventPrefix(){return this.enablePointerEvents?"pointer":"mouse"}addOrRemove(t,e){const i=this.upperCanvasEl,s=this._getEventPrefix();t(va(i),"resize",this._onResize),t(i,s+"down",this._onMouseDown),t(i,"".concat(s,"move"),this._onMouseMove,Pt),t(i,"".concat(s,"out"),this._onMouseOut),t(i,"".concat(s,"enter"),this._onMouseEnter),t(i,"wheel",this._onMouseWheel),t(i,"contextmenu",this._onContextMenu),t(i,"click",this._onClick),t(i,"dblclick",this._onClick),t(i,"dragstart",this._onDragStart),t(i,"dragend",this._onDragEnd),t(i,"dragover",this._onDragOver),t(i,"dragenter",this._onDragEnter),t(i,"dragleave",this._onDragLeave),t(i,"drop",this._onDrop),this.enablePointerEvents||t(i,"touchstart",this._onTouchStart,Pt)}removeListeners(){this.addOrRemove($t,"remove");const t=this._getEventPrefix(),e=Yt(this.upperCanvasEl);$t(e,"".concat(t,"up"),this._onMouseUp),$t(e,"touchend",this._onTouchEnd,Pt),$t(e,"".concat(t,"move"),this._onMouseMove,Pt),$t(e,"touchmove",this._onMouseMove,Pt),clearTimeout(this._willAddMouseDown)}_onMouseWheel(t){this.__onMouseWheel(t)}_onMouseOut(t){const e=this._hoveredTarget,i=y({e:t},Si(this,t));this.fire("mouse:out",y(y({},i),{},{target:e})),this._hoveredTarget=void 0,e&&e.fire("mouseout",y({},i)),this._hoveredTargets.forEach(s=>{this.fire("mouse:out",y(y({},i),{},{target:s})),s&&s.fire("mouseout",y({},i))}),this._hoveredTargets=[]}_onMouseEnter(t){this._currentTransform||this.findTarget(t)||(this.fire("mouse:over",y({e:t},Si(this,t))),this._hoveredTarget=void 0,this._hoveredTargets=[])}_onDragStart(t){this._isClick=!1;const e=this.getActiveObject();if(e&&e.onDragStart(t)){this._dragSource=e;const i={e:t,target:e};return this.fire("dragstart",i),e.fire("dragstart",i),void Me(this.upperCanvasEl,"drag",this._onDragProgress)}xa(t)}_renderDragEffects(t,e,i){let s=!1;const n=this._dropTarget;n&&n!==e&&n!==i&&(n.clearContextTop(),s=!0),e==null||e.clearContextTop(),i!==e&&(i==null||i.clearContextTop());const o=this.contextTop;o.save(),o.transform(...this.viewportTransform),e&&(o.save(),e.transform(o),e.renderDragSourceEffect(t),o.restore(),s=!0),i&&(o.save(),i.transform(o),i.renderDropTargetEffect(t),o.restore(),s=!0),o.restore(),s&&(this.contextTopDirty=!0)}_onDragEnd(t){const e=!!t.dataTransfer&&t.dataTransfer.dropEffect!==xt,i=e?this._activeObject:void 0,s={e:t,target:this._dragSource,subTargets:this.targets,dragSource:this._dragSource,didDrop:e,dropTarget:i};$t(this.upperCanvasEl,"drag",this._onDragProgress),this.fire("dragend",s),this._dragSource&&this._dragSource.fire("dragend",s),delete this._dragSource,this._onMouseUp(t)}_onDragProgress(t){const e={e:t,target:this._dragSource,dragSource:this._dragSource,dropTarget:this._draggedoverTarget};this.fire("drag",e),this._dragSource&&this._dragSource.fire("drag",e)}findDragTargets(t){return this.targets=[],{target:this._searchPossibleTargets(this._objects,this.getViewportPoint(t)),targets:[...this.targets]}}_onDragOver(t){const e="dragover",{target:i,targets:s}=this.findDragTargets(t),n=this._dragSource,o={e:t,target:i,subTargets:s,dragSource:n,canDrop:!1,dropTarget:void 0};let a;this.fire(e,o),this._fireEnterLeaveEvents(i,o),i&&(i.canDrop(t)&&(a=i),i.fire(e,o));for(let h=0;h<s.length;h++){const l=s[h];l.canDrop(t)&&(a=l),l.fire(e,o)}this._renderDragEffects(t,n,a),this._dropTarget=a}_onDragEnter(t){const{target:e,targets:i}=this.findDragTargets(t),s={e:t,target:e,subTargets:i,dragSource:this._dragSource};this.fire("dragenter",s),this._fireEnterLeaveEvents(e,s)}_onDragLeave(t){const e={e:t,target:this._draggedoverTarget,subTargets:this.targets,dragSource:this._dragSource};this.fire("dragleave",e),this._fireEnterLeaveEvents(void 0,e),this._renderDragEffects(t,this._dragSource),this._dropTarget=void 0,this.targets=[],this._hoveredTargets=[]}_onDrop(t){const{target:e,targets:i}=this.findDragTargets(t),s=this._basicEventHandler("drop:before",y({e:t,target:e,subTargets:i,dragSource:this._dragSource},Si(this,t)));s.didDrop=!1,s.dropTarget=void 0,this._basicEventHandler("drop",s),this.fire("drop:after",s)}_onContextMenu(t){const e=this.findTarget(t),i=this.targets||[],s=this._basicEventHandler("contextmenu:before",{e:t,target:e,subTargets:i});return this.stopContextMenu&&xa(t),this._basicEventHandler("contextmenu",s),!1}_onClick(t){const e=t.detail;e>3||e<2||(this._cacheTransformEventData(t),e==2&&t.type==="dblclick"&&this._handleEvent(t,"dblclick"),e==3&&this._handleEvent(t,"tripleclick"),this._resetTransformEventData())}getPointerId(t){const e=t.changedTouches;return e?e[0]&&e[0].identifier:this.enablePointerEvents?t.pointerId:-1}_isMainEvent(t){return t.isPrimary===!0||t.isPrimary!==!1&&(t.type==="touchend"&&t.touches.length===0||!t.changedTouches||t.changedTouches[0].identifier===this.mainTouchId)}_onTouchStart(t){let e=!this.allowTouchScrolling;const i=this._activeObject;this.mainTouchId===void 0&&(this.mainTouchId=this.getPointerId(t)),this.__onMouseDown(t),(this.isDrawingMode||i&&this._target===i)&&(e=!0),e&&t.preventDefault(),this._resetTransformEventData();const s=this.upperCanvasEl,n=this._getEventPrefix(),o=Yt(s);Me(o,"touchend",this._onTouchEnd,Pt),e&&Me(o,"touchmove",this._onMouseMove,Pt),$t(s,"".concat(n,"down"),this._onMouseDown)}_onMouseDown(t){this.__onMouseDown(t),this._resetTransformEventData();const e=this.upperCanvasEl,i=this._getEventPrefix();$t(e,"".concat(i,"move"),this._onMouseMove,Pt);const s=Yt(e);Me(s,"".concat(i,"up"),this._onMouseUp),Me(s,"".concat(i,"move"),this._onMouseMove,Pt)}_onTouchEnd(t){if(t.touches.length>0)return;this.__onMouseUp(t),this._resetTransformEventData(),delete this.mainTouchId;const e=this._getEventPrefix(),i=Yt(this.upperCanvasEl);$t(i,"touchend",this._onTouchEnd,Pt),$t(i,"touchmove",this._onMouseMove,Pt),this._willAddMouseDown&&clearTimeout(this._willAddMouseDown),this._willAddMouseDown=setTimeout(()=>{Me(this.upperCanvasEl,"".concat(e,"down"),this._onMouseDown),this._willAddMouseDown=0},400)}_onMouseUp(t){this.__onMouseUp(t),this._resetTransformEventData();const e=this.upperCanvasEl,i=this._getEventPrefix();if(this._isMainEvent(t)){const s=Yt(this.upperCanvasEl);$t(s,"".concat(i,"up"),this._onMouseUp),$t(s,"".concat(i,"move"),this._onMouseMove,Pt),Me(e,"".concat(i,"move"),this._onMouseMove,Pt)}}_onMouseMove(t){const e=this.getActiveObject();!this.allowTouchScrolling&&(!e||!e.shouldStartDragging(t))&&t.preventDefault&&t.preventDefault(),this.__onMouseMove(t)}_onResize(){this.calcOffset(),this._resetTransformEventData()}_shouldRender(t){const e=this.getActiveObject();return!!e!=!!t||e&&t&&e!==t}__onMouseUp(t){var e;this._cacheTransformEventData(t),this._handleEvent(t,"up:before");const i=this._currentTransform,s=this._isClick,n=this._target,{button:o}=t;if(o)return(this.fireMiddleClick&&o===1||this.fireRightClick&&o===2)&&this._handleEvent(t,"up"),void this._resetTransformEventData();if(this.isDrawingMode&&this._isCurrentlyDrawing)return void this._onMouseUpInDrawingMode(t);if(!this._isMainEvent(t))return;let a,h,l=!1;if(i&&(this._finalizeCurrentTransform(t),l=i.actionPerformed),!s){const c=n===this._activeObject;this.handleSelection(t),l||(l=this._shouldRender(n)||!c&&n===this._activeObject)}if(n){const c=n.findControl(this.getViewportPoint(t),_s(t)),{key:u,control:d}=c||{};if(h=u,n.selectable&&n!==this._activeObject&&n.activeOn==="up")this.setActiveObject(n,t),l=!0;else if(d){const g=d.getMouseUpHandler(t,n,d);g&&(a=this.getScenePoint(t),g.call(d,t,i,a.x,a.y))}n.isMoving=!1}if(i&&(i.target!==n||i.corner!==h)){const c=i.target&&i.target.controls[i.corner],u=c&&c.getMouseUpHandler(t,i.target,c);a=a||this.getScenePoint(t),u&&u.call(c,t,i,a.x,a.y)}this._setCursorFromEvent(t,n),this._handleEvent(t,"up"),this._groupSelector=null,this._currentTransform=null,n&&(n.__corner=void 0),l?this.requestRenderAll():s||(e=this._activeObject)!==null&&e!==void 0&&e.isEditing||this.renderTop()}_basicEventHandler(t,e){const{target:i,subTargets:s=[]}=e;this.fire(t,e),i&&i.fire(t,e);for(let n=0;n<s.length;n++)s[n]!==i&&s[n].fire(t,e);return e}_handleEvent(t,e,i){const s=this._target,n=this.targets||[],o=y(y(y({e:t,target:s,subTargets:n},Si(this,t)),{},{transform:this._currentTransform},e==="up:before"||e==="up"?{isClick:this._isClick,currentTarget:this.findTarget(t),currentSubTargets:this.targets}:{}),e==="down:before"||e==="down"?i:{});this.fire("mouse:".concat(e),o),s&&s.fire("mouse".concat(e),o);for(let a=0;a<n.length;a++)n[a]!==s&&n[a].fire("mouse".concat(e),o)}_onMouseDownInDrawingMode(t){this._isCurrentlyDrawing=!0,this.getActiveObject()&&(this.discardActiveObject(t),this.requestRenderAll());const e=this.getScenePoint(t);this.freeDrawingBrush&&this.freeDrawingBrush.onMouseDown(e,{e:t,pointer:e}),this._handleEvent(t,"down",{alreadySelected:!1})}_onMouseMoveInDrawingMode(t){if(this._isCurrentlyDrawing){const e=this.getScenePoint(t);this.freeDrawingBrush&&this.freeDrawingBrush.onMouseMove(e,{e:t,pointer:e})}this.setCursor(this.freeDrawingCursor),this._handleEvent(t,"move")}_onMouseUpInDrawingMode(t){const e=this.getScenePoint(t);this.freeDrawingBrush?this._isCurrentlyDrawing=!!this.freeDrawingBrush.onMouseUp({e:t,pointer:e}):this._isCurrentlyDrawing=!1,this._handleEvent(t,"up")}__onMouseDown(t){this._isClick=!0,this._cacheTransformEventData(t),this._handleEvent(t,"down:before");let e=this._target,i=!!e&&e===this._activeObject;const{button:s}=t;if(s)return(this.fireMiddleClick&&s===1||this.fireRightClick&&s===2)&&this._handleEvent(t,"down",{alreadySelected:i}),void this._resetTransformEventData();if(this.isDrawingMode)return void this._onMouseDownInDrawingMode(t);if(!this._isMainEvent(t)||this._currentTransform)return;let n=this._shouldRender(e),o=!1;if(this.handleMultiSelection(t,e)?(e=this._activeObject,o=!0,n=!0):this._shouldClearSelection(t,e)&&this.discardActiveObject(t),this.selection&&(!e||!e.selectable&&!e.isEditing&&e!==this._activeObject)){const a=this.getScenePoint(t);this._groupSelector={x:a.x,y:a.y,deltaY:0,deltaX:0}}if(i=!!e&&e===this._activeObject,e){e.selectable&&e.activeOn==="down"&&this.setActiveObject(e,t);const a=e.findControl(this.getViewportPoint(t),_s(t));if(e===this._activeObject&&(a||!o)){this._setupCurrentTransform(t,e,i);const h=a?a.control:void 0,l=this.getScenePoint(t),c=h&&h.getMouseDownHandler(t,e,h);c&&c.call(h,t,this._currentTransform,l.x,l.y)}}n&&(this._objectsToRender=void 0),this._handleEvent(t,"down",{alreadySelected:i}),n&&this.requestRenderAll()}_resetTransformEventData(){this._target=this._pointer=this._absolutePointer=void 0}_cacheTransformEventData(t){this._resetTransformEventData(),this._pointer=this.getViewportPoint(t),this._absolutePointer=He(this._pointer,void 0,this.viewportTransform),this._target=this._currentTransform?this._currentTransform.target:this.findTarget(t)}__onMouseMove(t){if(this._isClick=!1,this._cacheTransformEventData(t),this._handleEvent(t,"move:before"),this.isDrawingMode)return void this._onMouseMoveInDrawingMode(t);if(!this._isMainEvent(t))return;const e=this._groupSelector;if(e){const i=this.getScenePoint(t);e.deltaX=i.x-e.x,e.deltaY=i.y-e.y,this.renderTop()}else if(this._currentTransform)this._transformObject(t);else{const i=this.findTarget(t);this._setCursorFromEvent(t,i),this._fireOverOutEvents(t,i)}this.textEditingManager.onMouseMove(t),this._handleEvent(t,"move"),this._resetTransformEventData()}_fireOverOutEvents(t,e){const i=this._hoveredTarget,s=this._hoveredTargets,n=this.targets,o=Math.max(s.length,n.length);this.fireSyntheticInOutEvents("mouse",{e:t,target:e,oldTarget:i,fireCanvas:!0});for(let a=0;a<o;a++)this.fireSyntheticInOutEvents("mouse",{e:t,target:n[a],oldTarget:s[a]});this._hoveredTarget=e,this._hoveredTargets=this.targets.concat()}_fireEnterLeaveEvents(t,e){const i=this._draggedoverTarget,s=this._hoveredTargets,n=this.targets,o=Math.max(s.length,n.length);this.fireSyntheticInOutEvents("drag",y(y({},e),{},{target:t,oldTarget:i,fireCanvas:!0}));for(let a=0;a<o;a++)this.fireSyntheticInOutEvents("drag",y(y({},e),{},{target:n[a],oldTarget:s[a]}));this._draggedoverTarget=t}fireSyntheticInOutEvents(t,e){let{target:i,oldTarget:s,fireCanvas:n,e:o}=e,a=U(e,ud);const{targetIn:h,targetOut:l,canvasIn:c,canvasOut:u}=dd[t],d=s!==i;if(s&&d){const g=y(y({},a),{},{e:o,target:s,nextTarget:i},Si(this,o));n&&this.fire(u,g),s.fire(l,g)}if(i&&d){const g=y(y({},a),{},{e:o,target:i,previousTarget:s},Si(this,o));n&&this.fire(c,g),i.fire(h,g)}}__onMouseWheel(t){this._cacheTransformEventData(t),this._handleEvent(t,"wheel"),this._resetTransformEventData()}_transformObject(t){const e=this.getScenePoint(t),i=this._currentTransform,s=i.target,n=s.group?He(e,void 0,s.group.calcTransformMatrix()):e;i.shiftKey=t.shiftKey,i.altKey=!!this.centeredKey&&t[this.centeredKey],this._performTransformAction(t,i,n),i.actionPerformed&&this.requestRenderAll()}_performTransformAction(t,e,i){const{action:s,actionHandler:n,target:o}=e,a=!!n&&n(t,e,i.x,i.y);a&&o.setCoords(),s==="drag"&&a&&(e.target.isMoving=!0,this.setCursor(e.target.moveCursor||this.moveCursor)),e.actionPerformed=e.actionPerformed||a}_setCursorFromEvent(t,e){if(!e)return void this.setCursor(this.defaultCursor);let i=e.hoverCursor||this.hoverCursor;const s=ze(this._activeObject)?this._activeObject:null,n=(!s||e.group!==s)&&e.findControl(this.getViewportPoint(t));if(n){const o=n.control;this.setCursor(o.cursorStyleHandler(t,o,e))}else e.subTargetCheck&&this.targets.concat().reverse().map(o=>{i=o.hoverCursor||i}),this.setCursor(i)}handleMultiSelection(t,e){const i=this._activeObject,s=ze(i);if(i&&this._isSelectionKeyPressed(t)&&this.selection&&e&&e.selectable&&(i!==e||s)&&(s||!e.isDescendantOf(i)&&!i.isDescendantOf(e))&&!e.onSelect({e:t})&&!i.getActiveControl()){if(s){const n=i.getObjects();if(e===i){const o=this.getViewportPoint(t);if(!(e=this.searchPossibleTargets(n,o)||this.searchPossibleTargets(this._objects,o))||!e.selectable)return!1}e.group===i?(i.remove(e),this._hoveredTarget=e,this._hoveredTargets=[...this.targets],i.size()===1&&this._setActiveObject(i.item(0),t)):(i.multiSelectAdd(e),this._hoveredTarget=i,this._hoveredTargets=[...this.targets]),this._fireSelectionEvents(n,t)}else{i.isEditing&&i.exitEditing();const n=new(D.getClass("ActiveSelection"))([],{canvas:this});n.multiSelectAdd(i,e),this._hoveredTarget=n,this._setActiveObject(n,t),this._fireSelectionEvents([i],t)}return!0}return!1}handleSelection(t){if(!this.selection||!this._groupSelector)return!1;const{x:e,y:i,deltaX:s,deltaY:n}=this._groupSelector,o=new C(e,i),a=o.add(new C(s,n)),h=o.min(a),l=o.max(a).subtract(h),c=this.collectObjects({left:h.x,top:h.y,width:l.x,height:l.y},{includeIntersecting:!this.selectionFullyContained}),u=o.eq(a)?c[0]?[c[0]]:[]:c.length>1?c.filter(d=>!d.onSelect({e:t})).reverse():c;if(u.length===1)this.setActiveObject(u[0],t);else if(u.length>1){const d=D.getClass("ActiveSelection");this.setActiveObject(new d(u,{canvas:this}),t)}return this._groupSelector=null,!0}clear(){this.textEditingManager.clear(),super.clear()}destroy(){this.removeListeners(),this.textEditingManager.dispose(),super.destroy()}}const Lh={x1:0,y1:0,x2:0,y2:0},gd=y(y({},Lh),{},{r1:0,r2:0}),Ti=(r,t)=>isNaN(r)&&typeof t=="number"?t:r,fd=/^(\d+\.\d+)%|(\d+)%$/;function Rh(r){return r&&fd.test(r)}function Ih(r,t){const e=typeof r=="number"?r:typeof r=="string"?parseFloat(r)/(Rh(r)?100:1):NaN;return Ve(0,Ti(e,t),1)}const pd=/\s*;\s*/,md=/\s*:\s*/;function vd(r,t){let e,i;const s=r.getAttribute("style");if(s){const o=s.split(pd);o[o.length-1]===""&&o.pop();for(let a=o.length;a--;){const[h,l]=o[a].split(md).map(c=>c.trim());h==="stop-color"?e=l:h==="stop-opacity"&&(i=l)}}const n=new N(e||r.getAttribute("stop-color")||"rgb(0,0,0)");return{offset:Ih(r.getAttribute("offset"),0),color:n.toRgb(),opacity:Ti(parseFloat(i||r.getAttribute("stop-opacity")||""),1)*n.getAlpha()*t}}function yd(r,t){const e=[],i=r.getElementsByTagName("stop"),s=Ih(t,1);for(let n=i.length;n--;)e.push(vd(i[n],s));return e}function jh(r){return r.nodeName==="linearGradient"||r.nodeName==="LINEARGRADIENT"?"linear":"radial"}function Bh(r){return r.getAttribute("gradientUnits")==="userSpaceOnUse"?"pixels":"percentage"}function qt(r,t){return r.getAttribute(t)}function bd(r,t){return function(e,i){let s,{width:n,height:o,gradientUnits:a}=i;return Object.keys(e).reduce((h,l)=>{const c=e[l];return c==="Infinity"?s=1:c==="-Infinity"?s=0:(s=typeof c=="string"?parseFloat(c):c,typeof c=="string"&&Rh(c)&&(s*=.01,a==="pixels"&&(l!=="x1"&&l!=="x2"&&l!=="r2"||(s*=n),l!=="y1"&&l!=="y2"||(s*=o)))),h[l]=s,h},{})}(jh(r)==="linear"?function(e){return{x1:qt(e,"x1")||0,y1:qt(e,"y1")||0,x2:qt(e,"x2")||"100%",y2:qt(e,"y2")||0}}(r):function(e){return{x1:qt(e,"fx")||qt(e,"cx")||"50%",y1:qt(e,"fy")||qt(e,"cy")||"50%",r1:0,x2:qt(e,"cx")||"50%",y2:qt(e,"cy")||"50%",r2:qt(e,"r")||"50%"}}(r),y(y({},t),{},{gradientUnits:Bh(r)}))}class Ws{constructor(t){const{type:e="linear",gradientUnits:i="pixels",coords:s={},colorStops:n=[],offsetX:o=0,offsetY:a=0,gradientTransform:h,id:l}=t||{};Object.assign(this,{type:e,gradientUnits:i,coords:y(y({},e==="radial"?gd:Lh),s),colorStops:n,offsetX:o,offsetY:a,gradientTransform:h,id:l?"".concat(l,"_").concat(ke()):ke()})}addColorStop(t){for(const e in t){const i=new N(t[e]);this.colorStops.push({offset:parseFloat(e),color:i.toRgb(),opacity:i.getAlpha()})}return this}toObject(t){return y(y({},$e(this,t)),{},{type:this.type,coords:y({},this.coords),colorStops:this.colorStops.map(e=>y({},e)),offsetX:this.offsetX,offsetY:this.offsetY,gradientUnits:this.gradientUnits,gradientTransform:this.gradientTransform?[...this.gradientTransform]:void 0})}toSVG(t){let{additionalTransform:e}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const i=[],s=this.gradientTransform?this.gradientTransform.concat():pt.concat(),n=this.gradientUnits==="pixels"?"userSpaceOnUse":"objectBoundingBox",o=this.colorStops.map(u=>y({},u)).sort((u,d)=>u.offset-d.offset);let a=-this.offsetX,h=-this.offsetY;var l;n==="objectBoundingBox"?(a/=t.width,h/=t.height):(a+=t.width/2,h+=t.height/2),(l=t)&&typeof l._renderPathCommands=="function"&&this.gradientUnits!=="percentage"&&(a-=t.pathOffset.x,h-=t.pathOffset.y),s[4]-=a,s[5]-=h;const c=['id="SVGID_'.concat(this.id,'"'),'gradientUnits="'.concat(n,'"'),'gradientTransform="'.concat(e?e+" ":"").concat(mi(s),'"'),""].join(" ");if(this.type==="linear"){const{x1:u,y1:d,x2:g,y2:f}=this.coords;i.push("<linearGradient ",c,' x1="',u,'" y1="',d,'" x2="',g,'" y2="',f,`">
`)}else if(this.type==="radial"){const{x1:u,y1:d,x2:g,y2:f,r1:p,r2:v}=this.coords,b=p>v;i.push("<radialGradient ",c,' cx="',b?u:g,'" cy="',b?d:f,'" r="',b?p:v,'" fx="',b?g:u,'" fy="',b?f:d,`">
`),b&&(o.reverse(),o.forEach(x=>{x.offset=1-x.offset}));const w=Math.min(p,v);if(w>0){const x=w/Math.max(p,v);o.forEach(_=>{_.offset+=x*(1-_.offset)})}}return o.forEach(u=>{let{color:d,offset:g,opacity:f}=u;i.push("<stop ",'offset="',100*g+"%",'" style="stop-color:',d,f!==void 0?";stop-opacity: "+f:";",`"/>
`)}),i.push(this.type==="linear"?"</linearGradient>":"</radialGradient>",`
`),i.join("")}toLive(t){const{x1:e,y1:i,x2:s,y2:n,r1:o,r2:a}=this.coords,h=this.type==="linear"?t.createLinearGradient(e,i,s,n):t.createRadialGradient(e,i,o,s,n,a);return this.colorStops.forEach(l=>{let{color:c,opacity:u,offset:d}=l;h.addColorStop(d,u!==void 0?new N(c).setAlpha(u).toRgba():c)}),h}static async fromObject(t){const{colorStops:e,gradientTransform:i}=t;return new this(y(y({},t),{},{colorStops:e?e.map(s=>y({},s)):void 0,gradientTransform:i?[...i]:void 0}))}static fromElement(t,e,i){const s=Bh(t),n=e._findCenterFromElement();return new this(y({id:t.getAttribute("id")||void 0,type:jh(t),coords:bd(t,{width:i.viewBoxWidth||i.width,height:i.viewBoxHeight||i.height}),colorStops:yd(t,i.opacity),gradientUnits:s,gradientTransform:Mr(t.getAttribute("gradientTransform")||"")},s==="pixels"?{offsetX:e.width/2-n.x,offsetY:e.height/2-n.y}:{offsetX:0,offsetY:0}))}}m(Ws,"type","Gradient"),D.setClass(Ws,"gradient"),D.setClass(Ws,"linear"),D.setClass(Ws,"radial");const Cd=["type","source","patternTransform"];class Br{get type(){return"pattern"}set type(t){Se("warn","Setting type has no effect",t)}constructor(t){m(this,"repeat","repeat"),m(this,"offsetX",0),m(this,"offsetY",0),m(this,"crossOrigin",""),this.id=ke(),Object.assign(this,t)}isImageSource(){return!!this.source&&typeof this.source.src=="string"}isCanvasSource(){return!!this.source&&!!this.source.toDataURL}sourceToString(){return this.isImageSource()?this.source.src:this.isCanvasSource()?this.source.toDataURL():""}toLive(t){return this.source&&(!this.isImageSource()||this.source.complete&&this.source.naturalWidth!==0&&this.source.naturalHeight!==0)?t.createPattern(this.source,this.repeat):null}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];const{repeat:e,crossOrigin:i}=this;return y(y({},$e(this,t)),{},{type:"pattern",source:this.sourceToString(),repeat:e,crossOrigin:i,offsetX:G(this.offsetX,W.NUM_FRACTION_DIGITS),offsetY:G(this.offsetY,W.NUM_FRACTION_DIGITS),patternTransform:this.patternTransform?[...this.patternTransform]:null})}toSVG(t){let{width:e,height:i}=t;const{source:s,repeat:n,id:o}=this,a=Ti(this.offsetX/e,0),h=Ti(this.offsetY/i,0),l=n==="repeat-y"||n==="no-repeat"?1+Math.abs(a||0):Ti(s.width/e,0),c=n==="repeat-x"||n==="no-repeat"?1+Math.abs(h||0):Ti(s.height/i,0);return['<pattern id="SVGID_'.concat(o,'" x="').concat(a,'" y="').concat(h,'" width="').concat(l,'" height="').concat(c,'">'),'<image x="0" y="0" width="'.concat(s.width,'" height="').concat(s.height,'" xlink:href="').concat(this.sourceToString(),'"></image>'),"</pattern>",""].join(`
`)}static async fromObject(t,e){let{type:i,source:s,patternTransform:n}=t,o=U(t,Cd);const a=await zi(s,y(y({},e),{},{crossOrigin:o.crossOrigin}));return new this(y(y({},o),{},{patternTransform:n&&n.slice(0),source:a}))}}m(Br,"type","Pattern"),D.setClass(Br),D.setClass(Br,"pattern");class wd{constructor(t){m(this,"color","rgb(0, 0, 0)"),m(this,"width",1),m(this,"shadow",null),m(this,"strokeLineCap","round"),m(this,"strokeLineJoin","round"),m(this,"strokeMiterLimit",10),m(this,"strokeDashArray",null),m(this,"limitedToCanvasSize",!1),this.canvas=t}_setBrushStyles(t){t.strokeStyle=this.color,t.lineWidth=this.width,t.lineCap=this.strokeLineCap,t.miterLimit=this.strokeMiterLimit,t.lineJoin=this.strokeLineJoin,t.setLineDash(this.strokeDashArray||[])}_saveAndTransform(t){const e=this.canvas.viewportTransform;t.save(),t.transform(e[0],e[1],e[2],e[3],e[4],e[5])}needsFullRender(){return new N(this.color).getAlpha()<1||!!this.shadow}_setShadow(){if(!this.shadow||!this.canvas)return;const t=this.canvas,e=this.shadow,i=t.contextTop,s=t.getZoom()*t.getRetinaScaling();i.shadowColor=e.color,i.shadowBlur=e.blur*s,i.shadowOffsetX=e.offsetX*s,i.shadowOffsetY=e.offsetY*s}_resetShadow(){const t=this.canvas.contextTop;t.shadowColor="",t.shadowBlur=t.shadowOffsetX=t.shadowOffsetY=0}_isOutSideCanvas(t){return t.x<0||t.x>this.canvas.getWidth()||t.y<0||t.y>this.canvas.getHeight()}}const xd=["path","left","top"],_d=["d"];class pe extends ft{constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{path:i,left:s,top:n}=e,o=U(e,xd);super(),Object.assign(this,pe.ownDefaults),this.setOptions(o),this._setPath(t||[],!0),typeof s=="number"&&this.set(V,s),typeof n=="number"&&this.set(wt,n)}_setPath(t,e){this.path=_h(Array.isArray(t)?t:Ah(t)),this.setBoundingBox(e)}_findCenterFromElement(){const t=this._calcBoundsFromPath();return new C(t.left+t.width/2,t.top+t.height/2)}_renderPathCommands(t){const e=-this.pathOffset.x,i=-this.pathOffset.y;t.beginPath();for(const s of this.path)switch(s[0]){case"L":t.lineTo(s[1]+e,s[2]+i);break;case"M":t.moveTo(s[1]+e,s[2]+i);break;case"C":t.bezierCurveTo(s[1]+e,s[2]+i,s[3]+e,s[4]+i,s[5]+e,s[6]+i);break;case"Q":t.quadraticCurveTo(s[1]+e,s[2]+i,s[3]+e,s[4]+i);break;case"Z":t.closePath()}}_render(t){this._renderPathCommands(t),this._renderPaintInOrder(t)}toString(){return"#<Path (".concat(this.complexity(),'): { "top": ').concat(this.top,', "left": ').concat(this.left," }>")}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return y(y({},super.toObject(t)),{},{path:this.path.map(e=>e.slice())})}toDatalessObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];const e=this.toObject(t);return this.sourcePath&&(delete e.path,e.sourcePath=this.sourcePath),e}_toSVG(){const t=Ir(this.path,W.NUM_FRACTION_DIGITS);return["<path ","COMMON_PARTS",'d="'.concat(t,`" stroke-linecap="round" />
`)]}_getOffsetTransform(){const t=W.NUM_FRACTION_DIGITS;return" translate(".concat(G(-this.pathOffset.x,t),", ").concat(G(-this.pathOffset.y,t),")")}toClipPathSVG(t){const e=this._getOffsetTransform();return"	"+this._createBaseClipPathSVGMarkup(this._toSVG(),{reviver:t,additionalTransform:e})}toSVG(t){const e=this._getOffsetTransform();return this._createBaseSVGMarkup(this._toSVG(),{reviver:t,additionalTransform:e})}complexity(){return this.path.length}setDimensions(){this.setBoundingBox()}setBoundingBox(t){const{width:e,height:i,pathOffset:s}=this._calcDimensions();this.set({width:e,height:i,pathOffset:s}),t&&this.setPositionByOrigin(s,j,j)}_calcBoundsFromPath(){const t=[];let e=0,i=0,s=0,n=0;for(const o of this.path)switch(o[0]){case"L":s=o[1],n=o[2],t.push({x:e,y:i},{x:s,y:n});break;case"M":s=o[1],n=o[2],e=s,i=n;break;case"C":t.push(...Lr(s,n,o[1],o[2],o[3],o[4],o[5],o[6])),s=o[5],n=o[6];break;case"Q":t.push(...Lr(s,n,o[1],o[2],o[1],o[2],o[3],o[4])),s=o[3],n=o[4];break;case"Z":s=e,n=i}return se(t)}_calcDimensions(){const t=this._calcBoundsFromPath();return y(y({},t),{},{pathOffset:new C(t.left+t.width/2,t.top+t.height/2)})}static fromObject(t){return this._fromObject(t,{extraParam:"path"})}static async fromElement(t,e,i){const s=ge(t,this.ATTRIBUTE_NAMES,i),{d:n}=s;return new this(n,y(y(y({},U(s,_d)),e),{},{left:void 0,top:void 0}))}}m(pe,"type","Path"),m(pe,"cacheProperties",[...ue,"path","fillRule"]),m(pe,"ATTRIBUTE_NAMES",[...Oe,"d"]),D.setClass(pe),D.setSVGClass(pe);class qi extends wd{constructor(t){super(t),m(this,"decimate",.4),m(this,"drawStraightLine",!1),m(this,"straightLineKey","shiftKey"),this._points=[],this._hasStraightLine=!1}needsFullRender(){return super.needsFullRender()||this._hasStraightLine}static drawSegment(t,e,i){const s=e.midPointFrom(i);return t.quadraticCurveTo(e.x,e.y,s.x,s.y),s}onMouseDown(t,e){let{e:i}=e;this.canvas._isMainEvent(i)&&(this.drawStraightLine=!!this.straightLineKey&&i[this.straightLineKey],this._prepareForDrawing(t),this._addPoint(t),this._render())}onMouseMove(t,e){let{e:i}=e;if(this.canvas._isMainEvent(i)&&(this.drawStraightLine=!!this.straightLineKey&&i[this.straightLineKey],(this.limitedToCanvasSize!==!0||!this._isOutSideCanvas(t))&&this._addPoint(t)&&this._points.length>1))if(this.needsFullRender())this.canvas.clearContext(this.canvas.contextTop),this._render();else{const s=this._points,n=s.length,o=this.canvas.contextTop;this._saveAndTransform(o),this.oldEnd&&(o.beginPath(),o.moveTo(this.oldEnd.x,this.oldEnd.y)),this.oldEnd=qi.drawSegment(o,s[n-2],s[n-1]),o.stroke(),o.restore()}}onMouseUp(t){let{e}=t;return!this.canvas._isMainEvent(e)||(this.drawStraightLine=!1,this.oldEnd=void 0,this._finalizeAndAddPath(),!1)}_prepareForDrawing(t){this._reset(),this._addPoint(t),this.canvas.contextTop.moveTo(t.x,t.y)}_addPoint(t){return!(this._points.length>1&&t.eq(this._points[this._points.length-1]))&&(this.drawStraightLine&&this._points.length>1&&(this._hasStraightLine=!0,this._points.pop()),this._points.push(t),!0)}_reset(){this._points=[],this._setBrushStyles(this.canvas.contextTop),this._setShadow(),this._hasStraightLine=!1}_render(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.canvas.contextTop,e=this._points[0],i=this._points[1];if(this._saveAndTransform(t),t.beginPath(),this._points.length===2&&e.x===i.x&&e.y===i.y){const s=this.width/1e3;e.x-=s,i.x+=s}t.moveTo(e.x,e.y);for(let s=1;s<this._points.length;s++)qi.drawSegment(t,e,i),e=this._points[s],i=this._points[s+1];t.lineTo(e.x,e.y),t.stroke(),t.restore()}convertPointsToSVGPath(t){const e=this.width/1e3;return Ph(t,e)}createPath(t){const e=new pe(t,{fill:null,stroke:this.color,strokeWidth:this.width,strokeLineCap:this.strokeLineCap,strokeMiterLimit:this.strokeMiterLimit,strokeLineJoin:this.strokeLineJoin,strokeDashArray:this.strokeDashArray});return this.shadow&&(this.shadow.affectStroke=!0,e.shadow=new ne(this.shadow)),e}decimatePoints(t,e){if(t.length<=2)return t;let i,s=t[0];const n=this.canvas.getZoom(),o=Math.pow(e/n,2),a=t.length-1,h=[s];for(let l=1;l<a-1;l++)i=Math.pow(s.x-t[l].x,2)+Math.pow(s.y-t[l].y,2),i>=o&&(s=t[l],h.push(s));return h.push(t[a]),h}_finalizeAndAddPath(){this.canvas.contextTop.closePath(),this.decimate&&(this._points=this.decimatePoints(this._points,this.decimate));const t=this.convertPointsToSVGPath(this._points);if(function(i){return Ir(i)==="M 0 0 Q 0 0 0 0 L 0 0"}(t))return void this.canvas.requestRenderAll();const e=this.createPath(t);this.canvas.clearContext(this.canvas.contextTop),this.canvas.fire("before:path:created",{path:e}),this.canvas.add(e),this.canvas.requestRenderAll(),e.setCoords(),this._resetShadow(),this.canvas.fire("path:created",{path:e})}}const Sd=["left","top","radius"],Fh=["radius","startAngle","endAngle","counterClockwise"];class me extends ft{static getDefaults(){return y(y({},super.getDefaults()),me.ownDefaults)}constructor(t){super(),Object.assign(this,me.ownDefaults),this.setOptions(t)}_set(t,e){return super._set(t,e),t==="radius"&&this.setRadius(e),this}_render(t){t.beginPath(),t.arc(0,0,this.radius,Q(this.startAngle),Q(this.endAngle),this.counterClockwise),this._renderPaintInOrder(t)}getRadiusX(){return this.get("radius")*this.get(mt)}getRadiusY(){return this.get("radius")*this.get(Ot)}setRadius(t){this.radius=t,this.set({width:2*t,height:2*t})}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return super.toObject([...Fh,...t])}_toSVG(){const t=(this.endAngle-this.startAngle)%360;if(t===0)return["<circle ","COMMON_PARTS",'cx="0" cy="0" ','r="',"".concat(this.radius),`" />
`];{const{radius:e}=this,i=Q(this.startAngle),s=Q(this.endAngle),n=Vt(i)*e,o=Xt(i)*e,a=Vt(s)*e,h=Xt(s)*e,l=t>180?1:0,c=this.counterClockwise?0:1;return['<path d="M '.concat(n," ").concat(o," A ").concat(e," ").concat(e," 0 ").concat(l," ").concat(c," ").concat(a," ").concat(h,'" '),"COMMON_PARTS",` />
`]}}static async fromElement(t,e,i){const s=ge(t,this.ATTRIBUTE_NAMES,i),{left:n=0,top:o=0,radius:a=0}=s;return new this(y(y({},U(s,Sd)),{},{radius:a,left:n-a,top:o-a}))}static fromObject(t){return super._fromObject(t)}}m(me,"type","Circle"),m(me,"cacheProperties",[...ue,...Fh]),m(me,"ownDefaults",{radius:0,startAngle:0,endAngle:360,counterClockwise:!1}),m(me,"ATTRIBUTE_NAMES",["cx","cy","r",...Oe]),D.setClass(me),D.setSVGClass(me);const Td=["x1","y1","x2","y2"],Ed=["x1","y1","x2","y2"],Fr=["x1","x2","y1","y2"];class Xe extends ft{constructor(){let[t,e,i,s]=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[0,0,0,0],n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),Object.assign(this,Xe.ownDefaults),this.setOptions(n),this.x1=t,this.x2=i,this.y1=e,this.y2=s,this._setWidthHeight();const{left:o,top:a}=n;typeof o=="number"&&this.set(V,o),typeof a=="number"&&this.set(wt,a)}_setWidthHeight(){const{x1:t,y1:e,x2:i,y2:s}=this;this.width=Math.abs(i-t),this.height=Math.abs(s-e);const{left:n,top:o,width:a,height:h}=se([{x:t,y:e},{x:i,y:s}]),l=new C(n+a/2,o+h/2);this.setPositionByOrigin(l,j,j)}_set(t,e){return super._set(t,e),Fr.includes(t)&&this._setWidthHeight(),this}_render(t){t.beginPath();const e=this.calcLinePoints();t.moveTo(e.x1,e.y1),t.lineTo(e.x2,e.y2),t.lineWidth=this.strokeWidth;const i=t.strokeStyle;var s;Bt(this.stroke)?t.strokeStyle=this.stroke.toLive(t):t.strokeStyle=(s=this.stroke)!==null&&s!==void 0?s:t.fillStyle,this.stroke&&this._renderStroke(t),t.strokeStyle=i}_findCenterFromElement(){return new C((this.x1+this.x2)/2,(this.y1+this.y2)/2)}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return y(y({},super.toObject(t)),this.calcLinePoints())}_getNonTransformedDimensions(){const t=super._getNonTransformedDimensions();return this.strokeLineCap==="butt"&&(this.width===0&&(t.y-=this.strokeWidth),this.height===0&&(t.x-=this.strokeWidth)),t}calcLinePoints(){const{x1:t,x2:e,y1:i,y2:s,width:n,height:o}=this,a=t<=e?-1:1,h=i<=s?-1:1;return{x1:a*n/2,x2:a*-n/2,y1:h*o/2,y2:h*-o/2}}_toSVG(){const{x1:t,x2:e,y1:i,y2:s}=this.calcLinePoints();return["<line ","COMMON_PARTS",'x1="'.concat(t,'" y1="').concat(i,'" x2="').concat(e,'" y2="').concat(s,`" />
`)]}static async fromElement(t,e,i){const s=ge(t,this.ATTRIBUTE_NAMES,i),{x1:n=0,y1:o=0,x2:a=0,y2:h=0}=s;return new this([n,o,a,h],U(s,Td))}static fromObject(t){let{x1:e,y1:i,x2:s,y2:n}=t,o=U(t,Ed);return this._fromObject(y(y({},o),{},{points:[e,i,s,n]}),{extraParam:"points"})}}m(Xe,"type","Line"),m(Xe,"cacheProperties",[...ue,...Fr]),m(Xe,"ATTRIBUTE_NAMES",Oe.concat(Fr)),D.setClass(Xe),D.setSVGClass(Xe);class Ye extends ft{static getDefaults(){return y(y({},super.getDefaults()),Ye.ownDefaults)}constructor(t){super(),Object.assign(this,Ye.ownDefaults),this.setOptions(t)}_render(t){const e=this.width/2,i=this.height/2;t.beginPath(),t.moveTo(-e,i),t.lineTo(0,-i),t.lineTo(e,i),t.closePath(),this._renderPaintInOrder(t)}_toSVG(){const t=this.width/2,e=this.height/2;return["<polygon ","COMMON_PARTS",'points="',"".concat(-t," ").concat(e,",0 ").concat(-e,",").concat(t," ").concat(e),'" />']}}m(Ye,"type","Triangle"),m(Ye,"ownDefaults",{width:100,height:100}),D.setClass(Ye),D.setSVGClass(Ye);const $h=["rx","ry"];class ve extends ft{static getDefaults(){return y(y({},super.getDefaults()),ve.ownDefaults)}constructor(t){super(),Object.assign(this,ve.ownDefaults),this.setOptions(t)}_set(t,e){switch(super._set(t,e),t){case"rx":this.rx=e,this.set("width",2*e);break;case"ry":this.ry=e,this.set("height",2*e)}return this}getRx(){return this.get("rx")*this.get(mt)}getRy(){return this.get("ry")*this.get(Ot)}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return super.toObject([...$h,...t])}_toSVG(){return["<ellipse ","COMMON_PARTS",'cx="0" cy="0" rx="'.concat(this.rx,'" ry="').concat(this.ry,`" />
`)]}_render(t){t.beginPath(),t.save(),t.transform(1,0,0,this.ry/this.rx,0,0),t.arc(0,0,this.rx,0,ce,!1),t.restore(),this._renderPaintInOrder(t)}static async fromElement(t,e,i){const s=ge(t,this.ATTRIBUTE_NAMES,i);return s.left=(s.left||0)-s.rx,s.top=(s.top||0)-s.ry,new this(s)}}function kd(r){if(!r)return[];const t=r.replace(/,/g," ").trim().split(/\s+/),e=[];for(let i=0;i<t.length;i+=2)e.push({x:parseFloat(t[i]),y:parseFloat(t[i+1])});return e}m(ve,"type","Ellipse"),m(ve,"cacheProperties",[...ue,...$h]),m(ve,"ownDefaults",{rx:0,ry:0}),m(ve,"ATTRIBUTE_NAMES",[...Oe,"cx","cy","rx","ry"]),D.setClass(ve),D.setSVGClass(ve);const Od=["left","top"],zh={exactBoundingBox:!1};class Kt extends ft{static getDefaults(){return y(y({},super.getDefaults()),Kt.ownDefaults)}constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),m(this,"strokeDiff",void 0),Object.assign(this,Kt.ownDefaults),this.setOptions(e),this.points=t;const{left:i,top:s}=e;this.initialized=!0,this.setBoundingBox(!0),typeof i=="number"&&this.set(V,i),typeof s=="number"&&this.set(wt,s)}isOpen(){return!0}_projectStrokeOnPoints(t){return rh(this.points,t,this.isOpen())}_calcDimensions(t){t=y({scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,strokeLineCap:this.strokeLineCap,strokeLineJoin:this.strokeLineJoin,strokeMiterLimit:this.strokeMiterLimit,strokeUniform:this.strokeUniform,strokeWidth:this.strokeWidth},t||{});const e=this.exactBoundingBox?this._projectStrokeOnPoints(t).map(l=>l.projectedPoint):this.points;if(e.length===0)return{left:0,top:0,width:0,height:0,pathOffset:new C,strokeOffset:new C,strokeDiff:new C};const i=se(e),s=$i(y(y({},t),{},{scaleX:1,scaleY:1})),n=se(this.points.map(l=>gt(l,s,!0))),o=new C(this.scaleX,this.scaleY);let a=i.left+i.width/2,h=i.top+i.height/2;return this.exactBoundingBox&&(a-=h*Math.tan(Q(this.skewX)),h-=a*Math.tan(Q(this.skewY))),y(y({},i),{},{pathOffset:new C(a,h),strokeOffset:new C(n.left,n.top).subtract(new C(i.left,i.top)).multiply(o),strokeDiff:new C(i.width,i.height).subtract(new C(n.width,n.height)).multiply(o)})}_findCenterFromElement(){const t=se(this.points);return new C(t.left+t.width/2,t.top+t.height/2)}setDimensions(){this.setBoundingBox()}setBoundingBox(t){const{left:e,top:i,width:s,height:n,pathOffset:o,strokeOffset:a,strokeDiff:h}=this._calcDimensions();this.set({width:s,height:n,pathOffset:o,strokeOffset:a,strokeDiff:h}),t&&this.setPositionByOrigin(new C(e+s/2,i+n/2),j,j)}isStrokeAccountedForInDimensions(){return this.exactBoundingBox}_getNonTransformedDimensions(){return this.exactBoundingBox?new C(this.width,this.height):super._getNonTransformedDimensions()}_getTransformedDimensions(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(this.exactBoundingBox){let o;if(Object.keys(t).some(a=>this.strokeUniform||this.constructor.layoutProperties.includes(a))){var e,i;const{width:a,height:h}=this._calcDimensions(t);o=new C((e=t.width)!==null&&e!==void 0?e:a,(i=t.height)!==null&&i!==void 0?i:h)}else{var s,n;o=new C((s=t.width)!==null&&s!==void 0?s:this.width,(n=t.height)!==null&&n!==void 0?n:this.height)}return o.multiply(new C(t.scaleX||this.scaleX,t.scaleY||this.scaleY))}return super._getTransformedDimensions(t)}_set(t,e){const i=this.initialized&&this[t]!==e,s=super._set(t,e);return this.exactBoundingBox&&i&&((t===mt||t===Ot)&&this.strokeUniform&&this.constructor.layoutProperties.includes("strokeUniform")||this.constructor.layoutProperties.includes(t))&&this.setDimensions(),s}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return y(y({},super.toObject(t)),{},{points:this.points.map(e=>{let{x:i,y:s}=e;return{x:i,y:s}})})}_toSVG(){const t=[],e=this.pathOffset.x,i=this.pathOffset.y,s=W.NUM_FRACTION_DIGITS;for(let n=0,o=this.points.length;n<o;n++)t.push(G(this.points[n].x-e,s),",",G(this.points[n].y-i,s)," ");return["<".concat(this.constructor.type.toLowerCase()," "),"COMMON_PARTS",'points="'.concat(t.join(""),`" />
`)]}_render(t){const e=this.points.length,i=this.pathOffset.x,s=this.pathOffset.y;if(e&&!isNaN(this.points[e-1].y)){t.beginPath(),t.moveTo(this.points[0].x-i,this.points[0].y-s);for(let n=0;n<e;n++){const o=this.points[n];t.lineTo(o.x-i,o.y-s)}!this.isOpen()&&t.closePath(),this._renderPaintInOrder(t)}}complexity(){return this.points.length}static async fromElement(t,e,i){return new this(kd(t.getAttribute("points")),y(y({},U(ge(t,this.ATTRIBUTE_NAMES,i),Od)),e))}static fromObject(t){return this._fromObject(t,{extraParam:"points"})}}m(Kt,"ownDefaults",zh),m(Kt,"type","Polyline"),m(Kt,"layoutProperties",[ci,ui,"strokeLineCap","strokeLineJoin","strokeMiterLimit","strokeWidth","strokeUniform","points"]),m(Kt,"cacheProperties",[...ue,"points"]),m(Kt,"ATTRIBUTE_NAMES",[...Oe]),D.setClass(Kt),D.setSVGClass(Kt);class Vs extends Kt{isOpen(){return!1}}m(Vs,"ownDefaults",zh),m(Vs,"type","Polygon"),D.setClass(Vs),D.setSVGClass(Vs);const Hh=["fontSize","fontWeight","fontFamily","fontStyle"],Nh=["underline","overline","linethrough"],Wh=[...Hh,"lineHeight","text","charSpacing","textAlign","styles","path","pathStartOffset","pathSide","pathAlign"],Vh=[...Wh,...Nh,"textBackgroundColor","direction"],Md=[...Hh,...Nh,_t,"strokeWidth",at,"deltaY","textBackgroundColor"],Dd={_reNewline:er,_reSpacesAndTabs:/[ \t\r]/g,_reSpaceAndTab:/[ \t\r]/,_reWords:/\S+/g,fontSize:40,fontWeight:"normal",fontFamily:"Times New Roman",underline:!1,overline:!1,linethrough:!1,textAlign:V,fontStyle:"normal",lineHeight:1.16,superscript:{size:.6,baseline:-.35},subscript:{size:.6,baseline:.11},textBackgroundColor:"",stroke:null,shadow:null,path:void 0,pathStartOffset:0,pathSide:V,pathAlign:"baseline",_fontSizeFraction:.222,offsets:{underline:.1,linethrough:-.315,overline:-.88},_fontSizeMult:1.13,charSpacing:0,deltaY:0,direction:"ltr",CACHE_FONT_SIZE:400,MIN_TEXT_WIDTH:2},re="justify",Xs="justify-left",Ki="justify-right",Zi="justify-center";class Xh extends ft{isEmptyStyles(t){if(!this.styles||t!==void 0&&!this.styles[t])return!0;const e=t===void 0?this.styles:{line:this.styles[t]};for(const i in e)for(const s in e[i])for(const n in e[i][s])return!1;return!0}styleHas(t,e){if(!this.styles||e!==void 0&&!this.styles[e])return!1;const i=e===void 0?this.styles:{0:this.styles[e]};for(const s in i)for(const n in i[s])if(i[s][n][t]!==void 0)return!0;return!1}cleanStyle(t){if(!this.styles)return!1;const e=this.styles;let i,s,n=0,o=!0,a=0;for(const h in e){i=0;for(const l in e[h]){const c=e[h][l]||{};n++,c[t]!==void 0?(s?c[t]!==s&&(o=!1):s=c[t],c[t]===this[t]&&delete c[t]):o=!1,Object.keys(c).length!==0?i++:delete e[h][l]}i===0&&delete e[h]}for(let h=0;h<this._textLines.length;h++)a+=this._textLines[h].length;o&&n===a&&(this[t]=s,this.removeStyle(t))}removeStyle(t){if(!this.styles)return;const e=this.styles;let i,s,n;for(s in e){for(n in i=e[s],i)delete i[n][t],Object.keys(i[n]).length===0&&delete i[n];Object.keys(i).length===0&&delete e[s]}}_extendStyles(t,e){const{lineIndex:i,charIndex:s}=this.get2DCursorLocation(t);this._getLineStyle(i)||this._setLineStyle(i);const n=lr(y(y({},this._getStyleDeclaration(i,s)),e),o=>o!==void 0);this._setStyleDeclaration(i,s,n)}getSelectionStyles(t,e,i){const s=[];for(let n=t;n<(e||t);n++)s.push(this.getStyleAtPosition(n,i));return s}getStyleAtPosition(t,e){const{lineIndex:i,charIndex:s}=this.get2DCursorLocation(t);return e?this.getCompleteStyleDeclaration(i,s):this._getStyleDeclaration(i,s)}setSelectionStyles(t,e,i){for(let s=e;s<(i||e);s++)this._extendStyles(s,t);this._forceClearCache=!0}_getStyleDeclaration(t,e){var i;const s=this.styles&&this.styles[t];return s&&(i=s[e])!==null&&i!==void 0?i:{}}getCompleteStyleDeclaration(t,e){return y(y({},$e(this,this.constructor._styleProperties)),this._getStyleDeclaration(t,e))}_setStyleDeclaration(t,e,i){this.styles[t][e]=i}_deleteStyleDeclaration(t,e){delete this.styles[t][e]}_getLineStyle(t){return!!this.styles[t]}_setLineStyle(t){this.styles[t]={}}_deleteLineStyle(t){delete this.styles[t]}}m(Xh,"_styleProperties",Md);const Ad=/  +/g,Pd=/"/g;function $r(r,t,e,i,s){return"		".concat(function(n,o){let{left:a,top:h,width:l,height:c}=o,u=arguments.length>2&&arguments[2]!==void 0?arguments[2]:W.NUM_FRACTION_DIGITS;const d=Vi(at,n,!1),[g,f,p,v]=[a,h,l,c].map(b=>G(b,u));return"<rect ".concat(d,' x="').concat(g,'" y="').concat(f,'" width="').concat(p,'" height="').concat(v,'"></rect>')}(r,{left:t,top:e,width:i,height:s}),`
`)}const Ld=["textAnchor","textDecoration","dx","dy","top","left","fontSize","strokeWidth"];let zr;class yt extends Xh{static getDefaults(){return y(y({},super.getDefaults()),yt.ownDefaults)}constructor(t,e){super(),m(this,"__charBounds",[]),Object.assign(this,yt.ownDefaults),this.setOptions(e),this.styles||(this.styles={}),this.text=t,this.initialized=!0,this.path&&this.setPathInfo(),this.initDimensions(),this.setCoords()}setPathInfo(){const t=this.path;t&&(t.segmentsInfo=Rr(t.path))}_splitText(){const t=this._splitTextIntoLines(this.text);return this.textLines=t.lines,this._textLines=t.graphemeLines,this._unwrappedTextLines=t._unwrappedLines,this._text=t.graphemeText,t}initDimensions(){this._splitText(),this._clearCache(),this.dirty=!0,this.path?(this.width=this.path.width,this.height=this.path.height):(this.width=this.calcTextWidth()||this.cursorWidth||this.MIN_TEXT_WIDTH,this.height=this.calcTextHeight()),this.textAlign.includes(re)&&this.enlargeSpaces()}enlargeSpaces(){let t,e,i,s,n,o,a;for(let h=0,l=this._textLines.length;h<l;h++)if((this.textAlign===re||h!==l-1&&!this.isEndOfWrapping(h))&&(s=0,n=this._textLines[h],e=this.getLineWidth(h),e<this.width&&(a=this.textLines[h].match(this._reSpacesAndTabs)))){i=a.length,t=(this.width-e)/i;for(let c=0;c<=n.length;c++)o=this.__charBounds[h][c],this._reSpaceAndTab.test(n[c])?(o.width+=t,o.kernedWidth+=t,o.left+=s,s+=t):o.left+=s}}isEndOfWrapping(t){return t===this._textLines.length-1}missingNewlineOffset(t){return 1}get2DCursorLocation(t,e){const i=e?this._unwrappedTextLines:this._textLines;let s;for(s=0;s<i.length;s++){if(t<=i[s].length)return{lineIndex:s,charIndex:t};t-=i[s].length+this.missingNewlineOffset(s,e)}return{lineIndex:s-1,charIndex:i[s-1].length<t?i[s-1].length:t}}toString(){return"#<Text (".concat(this.complexity(),'): { "text": "').concat(this.text,'", "fontFamily": "').concat(this.fontFamily,'" }>')}_getCacheCanvasDimensions(){const t=super._getCacheCanvasDimensions(),e=this.fontSize;return t.width+=e*t.zoomX,t.height+=e*t.zoomY,t}_render(t){const e=this.path;e&&!e.isNotVisible()&&e._render(t),this._setTextStyles(t),this._renderTextLinesBackground(t),this._renderTextDecoration(t,"underline"),this._renderText(t),this._renderTextDecoration(t,"overline"),this._renderTextDecoration(t,"linethrough")}_renderText(t){this.paintFirst===_t?(this._renderTextStroke(t),this._renderTextFill(t)):(this._renderTextFill(t),this._renderTextStroke(t))}_setTextStyles(t,e,i){if(t.textBaseline="alphabetic",this.path)switch(this.pathAlign){case j:t.textBaseline="middle";break;case"ascender":t.textBaseline=wt;break;case"descender":t.textBaseline=tr}t.font=this._getFontDeclaration(e,i)}calcTextWidth(){let t=this.getLineWidth(0);for(let e=1,i=this._textLines.length;e<i;e++){const s=this.getLineWidth(e);s>t&&(t=s)}return t}_renderTextLine(t,e,i,s,n,o){this._renderChars(t,e,i,s,n,o)}_renderTextLinesBackground(t){if(!this.textBackgroundColor&&!this.styleHas("textBackgroundColor"))return;const e=t.fillStyle,i=this._getLeftOffset();let s=this._getTopOffset();for(let n=0,o=this._textLines.length;n<o;n++){const a=this.getHeightOfLine(n);if(!this.textBackgroundColor&&!this.styleHas("textBackgroundColor",n)){s+=a;continue}const h=this._textLines[n].length,l=this._getLineLeftOffset(n);let c,u,d=0,g=0,f=this.getValueOfPropertyAt(n,0,"textBackgroundColor");for(let p=0;p<h;p++){const v=this.__charBounds[n][p];u=this.getValueOfPropertyAt(n,p,"textBackgroundColor"),this.path?(t.save(),t.translate(v.renderLeft,v.renderTop),t.rotate(v.angle),t.fillStyle=u,u&&t.fillRect(-v.width/2,-a/this.lineHeight*(1-this._fontSizeFraction),v.width,a/this.lineHeight),t.restore()):u!==f?(c=i+l+g,this.direction==="rtl"&&(c=this.width-c-d),t.fillStyle=f,f&&t.fillRect(c,s,d,a/this.lineHeight),g=v.left,d=v.width,f=u):d+=v.kernedWidth}u&&!this.path&&(c=i+l+g,this.direction==="rtl"&&(c=this.width-c-d),t.fillStyle=u,t.fillRect(c,s,d,a/this.lineHeight)),s+=a}t.fillStyle=e,this._removeShadow(t)}_measureChar(t,e,i,s){const n=ji.getFontCache(e),o=this._getFontDeclaration(e),a=i+t,h=i&&o===this._getFontDeclaration(s),l=e.fontSize/this.CACHE_FONT_SIZE;let c,u,d,g;if(i&&n[i]!==void 0&&(d=n[i]),n[t]!==void 0&&(g=c=n[t]),h&&n[a]!==void 0&&(u=n[a],g=u-d),c===void 0||d===void 0||u===void 0){const f=function(){return zr||(zr=Mt({width:0,height:0}).getContext("2d")),zr}();this._setTextStyles(f,e,!0),c===void 0&&(g=c=f.measureText(t).width,n[t]=c),d===void 0&&h&&i&&(d=f.measureText(i).width,n[i]=d),h&&u===void 0&&(u=f.measureText(a).width,n[a]=u,g=u-d)}return{width:c*l,kernedWidth:g*l}}getHeightOfChar(t,e){return this.getValueOfPropertyAt(t,e,"fontSize")}measureLine(t){const e=this._measureLine(t);return this.charSpacing!==0&&(e.width-=this._getWidthOfCharSpacing()),e.width<0&&(e.width=0),e}_measureLine(t){let e,i,s=0;const n=this.pathSide===it,o=this.path,a=this._textLines[t],h=a.length,l=new Array(h);this.__charBounds[t]=l;for(let c=0;c<h;c++){const u=a[c];i=this._getGraphemeBox(u,t,c,e),l[c]=i,s+=i.kernedWidth,e=u}if(l[h]={left:i?i.left+i.width:0,width:0,kernedWidth:0,height:this.fontSize,deltaY:0},o&&o.segmentsInfo){let c=0;const u=o.segmentsInfo[o.segmentsInfo.length-1].length;switch(this.textAlign){case V:c=n?u-s:0;break;case j:c=(u-s)/2;break;case it:c=n?0:u-s}c+=this.pathStartOffset*(n?-1:1);for(let d=n?h-1:0;n?d>=0:d<h;n?d--:d++)i=l[d],c>u?c%=u:c<0&&(c+=u),this._setGraphemeOnPath(c,i),c+=i.kernedWidth}return{width:s,numOfSpaces:0}}_setGraphemeOnPath(t,e){const i=t+e.kernedWidth/2,s=this.path,n=Mh(s.path,i,s.segmentsInfo);e.renderLeft=n.x-s.pathOffset.x,e.renderTop=n.y-s.pathOffset.y,e.angle=n.angle+(this.pathSide===it?Math.PI:0)}_getGraphemeBox(t,e,i,s,n){const o=this.getCompleteStyleDeclaration(e,i),a=s?this.getCompleteStyleDeclaration(e,i-1):{},h=this._measureChar(t,o,s,a);let l,c=h.kernedWidth,u=h.width;this.charSpacing!==0&&(l=this._getWidthOfCharSpacing(),u+=l,c+=l);const d={width:u,left:0,height:o.fontSize,kernedWidth:c,deltaY:o.deltaY};if(i>0&&!n){const g=this.__charBounds[e][i-1];d.left=g.left+g.width+h.kernedWidth-h.width}return d}getHeightOfLine(t){if(this.__lineHeights[t])return this.__lineHeights[t];let e=this.getHeightOfChar(t,0);for(let i=1,s=this._textLines[t].length;i<s;i++)e=Math.max(this.getHeightOfChar(t,i),e);return this.__lineHeights[t]=e*this.lineHeight*this._fontSizeMult}calcTextHeight(){let t,e=0;for(let i=0,s=this._textLines.length;i<s;i++)t=this.getHeightOfLine(i),e+=i===s-1?t/this.lineHeight:t;return e}_getLeftOffset(){return this.direction==="ltr"?-this.width/2:this.width/2}_getTopOffset(){return-this.height/2}_renderTextCommon(t,e){t.save();let i=0;const s=this._getLeftOffset(),n=this._getTopOffset();for(let o=0,a=this._textLines.length;o<a;o++){const h=this.getHeightOfLine(o),l=h/this.lineHeight,c=this._getLineLeftOffset(o);this._renderTextLine(e,t,this._textLines[o],s+c,n+i+l,o),i+=h}t.restore()}_renderTextFill(t){(this.fill||this.styleHas(at))&&this._renderTextCommon(t,"fillText")}_renderTextStroke(t){(this.stroke&&this.strokeWidth!==0||!this.isEmptyStyles())&&(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(t),t.save(),this._setLineDash(t,this.strokeDashArray),t.beginPath(),this._renderTextCommon(t,"strokeText"),t.closePath(),t.restore())}_renderChars(t,e,i,s,n,o){const a=this.getHeightOfLine(o),h=this.textAlign.includes(re),l=this.path,c=!h&&this.charSpacing===0&&this.isEmptyStyles(o)&&!l,u=this.direction==="ltr",d=this.direction==="ltr"?1:-1,g=e.direction;let f,p,v,b,w,x="",_=0;if(e.save(),g!==this.direction&&(e.canvas.setAttribute("dir",u?"ltr":"rtl"),e.direction=u?"ltr":"rtl",e.textAlign=u?V:it),n-=a*this._fontSizeFraction/this.lineHeight,c)return this._renderChar(t,e,o,0,i.join(""),s,n),void e.restore();for(let k=0,A=i.length-1;k<=A;k++)b=k===A||this.charSpacing||l,x+=i[k],v=this.__charBounds[o][k],_===0?(s+=d*(v.kernedWidth-v.width),_+=v.width):_+=v.kernedWidth,h&&!b&&this._reSpaceAndTab.test(i[k])&&(b=!0),b||(f=f||this.getCompleteStyleDeclaration(o,k),p=this.getCompleteStyleDeclaration(o,k+1),b=js(f,p,!1)),b&&(l?(e.save(),e.translate(v.renderLeft,v.renderTop),e.rotate(v.angle),this._renderChar(t,e,o,k,x,-_/2,0),e.restore()):(w=s,this._renderChar(t,e,o,k,x,w,n)),x="",f=p,s+=d*_,_=0);e.restore()}_applyPatternGradientTransformText(t){const e=this.width+this.strokeWidth,i=this.height+this.strokeWidth,s=Mt({width:e,height:i}),n=s.getContext("2d");return s.width=e,s.height=i,n.beginPath(),n.moveTo(0,0),n.lineTo(e,0),n.lineTo(e,i),n.lineTo(0,i),n.closePath(),n.translate(e/2,i/2),n.fillStyle=t.toLive(n),this._applyPatternGradientTransform(n,t),n.fill(),n.createPattern(s,"no-repeat")}handleFiller(t,e,i){let s,n;return Bt(i)?i.gradientUnits==="percentage"||i.gradientTransform||i.patternTransform?(s=-this.width/2,n=-this.height/2,t.translate(s,n),t[e]=this._applyPatternGradientTransformText(i),{offsetX:s,offsetY:n}):(t[e]=i.toLive(t),this._applyPatternGradientTransform(t,i)):(t[e]=i,{offsetX:0,offsetY:0})}_setStrokeStyles(t,e){let{stroke:i,strokeWidth:s}=e;return t.lineWidth=s,t.lineCap=this.strokeLineCap,t.lineDashOffset=this.strokeDashOffset,t.lineJoin=this.strokeLineJoin,t.miterLimit=this.strokeMiterLimit,this.handleFiller(t,"strokeStyle",i)}_setFillStyles(t,e){let{fill:i}=e;return this.handleFiller(t,"fillStyle",i)}_renderChar(t,e,i,s,n,o,a){const h=this._getStyleDeclaration(i,s),l=this.getCompleteStyleDeclaration(i,s),c=t==="fillText"&&l.fill,u=t==="strokeText"&&l.stroke&&l.strokeWidth;if(u||c){if(e.save(),e.font=this._getFontDeclaration(l),h.textBackgroundColor&&this._removeShadow(e),h.deltaY&&(a+=h.deltaY),c){const d=this._setFillStyles(e,l);e.fillText(n,o-d.offsetX,a-d.offsetY)}if(u){const d=this._setStrokeStyles(e,l);e.strokeText(n,o-d.offsetX,a-d.offsetY)}e.restore()}}setSuperscript(t,e){this._setScript(t,e,this.superscript)}setSubscript(t,e){this._setScript(t,e,this.subscript)}_setScript(t,e,i){const s=this.get2DCursorLocation(t,!0),n=this.getValueOfPropertyAt(s.lineIndex,s.charIndex,"fontSize"),o=this.getValueOfPropertyAt(s.lineIndex,s.charIndex,"deltaY"),a={fontSize:n*i.size,deltaY:o+n*i.baseline};this.setSelectionStyles(a,t,e)}_getLineLeftOffset(t){const e=this.getLineWidth(t),i=this.width-e,s=this.textAlign,n=this.direction,o=this.isEndOfWrapping(t);let a=0;return s===re||s===Zi&&!o||s===Ki&&!o||s===Xs&&!o?0:(s===j&&(a=i/2),s===it&&(a=i),s===Zi&&(a=i/2),s===Ki&&(a=i),n==="rtl"&&(s===it||s===re||s===Ki?a=0:s===V||s===Xs?a=-i:s!==j&&s!==Zi||(a=-i/2)),a)}_clearCache(){this._forceClearCache=!1,this.__lineWidths=[],this.__lineHeights=[],this.__charBounds=[]}getLineWidth(t){if(this.__lineWidths[t]!==void 0)return this.__lineWidths[t];const{width:e}=this.measureLine(t);return this.__lineWidths[t]=e,e}_getWidthOfCharSpacing(){return this.charSpacing!==0?this.fontSize*this.charSpacing/1e3:0}getValueOfPropertyAt(t,e,i){var s;return(s=this._getStyleDeclaration(t,e)[i])!==null&&s!==void 0?s:this[i]}_renderTextDecoration(t,e){if(!this[e]&&!this.styleHas(e))return;let i=this._getTopOffset();const s=this._getLeftOffset(),n=this.path,o=this._getWidthOfCharSpacing(),a=this.offsets[e];for(let h=0,l=this._textLines.length;h<l;h++){const c=this.getHeightOfLine(h);if(!this[e]&&!this.styleHas(e,h)){i+=c;continue}const u=this._textLines[h],d=c/this.lineHeight,g=this._getLineLeftOffset(h);let f,p,v=0,b=0,w=this.getValueOfPropertyAt(h,0,e),x=this.getValueOfPropertyAt(h,0,at);const _=i+d*(1-this._fontSizeFraction);let k=this.getHeightOfChar(h,0),A=this.getValueOfPropertyAt(h,0,"deltaY");for(let O=0,I=u.length;O<I;O++){const F=this.__charBounds[h][O];f=this.getValueOfPropertyAt(h,O,e),p=this.getValueOfPropertyAt(h,O,at);const tt=this.getHeightOfChar(h,O),Z=this.getValueOfPropertyAt(h,O,"deltaY");if(n&&f&&p)t.save(),t.fillStyle=x,t.translate(F.renderLeft,F.renderTop),t.rotate(F.angle),t.fillRect(-F.kernedWidth/2,a*tt+Z,F.kernedWidth,this.fontSize/15),t.restore();else if((f!==w||p!==x||tt!==k||Z!==A)&&b>0){let z=s+g+v;this.direction==="rtl"&&(z=this.width-z-b),w&&x&&(t.fillStyle=x,t.fillRect(z,_+a*k+A,b,this.fontSize/15)),v=F.left,b=F.width,w=f,x=p,k=tt,A=Z}else b+=F.kernedWidth}let L=s+g+v;this.direction==="rtl"&&(L=this.width-L-b),t.fillStyle=p,f&&p&&t.fillRect(L,_+a*k+A,b-o,this.fontSize/15),i+=c}this._removeShadow(t)}_getFontDeclaration(){let{fontFamily:t=this.fontFamily,fontStyle:e=this.fontStyle,fontWeight:i=this.fontWeight,fontSize:s=this.fontSize}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;const o=t.includes("'")||t.includes('"')||t.includes(",")||yt.genericFonts.includes(t.toLowerCase())?t:'"'.concat(t,'"');return[e,i,"".concat(n?this.CACHE_FONT_SIZE:s,"px"),o].join(" ")}render(t){this.visible&&(this.canvas&&this.canvas.skipOffscreen&&!this.group&&!this.isOnScreen()||(this._forceClearCache&&this.initDimensions(),super.render(t)))}graphemeSplit(t){return Is(t)}_splitTextIntoLines(t){const e=t.split(this._reNewline),i=new Array(e.length),s=[`
`];let n=[];for(let o=0;o<e.length;o++)i[o]=this.graphemeSplit(e[o]),n=n.concat(i[o],s);return n.pop(),{_unwrappedLines:i,lines:e,graphemeText:n,graphemeLines:i}}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return y(y({},super.toObject([...Vh,...t])),{},{styles:ah(this.styles,this.text)},this.path?{path:this.path.toObject()}:{})}set(t,e){const{textLayoutProperties:i}=this.constructor;super.set(t,e);let s=!1,n=!1;if(typeof t=="object")for(const o in t)o==="path"&&this.setPathInfo(),s=s||i.includes(o),n=n||o==="path";else s=i.includes(t),n=t==="path";return n&&this.setPathInfo(),s&&this.initialized&&(this.initDimensions(),this.setCoords()),this}complexity(){return 1}static async fromElement(t,e,i){const s=ge(t,yt.ATTRIBUTE_NAMES,i),n=y(y({},e),s),{textAnchor:o=V,textDecoration:a="",dx:h=0,dy:l=0,top:c=0,left:u=0,fontSize:d=Qn,strokeWidth:g=1}=n,f=U(n,Ld),p=new this((t.textContent||"").replace(/^\s+|\s+$|\n+/g,"").replace(/\s+/g," "),y({left:u+h,top:c+l,underline:a.includes("underline"),overline:a.includes("overline"),linethrough:a.includes("line-through"),strokeWidth:0,fontSize:d},f)),v=p.getScaledHeight()/p.height,b=((p.height+p.strokeWidth)*p.lineHeight-p.height)*v,w=p.getScaledHeight()+b;let x=0;return o===j&&(x=p.getScaledWidth()/2),o===it&&(x=p.getScaledWidth()),p.set({left:p.left-x,top:p.top-(w-p.fontSize*(.07+p._fontSizeFraction))/p.lineHeight,strokeWidth:g}),p}static fromObject(t){return this._fromObject(y(y({},t),{},{styles:hh(t.styles||{},t.text)}),{extraParam:"text"})}}m(yt,"textLayoutProperties",Wh),m(yt,"cacheProperties",[...ue,...Vh]),m(yt,"ownDefaults",Dd),m(yt,"type","Text"),m(yt,"genericFonts",["serif","sans-serif","monospace","cursive","fantasy","system-ui","ui-serif","ui-sans-serif","ui-monospace","ui-rounded","math","emoji","fangsong"]),m(yt,"ATTRIBUTE_NAMES",Oe.concat("x","y","dx","dy","font-family","font-style","font-weight","font-size","letter-spacing","text-decoration","text-anchor")),eh(yt,[class extends ja{_toSVG(){const r=this._getSVGLeftTopOffsets(),t=this._getSVGTextAndBg(r.textTop,r.textLeft);return this._wrapSVGTextAndBg(t)}toSVG(r){const t=this._createBaseSVGMarkup(this._toSVG(),{reviver:r,noStyle:!0,withShadow:!0}),e=this.path;return e?t+e._createBaseSVGMarkup(e._toSVG(),{reviver:r,withShadow:!0,additionalTransform:mi(this.calcOwnMatrix())}):t}_getSVGLeftTopOffsets(){return{textLeft:-this.width/2,textTop:-this.height/2,lineTop:this.getHeightOfLine(0)}}_wrapSVGTextAndBg(r){let{textBgRects:t,textSpans:e}=r;const i=this.getSvgTextDecoration(this);return[t.join(""),'		<text xml:space="preserve" ',this.fontFamily?'font-family="'.concat(this.fontFamily.replace(Pd,"'"),'" '):"",this.fontSize?'font-size="'.concat(this.fontSize,'" '):"",this.fontStyle?'font-style="'.concat(this.fontStyle,'" '):"",this.fontWeight?'font-weight="'.concat(this.fontWeight,'" '):"",i?'text-decoration="'.concat(i,'" '):"",this.direction==="rtl"?'direction="'.concat(this.direction,'" '):"",'style="',this.getSvgStyles(!0),'"',this.addPaintOrder()," >",e.join(""),`</text>
`]}_getSVGTextAndBg(r,t){const e=[],i=[];let s,n=r;this.backgroundColor&&i.push(...$r(this.backgroundColor,-this.width/2,-this.height/2,this.width,this.height));for(let o=0,a=this._textLines.length;o<a;o++)s=this._getLineLeftOffset(o),this.direction==="rtl"&&(s+=this.width),(this.textBackgroundColor||this.styleHas("textBackgroundColor",o))&&this._setSVGTextLineBg(i,o,t+s,n),this._setSVGTextLineText(e,o,t+s,n),n+=this.getHeightOfLine(o);return{textSpans:e,textBgRects:i}}_createTextCharSpan(r,t,e,i,s){const n=W.NUM_FRACTION_DIGITS,o=this.getSvgSpanStyles(t,r!==r.trim()||!!r.match(Ad)),a=o?'style="'.concat(o,'"'):"",h=t.deltaY,l=h?' dy="'.concat(G(h,n),'" '):"",{angle:c,renderLeft:u,renderTop:d,width:g}=s;let f="";if(u!==void 0){const p=g/2;c&&(f=' rotate="'.concat(G(ie(c),n),'"'));const v=Fe({angle:ie(c)});v[4]=u,v[5]=d;const b=new C(-p,0).transform(v);e=b.x,i=b.y}return'<tspan x="'.concat(G(e,n),'" y="').concat(G(i,n),'" ').concat(l).concat(f).concat(a,">").concat(oh(r),"</tspan>")}_setSVGTextLineText(r,t,e,i){const s=this.getHeightOfLine(t),n=this.textAlign.includes(re),o=this._textLines[t];let a,h,l,c,u,d="",g=0;i+=s*(1-this._fontSizeFraction)/this.lineHeight;for(let f=0,p=o.length-1;f<=p;f++)u=f===p||this.charSpacing||this.path,d+=o[f],l=this.__charBounds[t][f],g===0?(e+=l.kernedWidth-l.width,g+=l.width):g+=l.kernedWidth,n&&!u&&this._reSpaceAndTab.test(o[f])&&(u=!0),u||(a=a||this.getCompleteStyleDeclaration(t,f),h=this.getCompleteStyleDeclaration(t,f+1),u=js(a,h,!0)),u&&(c=this._getStyleDeclaration(t,f),r.push(this._createTextCharSpan(d,c,e,i,l)),d="",a=h,this.direction==="rtl"?e-=g:e+=g,g=0)}_setSVGTextLineBg(r,t,e,i){const s=this._textLines[t],n=this.getHeightOfLine(t)/this.lineHeight;let o,a=0,h=0,l=this.getValueOfPropertyAt(t,0,"textBackgroundColor");for(let c=0;c<s.length;c++){const{left:u,width:d,kernedWidth:g}=this.__charBounds[t][c];o=this.getValueOfPropertyAt(t,c,"textBackgroundColor"),o!==l?(l&&r.push(...$r(l,e+h,i,a,n)),h=u,a=d,l=o):a+=g}o&&r.push(...$r(l,e+h,i,a,n))}_getSVGLineTopOffset(r){let t,e=0;for(t=0;t<r;t++)e+=this.getHeightOfLine(t);const i=this.getHeightOfLine(t);return{lineTop:e,offset:(this._fontSizeMult-this._fontSizeFraction)*i/(this.lineHeight*this._fontSizeMult)}}getSvgStyles(r){return"".concat(super.getSvgStyles(r)," white-space: pre;")}getSvgSpanStyles(r,t){const{fontFamily:e,strokeWidth:i,stroke:s,fill:n,fontSize:o,fontStyle:a,fontWeight:h,deltaY:l}=r,c=this.getSvgTextDecoration(r);return[s?Vi(_t,s):"",i?"stroke-width: ".concat(i,"; "):"",e?"font-family: ".concat(e.includes("'")||e.includes('"')?e:"'".concat(e,"'"),"; "):"",o?"font-size: ".concat(o,"px; "):"",a?"font-style: ".concat(a,"; "):"",h?"font-weight: ".concat(h,"; "):"",c&&"text-decoration: ".concat(c,"; "),n?Vi(at,n):"",l?"baseline-shift: ".concat(-l,"; "):"",t?"white-space: pre; ":""].join("")}getSvgTextDecoration(r){return["overline","underline","line-through"].filter(t=>r[t.replace("-","")]).join(" ")}}]),D.setClass(yt),D.setSVGClass(yt);class Rd{constructor(t){m(this,"target",void 0),m(this,"__mouseDownInPlace",!1),m(this,"__dragStartFired",!1),m(this,"__isDraggingOver",!1),m(this,"__dragStartSelection",void 0),m(this,"__dragImageDisposer",void 0),m(this,"_dispose",void 0),this.target=t;const e=[this.target.on("dragenter",this.dragEnterHandler.bind(this)),this.target.on("dragover",this.dragOverHandler.bind(this)),this.target.on("dragleave",this.dragLeaveHandler.bind(this)),this.target.on("dragend",this.dragEndHandler.bind(this)),this.target.on("drop",this.dropHandler.bind(this))];this._dispose=()=>{e.forEach(i=>i()),this._dispose=void 0}}isPointerOverSelection(t){const e=this.target,i=e.getSelectionStartFromPointer(t);return e.isEditing&&i>=e.selectionStart&&i<=e.selectionEnd&&e.selectionStart<e.selectionEnd}start(t){return this.__mouseDownInPlace=this.isPointerOverSelection(t)}isActive(){return this.__mouseDownInPlace}end(t){const e=this.isActive();return e&&!this.__dragStartFired&&(this.target.setCursorByClick(t),this.target.initDelayedCursor(!0)),this.__mouseDownInPlace=!1,this.__dragStartFired=!1,this.__isDraggingOver=!1,e}getDragStartSelection(){return this.__dragStartSelection}setDragImage(t,e){var i;let{selectionStart:s,selectionEnd:n}=e;const o=this.target,a=o.canvas,h=new C(o.flipX?-1:1,o.flipY?-1:1),l=o._getCursorBoundaries(s),c=new C(l.left+l.leftOffset,l.top+l.topOffset).multiply(h).transform(o.calcTransformMatrix()),u=a.getScenePoint(t).subtract(c),d=o.getCanvasRetinaScaling(),g=o.getBoundingRect(),f=c.subtract(new C(g.left,g.top)),p=a.viewportTransform,v=f.add(u).transform(p,!0),b=o.backgroundColor,w=kr(o.styles);o.backgroundColor="";const x={stroke:"transparent",fill:"transparent",textBackgroundColor:"transparent"};o.setSelectionStyles(x,0,s),o.setSelectionStyles(x,n,o.text.length),o.dirty=!0;const _=o.toCanvasElement({enableRetinaScaling:a.enableRetinaScaling,viewportTransform:!0});o.backgroundColor=b,o.styles=w,o.dirty=!0,Hs(_,{position:"fixed",left:"".concat(-_.width,"px"),border:xt,width:"".concat(_.width/d,"px"),height:"".concat(_.height/d,"px")}),this.__dragImageDisposer&&this.__dragImageDisposer(),this.__dragImageDisposer=()=>{_.remove()},Yt(t.target||this.target.hiddenTextarea).body.appendChild(_),(i=t.dataTransfer)===null||i===void 0||i.setDragImage(_,v.x,v.y)}onDragStart(t){this.__dragStartFired=!0;const e=this.target,i=this.isActive();if(i&&t.dataTransfer){const s=this.__dragStartSelection={selectionStart:e.selectionStart,selectionEnd:e.selectionEnd},n=e._text.slice(s.selectionStart,s.selectionEnd).join(""),o=y({text:e.text,value:n},s);t.dataTransfer.setData("text/plain",n),t.dataTransfer.setData("application/fabric",JSON.stringify({value:n,styles:e.getSelectionStyles(s.selectionStart,s.selectionEnd,!0)})),t.dataTransfer.effectAllowed="copyMove",this.setDragImage(t,o)}return e.abortCursorAnimation(),i}canDrop(t){if(this.target.editable&&!this.target.getActiveControl()&&!t.defaultPrevented){if(this.isActive()&&this.__dragStartSelection){const e=this.target.getSelectionStartFromPointer(t),i=this.__dragStartSelection;return e<i.selectionStart||e>i.selectionEnd}return!0}return!1}targetCanDrop(t){return this.target.canDrop(t)}dragEnterHandler(t){let{e}=t;const i=this.targetCanDrop(e);!this.__isDraggingOver&&i&&(this.__isDraggingOver=!0)}dragOverHandler(t){const{e}=t,i=this.targetCanDrop(e);!this.__isDraggingOver&&i?this.__isDraggingOver=!0:this.__isDraggingOver&&!i&&(this.__isDraggingOver=!1),this.__isDraggingOver&&(e.preventDefault(),t.canDrop=!0,t.dropTarget=this.target)}dragLeaveHandler(){(this.__isDraggingOver||this.isActive())&&(this.__isDraggingOver=!1)}dropHandler(t){var e;const{e:i}=t,s=i.defaultPrevented;this.__isDraggingOver=!1,i.preventDefault();let n=(e=i.dataTransfer)===null||e===void 0?void 0:e.getData("text/plain");if(n&&!s){const o=this.target,a=o.canvas;let h=o.getSelectionStartFromPointer(i);const{styles:l}=i.dataTransfer.types.includes("application/fabric")?JSON.parse(i.dataTransfer.getData("application/fabric")):{},c=n[Math.max(0,n.length-1)],u=0;if(this.__dragStartSelection){const d=this.__dragStartSelection.selectionStart,g=this.__dragStartSelection.selectionEnd;h>d&&h<=g?h=d:h>g&&(h-=g-d),o.removeChars(d,g),delete this.__dragStartSelection}o._reNewline.test(c)&&(o._reNewline.test(o._text[h])||h===o._text.length)&&(n=n.trimEnd()),t.didDrop=!0,t.dropTarget=o,o.insertChars(n,l,h),a.setActiveObject(o),o.enterEditing(i),o.selectionStart=Math.min(h+u,o._text.length),o.selectionEnd=Math.min(o.selectionStart+n.length,o._text.length),o.hiddenTextarea.value=o.text,o._updateTextarea(),o.hiddenTextarea.focus(),o.fire(ms,{index:h+u,action:"drop"}),a.fire("text:changed",{target:o}),a.contextTopDirty=!0,a.requestRenderAll()}}dragEndHandler(t){let{e}=t;if(this.isActive()&&this.__dragStartFired&&this.__dragStartSelection){var i;const s=this.target,n=this.target.canvas,{selectionStart:o,selectionEnd:a}=this.__dragStartSelection,h=((i=e.dataTransfer)===null||i===void 0?void 0:i.dropEffect)||xt;h===xt?(s.selectionStart=o,s.selectionEnd=a,s._updateTextarea(),s.hiddenTextarea.focus()):(s.clearContextTop(),h==="move"&&(s.removeChars(o,a),s.selectionStart=s.selectionEnd=o,s.hiddenTextarea&&(s.hiddenTextarea.value=s.text),s._updateTextarea(),s.fire(ms,{index:o,action:"dragend"}),n.fire("text:changed",{target:s}),n.requestRenderAll()),s.exitEditing())}this.__dragImageDisposer&&this.__dragImageDisposer(),delete this.__dragImageDisposer,delete this.__dragStartSelection,this.__isDraggingOver=!1}dispose(){this._dispose&&this._dispose()}}const Yh=/[ \n\.,;!\?\-]/;class Id extends yt{constructor(){super(...arguments),m(this,"_currentCursorOpacity",1)}initBehavior(){this._tick=this._tick.bind(this),this._onTickComplete=this._onTickComplete.bind(this),this.updateSelectionOnMouseMove=this.updateSelectionOnMouseMove.bind(this)}onDeselect(t){return this.isEditing&&this.exitEditing(),this.selected=!1,super.onDeselect(t)}_animateCursor(t){let{toValue:e,duration:i,delay:s,onComplete:n}=t;return Sr({startValue:this._currentCursorOpacity,endValue:e,duration:i,delay:s,onComplete:n,abort:()=>!this.canvas||this.selectionStart!==this.selectionEnd,onChange:o=>{this._currentCursorOpacity=o,this.renderCursorOrSelection()}})}_tick(t){this._currentTickState=this._animateCursor({toValue:0,duration:this.cursorDuration/2,delay:Math.max(t||0,100),onComplete:this._onTickComplete})}_onTickComplete(){var t;(t=this._currentTickCompleteState)===null||t===void 0||t.abort(),this._currentTickCompleteState=this._animateCursor({toValue:1,duration:this.cursorDuration,onComplete:this._tick})}initDelayedCursor(t){this.abortCursorAnimation(),this._tick(t?0:this.cursorDelay)}abortCursorAnimation(){let t=!1;[this._currentTickState,this._currentTickCompleteState].forEach(e=>{e&&!e.isDone()&&(t=!0,e.abort())}),this._currentCursorOpacity=1,t&&this.clearContextTop()}restartCursorIfNeeded(){[this._currentTickState,this._currentTickCompleteState].some(t=>!t||t.isDone())&&this.initDelayedCursor()}selectAll(){return this.selectionStart=0,this.selectionEnd=this._text.length,this._fireSelectionChanged(),this._updateTextarea(),this}cmdAll(){this.selectAll(),this.renderCursorOrSelection()}getSelectedText(){return this._text.slice(this.selectionStart,this.selectionEnd).join("")}findWordBoundaryLeft(t){let e=0,i=t-1;if(this._reSpace.test(this._text[i]))for(;this._reSpace.test(this._text[i]);)e++,i--;for(;/\S/.test(this._text[i])&&i>-1;)e++,i--;return t-e}findWordBoundaryRight(t){let e=0,i=t;if(this._reSpace.test(this._text[i]))for(;this._reSpace.test(this._text[i]);)e++,i++;for(;/\S/.test(this._text[i])&&i<this._text.length;)e++,i++;return t+e}findLineBoundaryLeft(t){let e=0,i=t-1;for(;!/\n/.test(this._text[i])&&i>-1;)e++,i--;return t-e}findLineBoundaryRight(t){let e=0,i=t;for(;!/\n/.test(this._text[i])&&i<this._text.length;)e++,i++;return t+e}searchWordBoundary(t,e){const i=this._text;let s=t>0&&this._reSpace.test(i[t])&&(e===-1||!er.test(i[t-1]))?t-1:t,n=i[s];for(;s>0&&s<i.length&&!Yh.test(n);)s+=e,n=i[s];return e===-1&&Yh.test(n)&&s++,s}selectWord(t){var e;t=(e=t)!==null&&e!==void 0?e:this.selectionStart;const i=this.searchWordBoundary(t,-1),s=Math.max(i,this.searchWordBoundary(t,1));this.selectionStart=i,this.selectionEnd=s,this._fireSelectionChanged(),this._updateTextarea(),this.renderCursorOrSelection()}selectLine(t){var e;t=(e=t)!==null&&e!==void 0?e:this.selectionStart;const i=this.findLineBoundaryLeft(t),s=this.findLineBoundaryRight(t);this.selectionStart=i,this.selectionEnd=s,this._fireSelectionChanged(),this._updateTextarea()}enterEditing(t){!this.isEditing&&this.editable&&(this.enterEditingImpl(),this.fire("editing:entered",t?{e:t}:void 0),this._fireSelectionChanged(),this.canvas&&(this.canvas.fire("text:editing:entered",{target:this,e:t}),this.canvas.requestRenderAll()))}enterEditingImpl(){this.canvas&&(this.canvas.calcOffset(),this.canvas.textEditingManager.exitTextEditing()),this.isEditing=!0,this.initHiddenTextarea(),this.hiddenTextarea.focus(),this.hiddenTextarea.value=this.text,this._updateTextarea(),this._saveEditingProps(),this._setEditingProps(),this._textBeforeEdit=this.text,this._tick()}updateSelectionOnMouseMove(t){if(this.getActiveControl())return;const e=this.hiddenTextarea;Yt(e).activeElement!==e&&e.focus();const i=this.getSelectionStartFromPointer(t),s=this.selectionStart,n=this.selectionEnd;(i===this.__selectionStartOnMouseDown&&s!==n||s!==i&&n!==i)&&(i>this.__selectionStartOnMouseDown?(this.selectionStart=this.__selectionStartOnMouseDown,this.selectionEnd=i):(this.selectionStart=i,this.selectionEnd=this.__selectionStartOnMouseDown),this.selectionStart===s&&this.selectionEnd===n||(this._fireSelectionChanged(),this._updateTextarea(),this.renderCursorOrSelection()))}_setEditingProps(){this.hoverCursor="text",this.canvas&&(this.canvas.defaultCursor=this.canvas.moveCursor="text"),this.borderColor=this.editingBorderColor,this.hasControls=this.selectable=!1,this.lockMovementX=this.lockMovementY=!0}fromStringToGraphemeSelection(t,e,i){const s=i.slice(0,t),n=this.graphemeSplit(s).length;if(t===e)return{selectionStart:n,selectionEnd:n};const o=i.slice(t,e);return{selectionStart:n,selectionEnd:n+this.graphemeSplit(o).length}}fromGraphemeToStringSelection(t,e,i){const s=i.slice(0,t).join("").length;return t===e?{selectionStart:s,selectionEnd:s}:{selectionStart:s,selectionEnd:s+i.slice(t,e).join("").length}}_updateTextarea(){if(this.cursorOffsetCache={},this.hiddenTextarea){if(!this.inCompositionMode){const t=this.fromGraphemeToStringSelection(this.selectionStart,this.selectionEnd,this._text);this.hiddenTextarea.selectionStart=t.selectionStart,this.hiddenTextarea.selectionEnd=t.selectionEnd}this.updateTextareaPosition()}}updateFromTextArea(){if(!this.hiddenTextarea)return;this.cursorOffsetCache={};const t=this.hiddenTextarea;this.text=t.value,this.set("dirty",!0),this.initDimensions(),this.setCoords();const e=this.fromStringToGraphemeSelection(t.selectionStart,t.selectionEnd,t.value);this.selectionEnd=this.selectionStart=e.selectionEnd,this.inCompositionMode||(this.selectionStart=e.selectionStart),this.updateTextareaPosition()}updateTextareaPosition(){if(this.selectionStart===this.selectionEnd){const t=this._calcTextareaPosition();this.hiddenTextarea.style.left=t.left,this.hiddenTextarea.style.top=t.top}}_calcTextareaPosition(){if(!this.canvas)return{left:"1px",top:"1px"};const t=this.inCompositionMode?this.compositionStart:this.selectionStart,e=this._getCursorBoundaries(t),i=this.get2DCursorLocation(t),s=i.lineIndex,n=i.charIndex,o=this.getValueOfPropertyAt(s,n,"fontSize")*this.lineHeight,a=e.leftOffset,h=this.getCanvasRetinaScaling(),l=this.canvas.upperCanvasEl,c=l.width/h,u=l.height/h,d=c-o,g=u-o,f=new C(e.left+a,e.top+e.topOffset+o).transform(this.calcTransformMatrix()).transform(this.canvas.viewportTransform).multiply(new C(l.clientWidth/c,l.clientHeight/u));return f.x<0&&(f.x=0),f.x>d&&(f.x=d),f.y<0&&(f.y=0),f.y>g&&(f.y=g),f.x+=this.canvas._offset.left,f.y+=this.canvas._offset.top,{left:"".concat(f.x,"px"),top:"".concat(f.y,"px"),fontSize:"".concat(o,"px"),charHeight:o}}_saveEditingProps(){this._savedProps={hasControls:this.hasControls,borderColor:this.borderColor,lockMovementX:this.lockMovementX,lockMovementY:this.lockMovementY,hoverCursor:this.hoverCursor,selectable:this.selectable,defaultCursor:this.canvas&&this.canvas.defaultCursor,moveCursor:this.canvas&&this.canvas.moveCursor}}_restoreEditingProps(){this._savedProps&&(this.hoverCursor=this._savedProps.hoverCursor,this.hasControls=this._savedProps.hasControls,this.borderColor=this._savedProps.borderColor,this.selectable=this._savedProps.selectable,this.lockMovementX=this._savedProps.lockMovementX,this.lockMovementY=this._savedProps.lockMovementY,this.canvas&&(this.canvas.defaultCursor=this._savedProps.defaultCursor||this.canvas.defaultCursor,this.canvas.moveCursor=this._savedProps.moveCursor||this.canvas.moveCursor),delete this._savedProps)}_exitEditing(){const t=this.hiddenTextarea;this.selected=!1,this.isEditing=!1,t&&(t.blur&&t.blur(),t.parentNode&&t.parentNode.removeChild(t)),this.hiddenTextarea=null,this.abortCursorAnimation(),this.selectionStart!==this.selectionEnd&&this.clearContextTop()}exitEditingImpl(){this._exitEditing(),this.selectionEnd=this.selectionStart,this._restoreEditingProps(),this._forceClearCache&&(this.initDimensions(),this.setCoords())}exitEditing(){const t=this._textBeforeEdit!==this.text;return this.exitEditingImpl(),this.fire("editing:exited"),t&&this.fire(ys),this.canvas&&(this.canvas.fire("text:editing:exited",{target:this}),t&&this.canvas.fire("object:modified",{target:this})),this}_removeExtraneousStyles(){for(const t in this.styles)this._textLines[t]||delete this.styles[t]}removeStyleFromTo(t,e){const{lineIndex:i,charIndex:s}=this.get2DCursorLocation(t,!0),{lineIndex:n,charIndex:o}=this.get2DCursorLocation(e,!0);if(i!==n){if(this.styles[i])for(let a=s;a<this._unwrappedTextLines[i].length;a++)delete this.styles[i][a];if(this.styles[n])for(let a=o;a<this._unwrappedTextLines[n].length;a++){const h=this.styles[n][a];h&&(this.styles[i]||(this.styles[i]={}),this.styles[i][s+a-o]=h)}for(let a=i+1;a<=n;a++)delete this.styles[a];this.shiftLineStyles(n,i-n)}else if(this.styles[i]){const a=this.styles[i],h=o-s;for(let l=s;l<o;l++)delete a[l];for(const l in this.styles[i]){const c=parseInt(l,10);c>=o&&(a[c-h]=a[l],delete a[l])}}}shiftLineStyles(t,e){const i=Object.assign({},this.styles);for(const s in this.styles){const n=parseInt(s,10);n>t&&(this.styles[n+e]=i[n],i[n-e]||delete this.styles[n])}}insertNewlineStyleObject(t,e,i,s){const n={},o=this._unwrappedTextLines[t].length,a=o===e;let h=!1;i||(i=1),this.shiftLineStyles(t,i);const l=this.styles[t]?this.styles[t][e===0?e:e-1]:void 0;for(const u in this.styles[t]){const d=parseInt(u,10);d>=e&&(h=!0,n[d-e]=this.styles[t][u],a&&e===0||delete this.styles[t][u])}let c=!1;for(h&&!a&&(this.styles[t+i]=n,c=!0),(c||o>e)&&i--;i>0;)s&&s[i-1]?this.styles[t+i]={0:y({},s[i-1])}:l?this.styles[t+i]={0:y({},l)}:delete this.styles[t+i],i--;this._forceClearCache=!0}insertCharStyleObject(t,e,i,s){this.styles||(this.styles={});const n=this.styles[t],o=n?y({},n):{};i||(i=1);for(const h in o){const l=parseInt(h,10);l>=e&&(n[l+i]=o[l],o[l-i]||delete n[l])}if(this._forceClearCache=!0,s){for(;i--;)Object.keys(s[i]).length&&(this.styles[t]||(this.styles[t]={}),this.styles[t][e+i]=y({},s[i]));return}if(!n)return;const a=n[e?e-1:1];for(;a&&i--;)this.styles[t][e+i]=y({},a)}insertNewStyleBlock(t,e,i){const s=this.get2DCursorLocation(e,!0),n=[0];let o,a=0;for(let h=0;h<t.length;h++)t[h]===`
`?(a++,n[a]=0):n[a]++;for(n[0]>0&&(this.insertCharStyleObject(s.lineIndex,s.charIndex,n[0],i),i=i&&i.slice(n[0]+1)),a&&this.insertNewlineStyleObject(s.lineIndex,s.charIndex+n[0],a),o=1;o<a;o++)n[o]>0?this.insertCharStyleObject(s.lineIndex+o,0,n[o],i):i&&this.styles[s.lineIndex+o]&&i[0]&&(this.styles[s.lineIndex+o][0]=i[0]),i=i&&i.slice(n[o]+1);n[o]>0&&this.insertCharStyleObject(s.lineIndex+o,0,n[o],i)}removeChars(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t+1;this.removeStyleFromTo(t,e),this._text.splice(t,e-t),this.text=this._text.join(""),this.set("dirty",!0),this.initDimensions(),this.setCoords(),this._removeExtraneousStyles()}insertChars(t,e,i){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:i;s>i&&this.removeStyleFromTo(i,s);const n=this.graphemeSplit(t);this.insertNewStyleBlock(n,i,e),this._text=[...this._text.slice(0,i),...n,...this._text.slice(s)],this.text=this._text.join(""),this.set("dirty",!0),this.initDimensions(),this.setCoords(),this._removeExtraneousStyles()}setSelectionStartEndWithShift(t,e,i){i<=t?(e===t?this._selectionDirection=V:this._selectionDirection===it&&(this._selectionDirection=V,this.selectionEnd=t),this.selectionStart=i):i>t&&i<e?this._selectionDirection===it?this.selectionEnd=i:this.selectionStart=i:(e===t?this._selectionDirection=it:this._selectionDirection===V&&(this._selectionDirection=it,this.selectionStart=e),this.selectionEnd=i)}}class jd extends Id{initHiddenTextarea(){const t=this.canvas&&Yt(this.canvas.getElement())||li(),e=t.createElement("textarea");Object.entries({autocapitalize:"off",autocorrect:"off",autocomplete:"off",spellcheck:"false","data-fabric":"textarea",wrap:"off"}).map(o=>{let[a,h]=o;return e.setAttribute(a,h)});const{top:i,left:s,fontSize:n}=this._calcTextareaPosition();e.style.cssText="position: absolute; top: ".concat(i,"; left: ").concat(s,"; z-index: -999; opacity: 0; width: 1px; height: 1px; font-size: 1px; padding-top: ").concat(n,";"),(this.hiddenTextareaContainer||t.body).appendChild(e),Object.entries({blur:"blur",keydown:"onKeyDown",keyup:"onKeyUp",input:"onInput",copy:"copy",cut:"copy",paste:"paste",compositionstart:"onCompositionStart",compositionupdate:"onCompositionUpdate",compositionend:"onCompositionEnd"}).map(o=>{let[a,h]=o;return e.addEventListener(a,this[h].bind(this))}),this.hiddenTextarea=e}blur(){this.abortCursorAnimation()}onKeyDown(t){if(!this.isEditing)return;const e=this.direction==="rtl"?this.keysMapRtl:this.keysMap;if(t.keyCode in e)this[e[t.keyCode]](t);else{if(!(t.keyCode in this.ctrlKeysMapDown)||!t.ctrlKey&&!t.metaKey)return;this[this.ctrlKeysMapDown[t.keyCode]](t)}t.stopImmediatePropagation(),t.preventDefault(),t.keyCode>=33&&t.keyCode<=40?(this.inCompositionMode=!1,this.clearContextTop(),this.renderCursorOrSelection()):this.canvas&&this.canvas.requestRenderAll()}onKeyUp(t){!this.isEditing||this._copyDone||this.inCompositionMode?this._copyDone=!1:t.keyCode in this.ctrlKeysMapUp&&(t.ctrlKey||t.metaKey)&&(this[this.ctrlKeysMapUp[t.keyCode]](t),t.stopImmediatePropagation(),t.preventDefault(),this.canvas&&this.canvas.requestRenderAll())}onInput(t){const e=this.fromPaste,{value:i,selectionStart:s,selectionEnd:n}=this.hiddenTextarea;if(this.fromPaste=!1,t&&t.stopPropagation(),!this.isEditing)return;const o=()=>{this.updateFromTextArea(),this.fire(ms),this.canvas&&(this.canvas.fire("text:changed",{target:this}),this.canvas.requestRenderAll())};if(this.hiddenTextarea.value==="")return this.styles={},void o();const a=this._splitTextIntoLines(i).graphemeText,h=this._text.length,l=a.length,c=this.selectionStart,u=this.selectionEnd,d=c!==u;let g,f,p,v,b=l-h;const w=this.fromStringToGraphemeSelection(s,n,i),x=c>w.selectionStart;d?(f=this._text.slice(c,u),b+=u-c):l<h&&(f=x?this._text.slice(u+b,u):this._text.slice(c,c-b));const _=a.slice(w.selectionEnd-b,w.selectionEnd);if(f&&f.length&&(_.length&&(g=this.getSelectionStyles(c,c+1,!1),g=_.map(()=>g[0])),d?(p=c,v=u):x?(p=u-f.length,v=u):(p=u,v=u+f.length),this.removeStyleFromTo(p,v)),_.length){const{copyPasteData:k}=ee();e&&_.join("")===k.copiedText&&!W.disableStyleCopyPaste&&(g=k.copiedTextStyle),this.insertNewStyleBlock(_,c,g)}o()}onCompositionStart(){this.inCompositionMode=!0}onCompositionEnd(){this.inCompositionMode=!1}onCompositionUpdate(t){let{target:e}=t;const{selectionStart:i,selectionEnd:s}=e;this.compositionStart=i,this.compositionEnd=s,this.updateTextareaPosition()}copy(){if(this.selectionStart===this.selectionEnd)return;const{copyPasteData:t}=ee();t.copiedText=this.getSelectedText(),W.disableStyleCopyPaste?t.copiedTextStyle=void 0:t.copiedTextStyle=this.getSelectionStyles(this.selectionStart,this.selectionEnd,!0),this._copyDone=!0}paste(){this.fromPaste=!0}_getWidthBeforeCursor(t,e){let i,s=this._getLineLeftOffset(t);return e>0&&(i=this.__charBounds[t][e-1],s+=i.left+i.width),s}getDownCursorOffset(t,e){const i=this._getSelectionForOffset(t,e),s=this.get2DCursorLocation(i),n=s.lineIndex;if(n===this._textLines.length-1||t.metaKey||t.keyCode===34)return this._text.length-i;const o=s.charIndex,a=this._getWidthBeforeCursor(n,o),h=this._getIndexOnLine(n+1,a);return this._textLines[n].slice(o).length+h+1+this.missingNewlineOffset(n)}_getSelectionForOffset(t,e){return t.shiftKey&&this.selectionStart!==this.selectionEnd&&e?this.selectionEnd:this.selectionStart}getUpCursorOffset(t,e){const i=this._getSelectionForOffset(t,e),s=this.get2DCursorLocation(i),n=s.lineIndex;if(n===0||t.metaKey||t.keyCode===33)return-i;const o=s.charIndex,a=this._getWidthBeforeCursor(n,o),h=this._getIndexOnLine(n-1,a),l=this._textLines[n].slice(0,o),c=this.missingNewlineOffset(n-1);return-this._textLines[n-1].length+h-l.length+(1-c)}_getIndexOnLine(t,e){const i=this._textLines[t];let s,n,o=this._getLineLeftOffset(t),a=0;for(let h=0,l=i.length;h<l;h++)if(s=this.__charBounds[t][h].width,o+=s,o>e){n=!0;const c=o-s,u=o,d=Math.abs(c-e);a=Math.abs(u-e)<d?h:h-1;break}return n||(a=i.length-1),a}moveCursorDown(t){this.selectionStart>=this._text.length&&this.selectionEnd>=this._text.length||this._moveCursorUpOrDown("Down",t)}moveCursorUp(t){this.selectionStart===0&&this.selectionEnd===0||this._moveCursorUpOrDown("Up",t)}_moveCursorUpOrDown(t,e){const i=this["get".concat(t,"CursorOffset")](e,this._selectionDirection===it);if(e.shiftKey?this.moveCursorWithShift(i):this.moveCursorWithoutShift(i),i!==0){const s=this.text.length;this.selectionStart=Ve(0,this.selectionStart,s),this.selectionEnd=Ve(0,this.selectionEnd,s),this.abortCursorAnimation(),this.initDelayedCursor(),this._fireSelectionChanged(),this._updateTextarea()}}moveCursorWithShift(t){const e=this._selectionDirection===V?this.selectionStart+t:this.selectionEnd+t;return this.setSelectionStartEndWithShift(this.selectionStart,this.selectionEnd,e),t!==0}moveCursorWithoutShift(t){return t<0?(this.selectionStart+=t,this.selectionEnd=this.selectionStart):(this.selectionEnd+=t,this.selectionStart=this.selectionEnd),t!==0}moveCursorLeft(t){this.selectionStart===0&&this.selectionEnd===0||this._moveCursorLeftOrRight("Left",t)}_move(t,e,i){let s;if(t.altKey)s=this["findWordBoundary".concat(i)](this[e]);else{if(!t.metaKey&&t.keyCode!==35&&t.keyCode!==36)return this[e]+=i==="Left"?-1:1,!0;s=this["findLineBoundary".concat(i)](this[e])}return s!==void 0&&this[e]!==s&&(this[e]=s,!0)}_moveLeft(t,e){return this._move(t,e,"Left")}_moveRight(t,e){return this._move(t,e,"Right")}moveCursorLeftWithoutShift(t){let e=!0;return this._selectionDirection=V,this.selectionEnd===this.selectionStart&&this.selectionStart!==0&&(e=this._moveLeft(t,"selectionStart")),this.selectionEnd=this.selectionStart,e}moveCursorLeftWithShift(t){return this._selectionDirection===it&&this.selectionStart!==this.selectionEnd?this._moveLeft(t,"selectionEnd"):this.selectionStart!==0?(this._selectionDirection=V,this._moveLeft(t,"selectionStart")):void 0}moveCursorRight(t){this.selectionStart>=this._text.length&&this.selectionEnd>=this._text.length||this._moveCursorLeftOrRight("Right",t)}_moveCursorLeftOrRight(t,e){const i="moveCursor".concat(t).concat(e.shiftKey?"WithShift":"WithoutShift");this._currentCursorOpacity=1,this[i](e)&&(this.abortCursorAnimation(),this.initDelayedCursor(),this._fireSelectionChanged(),this._updateTextarea())}moveCursorRightWithShift(t){return this._selectionDirection===V&&this.selectionStart!==this.selectionEnd?this._moveRight(t,"selectionStart"):this.selectionEnd!==this._text.length?(this._selectionDirection=it,this._moveRight(t,"selectionEnd")):void 0}moveCursorRightWithoutShift(t){let e=!0;return this._selectionDirection=it,this.selectionStart===this.selectionEnd?(e=this._moveRight(t,"selectionStart"),this.selectionEnd=this.selectionStart):this.selectionStart=this.selectionEnd,e}}const Gh=r=>!!r.button;class Bd extends jd{constructor(){super(...arguments),m(this,"draggableTextDelegate",void 0)}initBehavior(){this.on("mousedown",this._mouseDownHandler),this.on("mouseup",this.mouseUpHandler),this.on("mousedblclick",this.doubleClickHandler),this.on("mousetripleclick",this.tripleClickHandler),this.draggableTextDelegate=new Rd(this),super.initBehavior()}shouldStartDragging(){return this.draggableTextDelegate.isActive()}onDragStart(t){return this.draggableTextDelegate.onDragStart(t)}canDrop(t){return this.draggableTextDelegate.canDrop(t)}doubleClickHandler(t){this.isEditing&&(this.selectWord(this.getSelectionStartFromPointer(t.e)),this.renderCursorOrSelection())}tripleClickHandler(t){this.isEditing&&(this.selectLine(this.getSelectionStartFromPointer(t.e)),this.renderCursorOrSelection())}_mouseDownHandler(t){let{e,alreadySelected:i}=t;this.canvas&&this.editable&&!Gh(e)&&!this.getActiveControl()&&(this.draggableTextDelegate.start(e)||(this.canvas.textEditingManager.register(this),i&&(this.inCompositionMode=!1,this.setCursorByClick(e)),this.isEditing&&(this.__selectionStartOnMouseDown=this.selectionStart,this.selectionStart===this.selectionEnd&&this.abortCursorAnimation(),this.renderCursorOrSelection()),this.selected||(this.selected=i||this.isEditing)))}mouseUpHandler(t){let{e,transform:i}=t;const s=this.draggableTextDelegate.end(e);if(this.canvas){this.canvas.textEditingManager.unregister(this);const n=this.canvas._activeObject;if(n&&n!==this)return}!this.editable||this.group&&!this.group.interactive||i&&i.actionPerformed||Gh(e)||s||this.selected&&!this.getActiveControl()&&(this.enterEditing(e),this.selectionStart===this.selectionEnd?this.initDelayedCursor(!0):this.renderCursorOrSelection())}setCursorByClick(t){const e=this.getSelectionStartFromPointer(t),i=this.selectionStart,s=this.selectionEnd;t.shiftKey?this.setSelectionStartEndWithShift(i,s,e):(this.selectionStart=e,this.selectionEnd=e),this.isEditing&&(this._fireSelectionChanged(),this._updateTextarea())}getSelectionStartFromPointer(t){const e=this.canvas.getScenePoint(t).transform(Dt(this.calcTransformMatrix())).add(new C(-this._getLeftOffset(),-this._getTopOffset()));let i=0,s=0,n=0;for(let l=0;l<this._textLines.length&&i<=e.y;l++)i+=this.getHeightOfLine(l),n=l,l>0&&(s+=this._textLines[l-1].length+this.missingNewlineOffset(l-1));let o=Math.abs(this._getLineLeftOffset(n));const a=this._textLines[n].length,h=this.__charBounds[n];for(let l=0;l<a;l++){const c=o+h[l].kernedWidth;if(e.x<=c){Math.abs(e.x-c)<=Math.abs(e.x-o)&&s++;break}o=c,s++}return Math.min(this.flipX?a-s:s,this._text.length)}}const Ys="moveCursorUp",Gs="moveCursorDown",Us="moveCursorLeft",qs="moveCursorRight",Ks="exitEditing",Uh=(r,t)=>{const e=t.getRetinaScaling();r.setTransform(e,0,0,e,0,0);const i=t.viewportTransform;r.transform(i[0],i[1],i[2],i[3],i[4],i[5])},Fd=y({selectionStart:0,selectionEnd:0,selectionColor:"rgba(17,119,255,0.3)",isEditing:!1,editable:!0,editingBorderColor:"rgba(102,153,255,0.25)",cursorWidth:2,cursorColor:"",cursorDelay:1e3,cursorDuration:600,caching:!0,hiddenTextareaContainer:null,keysMap:{9:Ks,27:Ks,33:Ys,34:Gs,35:qs,36:Us,37:Us,38:Ys,39:qs,40:Gs},keysMapRtl:{9:Ks,27:Ks,33:Ys,34:Gs,35:Us,36:qs,37:qs,38:Ys,39:Us,40:Gs},ctrlKeysMapDown:{65:"cmdAll"},ctrlKeysMapUp:{67:"copy",88:"cut"}},{_selectionDirection:null,_reSpace:/\s|\r?\n/,inCompositionMode:!1});class ye extends Bd{static getDefaults(){return y(y({},super.getDefaults()),ye.ownDefaults)}get type(){const t=super.type;return t==="itext"?"i-text":t}constructor(t,e){super(t,y(y({},ye.ownDefaults),e)),this.initBehavior()}_set(t,e){return this.isEditing&&this._savedProps&&t in this._savedProps?(this._savedProps[t]=e,this):(t==="canvas"&&(this.canvas instanceof jr&&this.canvas.textEditingManager.remove(this),e instanceof jr&&e.textEditingManager.add(this)),super._set(t,e))}setSelectionStart(t){t=Math.max(t,0),this._updateAndFire("selectionStart",t)}setSelectionEnd(t){t=Math.min(t,this.text.length),this._updateAndFire("selectionEnd",t)}_updateAndFire(t,e){this[t]!==e&&(this._fireSelectionChanged(),this[t]=e),this._updateTextarea()}_fireSelectionChanged(){this.fire("selection:changed"),this.canvas&&this.canvas.fire("text:selection:changed",{target:this})}initDimensions(){this.isEditing&&this.initDelayedCursor(),super.initDimensions()}getSelectionStyles(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.selectionStart||0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.selectionEnd,i=arguments.length>2?arguments[2]:void 0;return super.getSelectionStyles(t,e,i)}setSelectionStyles(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.selectionStart||0,i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.selectionEnd;return super.setSelectionStyles(t,e,i)}get2DCursorLocation(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.selectionStart,e=arguments.length>1?arguments[1]:void 0;return super.get2DCursorLocation(t,e)}render(t){super.render(t),this.cursorOffsetCache={},this.renderCursorOrSelection()}toCanvasElement(t){const e=this.isEditing;this.isEditing=!1;const i=super.toCanvasElement(t);return this.isEditing=e,i}renderCursorOrSelection(){if(!this.isEditing||!this.canvas)return;const t=this.clearContextTop(!0);if(!t)return;const e=this._getCursorBoundaries(),i=this.findAncestorsWithClipPath(),s=i.length>0;let n,o=t;if(s){n=Mt(t.canvas),o=n.getContext("2d"),Uh(o,this.canvas);const a=this.calcTransformMatrix();o.transform(a[0],a[1],a[2],a[3],a[4],a[5])}if(this.selectionStart!==this.selectionEnd||this.inCompositionMode?this.renderSelection(o,e):this.renderCursor(o,e),s)for(const a of i){const h=a.clipPath,l=Mt(t.canvas),c=l.getContext("2d");if(Uh(c,this.canvas),!h.absolutePositioned){const u=a.calcTransformMatrix();c.transform(u[0],u[1],u[2],u[3],u[4],u[5])}h.transform(c),h.drawObject(c,!0,{}),this.drawClipPathOnCache(o,h,l)}s&&(t.setTransform(1,0,0,1,0,0),t.drawImage(n,0,0)),this.canvas.contextTopDirty=!0,t.restore()}findAncestorsWithClipPath(){const t=[];let e=this;for(;e;)e.clipPath&&t.push(e),e=e.parent;return t}_getCursorBoundaries(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.selectionStart,e=arguments.length>1?arguments[1]:void 0;const i=this._getLeftOffset(),s=this._getTopOffset(),n=this._getCursorBoundariesOffsets(t,e);return{left:i,top:s,leftOffset:n.left,topOffset:n.top}}_getCursorBoundariesOffsets(t,e){return e?this.__getCursorBoundariesOffsets(t):this.cursorOffsetCache&&"top"in this.cursorOffsetCache?this.cursorOffsetCache:this.cursorOffsetCache=this.__getCursorBoundariesOffsets(t)}__getCursorBoundariesOffsets(t){let e=0,i=0;const{charIndex:s,lineIndex:n}=this.get2DCursorLocation(t);for(let l=0;l<n;l++)e+=this.getHeightOfLine(l);const o=this._getLineLeftOffset(n),a=this.__charBounds[n][s];a&&(i=a.left),this.charSpacing!==0&&s===this._textLines[n].length&&(i-=this._getWidthOfCharSpacing());const h={top:e,left:o+(i>0?i:0)};return this.direction==="rtl"&&(this.textAlign===it||this.textAlign===re||this.textAlign===Ki?h.left*=-1:this.textAlign===V||this.textAlign===Xs?h.left=o-(i>0?i:0):this.textAlign!==j&&this.textAlign!==Zi||(h.left=o-(i>0?i:0))),h}renderCursorAt(t){this._renderCursor(this.canvas.contextTop,this._getCursorBoundaries(t,!0),t)}renderCursor(t,e){this._renderCursor(t,e,this.selectionStart)}getCursorRenderingData(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.selectionStart,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this._getCursorBoundaries(t);const i=this.get2DCursorLocation(t),s=i.lineIndex,n=i.charIndex>0?i.charIndex-1:0,o=this.getValueOfPropertyAt(s,n,"fontSize"),a=this.getObjectScaling().x*this.canvas.getZoom(),h=this.cursorWidth/a,l=this.getValueOfPropertyAt(s,n,"deltaY"),c=e.topOffset+(1-this._fontSizeFraction)*this.getHeightOfLine(s)/this.lineHeight-o*(1-this._fontSizeFraction);return{color:this.cursorColor||this.getValueOfPropertyAt(s,n,"fill"),opacity:this._currentCursorOpacity,left:e.left+e.leftOffset-h/2,top:c+e.top+l,width:h,height:o}}_renderCursor(t,e,i){const{color:s,opacity:n,left:o,top:a,width:h,height:l}=this.getCursorRenderingData(i,e);t.fillStyle=s,t.globalAlpha=n,t.fillRect(o,a,h,l)}renderSelection(t,e){const i={selectionStart:this.inCompositionMode?this.hiddenTextarea.selectionStart:this.selectionStart,selectionEnd:this.inCompositionMode?this.hiddenTextarea.selectionEnd:this.selectionEnd};this._renderSelection(t,i,e)}renderDragSourceEffect(){const t=this.draggableTextDelegate.getDragStartSelection();this._renderSelection(this.canvas.contextTop,t,this._getCursorBoundaries(t.selectionStart,!0))}renderDropTargetEffect(t){const e=this.getSelectionStartFromPointer(t);this.renderCursorAt(e)}_renderSelection(t,e,i){const s=e.selectionStart,n=e.selectionEnd,o=this.textAlign.includes(re),a=this.get2DCursorLocation(s),h=this.get2DCursorLocation(n),l=a.lineIndex,c=h.lineIndex,u=a.charIndex<0?0:a.charIndex,d=h.charIndex<0?0:h.charIndex;for(let g=l;g<=c;g++){const f=this._getLineLeftOffset(g)||0;let p=this.getHeightOfLine(g),v=0,b=0,w=0;if(g===l&&(b=this.__charBounds[l][u].left),g>=l&&g<c)w=o&&!this.isEndOfWrapping(g)?this.width:this.getLineWidth(g)||5;else if(g===c)if(d===0)w=this.__charBounds[c][d].left;else{const L=this._getWidthOfCharSpacing();w=this.__charBounds[c][d-1].left+this.__charBounds[c][d-1].width-L}v=p,(this.lineHeight<1||g===c&&this.lineHeight>1)&&(p/=this.lineHeight);let x=i.left+f+b,_=p,k=0;const A=w-b;this.inCompositionMode?(t.fillStyle=this.compositionColor||"black",_=1,k=p):t.fillStyle=this.selectionColor,this.direction==="rtl"&&(this.textAlign===it||this.textAlign===re||this.textAlign===Ki?x=this.width-x-A:this.textAlign===V||this.textAlign===Xs?x=i.left+f-w:this.textAlign!==j&&this.textAlign!==Zi||(x=i.left+f-w)),t.fillRect(x,i.top+i.topOffset+k,A,_),i.topOffset+=v}}getCurrentCharFontSize(){const t=this._getCurrentCharIndex();return this.getValueOfPropertyAt(t.l,t.c,"fontSize")}getCurrentCharColor(){const t=this._getCurrentCharIndex();return this.getValueOfPropertyAt(t.l,t.c,at)}_getCurrentCharIndex(){const t=this.get2DCursorLocation(this.selectionStart,!0),e=t.charIndex>0?t.charIndex-1:0;return{l:t.lineIndex,c:e}}dispose(){this.exitEditingImpl(),this.draggableTextDelegate.dispose(),super.dispose()}}m(ye,"ownDefaults",Fd),m(ye,"type","IText"),D.setClass(ye),D.setClass(ye,"i-text");class De extends ye{static getDefaults(){return y(y({},super.getDefaults()),De.ownDefaults)}constructor(t,e){super(t,y(y({},De.ownDefaults),e))}static createControls(){return{controls:wu()}}initDimensions(){this.initialized&&(this.isEditing&&this.initDelayedCursor(),this._clearCache(),this.dynamicMinWidth=0,this._styleMap=this._generateStyleMap(this._splitText()),this.dynamicMinWidth>this.width&&this._set("width",this.dynamicMinWidth),this.textAlign.includes(re)&&this.enlargeSpaces(),this.height=this.calcTextHeight())}_generateStyleMap(t){let e=0,i=0,s=0;const n={};for(let o=0;o<t.graphemeLines.length;o++)t.graphemeText[s]===`
`&&o>0?(i=0,s++,e++):!this.splitByGrapheme&&this._reSpaceAndTab.test(t.graphemeText[s])&&o>0&&(i++,s++),n[o]={line:e,offset:i},s+=t.graphemeLines[o].length,i+=t.graphemeLines[o].length;return n}styleHas(t,e){if(this._styleMap&&!this.isWrapping){const i=this._styleMap[e];i&&(e=i.line)}return super.styleHas(t,e)}isEmptyStyles(t){if(!this.styles)return!0;let e,i=0,s=t+1,n=!1;const o=this._styleMap[t],a=this._styleMap[t+1];o&&(t=o.line,i=o.offset),a&&(s=a.line,n=s===t,e=a.offset);const h=t===void 0?this.styles:{line:this.styles[t]};for(const l in h)for(const c in h[l]){const u=parseInt(c,10);if(u>=i&&(!n||u<e))for(const d in h[l][c])return!1}return!0}_getStyleDeclaration(t,e){if(this._styleMap&&!this.isWrapping){const i=this._styleMap[t];if(!i)return{};t=i.line,e=i.offset+e}return super._getStyleDeclaration(t,e)}_setStyleDeclaration(t,e,i){const s=this._styleMap[t];super._setStyleDeclaration(s.line,s.offset+e,i)}_deleteStyleDeclaration(t,e){const i=this._styleMap[t];super._deleteStyleDeclaration(i.line,i.offset+e)}_getLineStyle(t){const e=this._styleMap[t];return!!this.styles[e.line]}_setLineStyle(t){const e=this._styleMap[t];super._setLineStyle(e.line)}_wrapText(t,e){this.isWrapping=!0;const i=this.getGraphemeDataForRender(t),s=[];for(let n=0;n<i.wordsData.length;n++)s.push(...this._wrapLine(n,e,i));return this.isWrapping=!1,s}getGraphemeDataForRender(t){const e=this.splitByGrapheme,i=e?"":" ";let s=0;return{wordsData:t.map((n,o)=>{let a=0;const h=e?this.graphemeSplit(n):this.wordSplit(n);return h.length===0?[{word:[],width:0}]:h.map(l=>{const c=e?[l]:this.graphemeSplit(l),u=this._measureWord(c,o,a);return s=Math.max(u,s),a+=c.length+i.length,{word:c,width:u}})}),largestWordWidth:s}}_measureWord(t,e){let i,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,n=0;for(let o=0,a=t.length;o<a;o++)n+=this._getGraphemeBox(t[o],e,o+s,i,!0).kernedWidth,i=t[o];return n}wordSplit(t){return t.split(this._wordJoiners)}_wrapLine(t,e,i){let{largestWordWidth:s,wordsData:n}=i,o=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0;const a=this._getWidthOfCharSpacing(),h=this.splitByGrapheme,l=[],c=h?"":" ";let u=0,d=[],g=0,f=0,p=!0;e-=o;const v=Math.max(e,s,this.dynamicMinWidth),b=n[t];let w;for(g=0,w=0;w<b.length;w++){const{word:x,width:_}=b[w];g+=x.length,u+=f+_-a,u>v&&!p?(l.push(d),d=[],u=_,p=!0):u+=a,p||h||d.push(c),d=d.concat(x),f=h?0:this._measureWord([c],t,g),g++,p=!1}return w&&l.push(d),s+o>this.dynamicMinWidth&&(this.dynamicMinWidth=s-a+o),l}isEndOfWrapping(t){return!this._styleMap[t+1]||this._styleMap[t+1].line!==this._styleMap[t].line}missingNewlineOffset(t,e){return this.splitByGrapheme&&!e?this.isEndOfWrapping(t)?1:0:1}_splitTextIntoLines(t){const e=super._splitTextIntoLines(t),i=this._wrapText(e.lines,this.width),s=new Array(i.length);for(let n=0;n<i.length;n++)s[n]=i[n].join("");return e.lines=s,e.graphemeLines=i,e}getMinWidth(){return Math.max(this.minWidth,this.dynamicMinWidth)}_removeExtraneousStyles(){const t=new Map;for(const e in this._styleMap){const i=parseInt(e,10);if(this._textLines[i]){const s=this._styleMap[e].line;t.set("".concat(s),!0)}}for(const e in this.styles)t.has(e)||delete this.styles[e]}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return super.toObject(["minWidth","splitByGrapheme",...t])}}m(De,"type","Textbox"),m(De,"textLayoutProperties",[...ye.textLayoutProperties,"width"]),m(De,"ownDefaults",{minWidth:20,dynamicMinWidth:2,lockScalingFlip:!0,noScaleCache:!1,_wordJoiners:/[ \t\r]/,splitByGrapheme:!1}),D.setClass(De);class qh extends $s{shouldPerformLayout(t){return!!t.target.clipPath&&super.shouldPerformLayout(t)}shouldLayoutClipPath(){return!1}calcLayoutResult(t,e){const{target:i}=t,{clipPath:s,group:n}=i;if(!s||!this.shouldPerformLayout(t))return;const{width:o,height:a}=se(yh(i,s)),h=new C(o,a);if(s.absolutePositioned)return{center:He(s.getRelativeCenterPoint(),void 0,n?n.calcTransformMatrix():void 0),size:h};{const l=s.getRelativeCenterPoint().transform(i.calcOwnMatrix(),!0);if(this.shouldPerformLayout(t)){const{center:c=new C,correction:u=new C}=this.calcBoundingBox(e,t)||{};return{center:c.add(l),correction:u.subtract(l),size:h}}return{center:i.getRelativeCenterPoint().add(l),size:h}}}}m(qh,"type","clip-path"),D.setClass(qh);class Hr extends $s{getInitialSize(t,e){let{target:i}=t,{size:s}=e;return new C(i.width||s.x,i.height||s.y)}}m(Hr,"type","fixed"),D.setClass(Hr);class $d extends _i{subscribeTargets(t){const e=t.target;t.targets.reduce((i,s)=>(s.parent&&i.add(s.parent),i),new Set).forEach(i=>{i.layoutManager.subscribeTargets({target:i,targets:[e]})})}unsubscribeTargets(t){const e=t.target,i=e.getObjects();t.targets.reduce((s,n)=>(n.parent&&s.add(n.parent),s),new Set).forEach(s=>{!i.some(n=>n.parent===s)&&s.layoutManager.unsubscribeTargets({target:s,targets:[e]})})}}class Ge extends Ft{static getDefaults(){return y(y({},super.getDefaults()),Ge.ownDefaults)}constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),Object.assign(this,Ge.ownDefaults),this.setOptions(e);const{left:i,top:s,layoutManager:n}=e;this.groupInit(t,{left:i,top:s,layoutManager:n??new $d})}_shouldSetNestedCoords(){return!0}__objectSelectionMonitor(){}multiSelectAdd(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this.multiSelectionStacking==="selection-order"?this.add(...e):e.forEach(s=>{const n=this._objects.findIndex(a=>a.isInFrontOf(s)),o=n===-1?this.size():n;this.insertAt(o,s)})}canEnterGroup(t){return this.getObjects().some(e=>e.isDescendantOf(t)||t.isDescendantOf(e))?(Se("error","ActiveSelection: circular object trees are not supported, this call has no effect"),!1):super.canEnterGroup(t)}enterGroup(t,e){t.parent&&t.parent===t.group?t.parent._exitGroup(t):t.group&&t.parent!==t.group&&t.group.remove(t),this._enterGroup(t,e)}exitGroup(t,e){this._exitGroup(t,e),t.parent&&t.parent._enterGroup(t,!0)}_onAfterObjectsChange(t,e){super._onAfterObjectsChange(t,e);const i=new Set;e.forEach(s=>{const{parent:n}=s;n&&i.add(n)}),t===Dr?i.forEach(s=>{s._onAfterObjectsChange(Bs,e)}):i.forEach(s=>{s._set("dirty",!0)})}onDeselect(){return this.removeAll(),!1}toString(){return"#<ActiveSelection: (".concat(this.complexity(),")>")}shouldCache(){return!1}isOnACache(){return!1}_renderControls(t,e,i){t.save(),t.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1;const s=y(y({hasControls:!1},i),{},{forActiveSelection:!0});for(let n=0;n<this._objects.length;n++)this._objects[n]._renderControls(t,s);super._renderControls(t,e),t.restore()}}m(Ge,"type","ActiveSelection"),m(Ge,"ownDefaults",{multiSelectionStacking:"canvas-stacking"}),D.setClass(Ge),D.setClass(Ge,"activeSelection");class zd{constructor(){m(this,"resources",{})}applyFilters(t,e,i,s,n){const o=n.getContext("2d");if(!o)return;o.drawImage(e,0,0,i,s);const a={sourceWidth:i,sourceHeight:s,imageData:o.getImageData(0,0,i,s),originalEl:e,originalImageData:o.getImageData(0,0,i,s),canvasEl:n,ctx:o,filterBackend:this};t.forEach(l=>{l.applyTo(a)});const{imageData:h}=a;return h.width===i&&h.height===s||(n.width=h.width,n.height=h.height),o.putImageData(h,0,0),a}}class Kh{constructor(){let{tileSize:t=W.textureSize}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};m(this,"aPosition",new Float32Array([0,0,0,1,1,0,1,1])),m(this,"resources",{}),this.tileSize=t,this.setupGLContext(t,t),this.captureGPUInfo()}setupGLContext(t,e){this.dispose(),this.createWebGLCanvas(t,e)}createWebGLCanvas(t,e){const i=Mt({width:t,height:e}),s=i.getContext("webgl",{alpha:!0,premultipliedAlpha:!1,depth:!1,stencil:!1,antialias:!1});s&&(s.clearColor(0,0,0,0),this.canvas=i,this.gl=s)}applyFilters(t,e,i,s,n,o){const a=this.gl,h=n.getContext("2d");if(!a||!h)return;let l;o&&(l=this.getCachedTexture(o,e));const c={originalWidth:e.width||e.naturalWidth||0,originalHeight:e.height||e.naturalHeight||0,sourceWidth:i,sourceHeight:s,destinationWidth:i,destinationHeight:s,context:a,sourceTexture:this.createTexture(a,i,s,l?void 0:e),targetTexture:this.createTexture(a,i,s),originalTexture:l||this.createTexture(a,i,s,l?void 0:e),passes:t.length,webgl:!0,aPosition:this.aPosition,programCache:this.programCache,pass:0,filterBackend:this,targetCanvas:n},u=a.createFramebuffer();return a.bindFramebuffer(a.FRAMEBUFFER,u),t.forEach(d=>{d&&d.applyTo(c)}),function(d){const g=d.targetCanvas,f=g.width,p=g.height,v=d.destinationWidth,b=d.destinationHeight;f===v&&p===b||(g.width=v,g.height=b)}(c),this.copyGLTo2D(a,c),a.bindTexture(a.TEXTURE_2D,null),a.deleteTexture(c.sourceTexture),a.deleteTexture(c.targetTexture),a.deleteFramebuffer(u),h.setTransform(1,0,0,1,0,0),c}dispose(){this.canvas&&(this.canvas=null,this.gl=null),this.clearWebGLCaches()}clearWebGLCaches(){this.programCache={},this.textureCache={}}createTexture(t,e,i,s,n){const{NEAREST:o,TEXTURE_2D:a,RGBA:h,UNSIGNED_BYTE:l,CLAMP_TO_EDGE:c,TEXTURE_MAG_FILTER:u,TEXTURE_MIN_FILTER:d,TEXTURE_WRAP_S:g,TEXTURE_WRAP_T:f}=t,p=t.createTexture();return t.bindTexture(a,p),t.texParameteri(a,u,n||o),t.texParameteri(a,d,n||o),t.texParameteri(a,g,c),t.texParameteri(a,f,c),s?t.texImage2D(a,0,h,h,l,s):t.texImage2D(a,0,h,e,i,0,h,l,null),p}getCachedTexture(t,e,i){const{textureCache:s}=this;if(s[t])return s[t];{const n=this.createTexture(this.gl,e.width,e.height,e,i);return n&&(s[t]=n),n}}evictCachesForKey(t){this.textureCache[t]&&(this.gl.deleteTexture(this.textureCache[t]),delete this.textureCache[t])}copyGLTo2D(t,e){const i=t.canvas,s=e.targetCanvas,n=s.getContext("2d");if(!n)return;n.translate(0,s.height),n.scale(1,-1);const o=i.height-s.height;n.drawImage(i,0,o,s.width,s.height,0,0,s.width,s.height)}copyGLTo2DPutImageData(t,e){const i=e.targetCanvas.getContext("2d"),s=e.destinationWidth,n=e.destinationHeight,o=s*n*4;if(!i)return;const a=new Uint8Array(this.imageBuffer,0,o),h=new Uint8ClampedArray(this.imageBuffer,0,o);t.readPixels(0,0,s,n,t.RGBA,t.UNSIGNED_BYTE,a);const l=new ImageData(h,s,n);i.putImageData(l,0,0)}captureGPUInfo(){if(this.gpuInfo)return this.gpuInfo;const t=this.gl,e={renderer:"",vendor:""};if(!t)return e;const i=t.getExtension("WEBGL_debug_renderer_info");if(i){const s=t.getParameter(i.UNMASKED_RENDERER_WEBGL),n=t.getParameter(i.UNMASKED_VENDOR_WEBGL);s&&(e.renderer=s.toLowerCase()),n&&(e.vendor=n.toLowerCase())}return this.gpuInfo=e,e}}let Nr;function Hd(){const{WebGLProbe:r}=ee();return r.queryWebGL(jt()),W.enableGLFiltering&&r.isSupported(W.textureSize)?new Kh({tileSize:W.textureSize}):new zd}function Wr(){return!Nr&&(!(arguments.length>0&&arguments[0]!==void 0)||arguments[0])&&(Nr=Hd()),Nr}const Nd=["filters","resizeFilter","src","crossOrigin","type"],Zh=["cropX","cropY"];class Lt extends ft{static getDefaults(){return y(y({},super.getDefaults()),Lt.ownDefaults)}constructor(t,e){super(),m(this,"_lastScaleX",1),m(this,"_lastScaleY",1),m(this,"_filterScalingX",1),m(this,"_filterScalingY",1),this.filters=[],Object.assign(this,Lt.ownDefaults),this.setOptions(e),this.cacheKey="texture".concat(ke()),this.setElement(typeof t=="string"?(this.canvas&&Yt(this.canvas.getElement())||li()).getElementById(t):t,e)}getElement(){return this._element}setElement(t){var e;let i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.removeTexture(this.cacheKey),this.removeTexture("".concat(this.cacheKey,"_filtered")),this._element=t,this._originalElement=t,this._setWidthHeight(i),(e=t.classList)===null||e===void 0||e.add(Lt.CSS_CANVAS),this.filters.length!==0&&this.applyFilters(),this.resizeFilter&&this.applyResizeFilters()}removeTexture(t){const e=Wr(!1);e instanceof Kh&&e.evictCachesForKey(t)}dispose(){super.dispose(),this.removeTexture(this.cacheKey),this.removeTexture("".concat(this.cacheKey,"_filtered")),this._cacheContext=null,["_originalElement","_element","_filteredEl","_cacheCanvas"].forEach(t=>{const e=this[t];e&&ee().dispose(e),this[t]=void 0})}getCrossOrigin(){return this._originalElement&&(this._originalElement.crossOrigin||null)}getOriginalSize(){const t=this.getElement();return t?{width:t.naturalWidth||t.width,height:t.naturalHeight||t.height}:{width:0,height:0}}_stroke(t){if(!this.stroke||this.strokeWidth===0)return;const e=this.width/2,i=this.height/2;t.beginPath(),t.moveTo(-e,-i),t.lineTo(e,-i),t.lineTo(e,i),t.lineTo(-e,i),t.lineTo(-e,-i),t.closePath()}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];const e=[];return this.filters.forEach(i=>{i&&e.push(i.toObject())}),y(y({},super.toObject([...Zh,...t])),{},{src:this.getSrc(),crossOrigin:this.getCrossOrigin(),filters:e},this.resizeFilter?{resizeFilter:this.resizeFilter.toObject()}:{})}hasCrop(){return!!this.cropX||!!this.cropY||this.width<this._element.width||this.height<this._element.height}_toSVG(){const t=[],e=this._element,i=-this.width/2,s=-this.height/2;let n=[],o=[],a="",h="";if(!e)return[];if(this.hasCrop()){const l=ke();n.push('<clipPath id="imageCrop_'+l+`">
`,'	<rect x="'+i+'" y="'+s+'" width="'+this.width+'" height="'+this.height+`" />
`,`</clipPath>
`),a=' clip-path="url(#imageCrop_'+l+')" '}if(this.imageSmoothing||(h=' image-rendering="optimizeSpeed"'),t.push("	<image ","COMMON_PARTS",'xlink:href="'.concat(this.getSvgSrc(!0),'" x="').concat(i-this.cropX,'" y="').concat(s-this.cropY,'" width="').concat(e.width||e.naturalWidth,'" height="').concat(e.height||e.naturalHeight,'"').concat(h).concat(a,`></image>
`)),this.stroke||this.strokeDashArray){const l=this.fill;this.fill=null,o=['	<rect x="'.concat(i,'" y="').concat(s,'" width="').concat(this.width,'" height="').concat(this.height,'" style="').concat(this.getSvgStyles(),`" />
`)],this.fill=l}return n=this.paintFirst!==at?n.concat(o,t):n.concat(t,o),n}getSrc(t){const e=t?this._element:this._originalElement;return e?e.toDataURL?e.toDataURL():this.srcFromAttribute?e.getAttribute("src")||"":e.src:this.src||""}getSvgSrc(t){return this.getSrc(t)}setSrc(t){let{crossOrigin:e,signal:i}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return zi(t,{crossOrigin:e,signal:i}).then(s=>{e!==void 0&&this.set({crossOrigin:e}),this.setElement(s)})}toString(){return'#<Image: { src: "'.concat(this.getSrc(),'" }>')}applyResizeFilters(){const t=this.resizeFilter,e=this.minimumScaleTrigger,i=this.getTotalObjectScaling(),s=i.x,n=i.y,o=this._filteredEl||this._originalElement;if(this.group&&this.set("dirty",!0),!t||s>e&&n>e)return this._element=o,this._filterScalingX=1,this._filterScalingY=1,this._lastScaleX=s,void(this._lastScaleY=n);const a=Mt(o),{width:h,height:l}=o;this._element=a,this._lastScaleX=t.scaleX=s,this._lastScaleY=t.scaleY=n,Wr().applyFilters([t],o,h,l,this._element),this._filterScalingX=a.width/this._originalElement.width,this._filterScalingY=a.height/this._originalElement.height}applyFilters(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.filters||[];if(t=t.filter(n=>n&&!n.isNeutralState()),this.set("dirty",!0),this.removeTexture("".concat(this.cacheKey,"_filtered")),t.length===0)return this._element=this._originalElement,this._filteredEl=void 0,this._filterScalingX=1,void(this._filterScalingY=1);const e=this._originalElement,i=e.naturalWidth||e.width,s=e.naturalHeight||e.height;if(this._element===this._originalElement){const n=Mt({width:i,height:s});this._element=n,this._filteredEl=n}else this._filteredEl&&(this._element=this._filteredEl,this._filteredEl.getContext("2d").clearRect(0,0,i,s),this._lastScaleX=1,this._lastScaleY=1);Wr().applyFilters(t,this._originalElement,i,s,this._element,this.cacheKey),this._originalElement.width===this._element.width&&this._originalElement.height===this._element.height||(this._filterScalingX=this._element.width/this._originalElement.width,this._filterScalingY=this._element.height/this._originalElement.height)}_render(t){t.imageSmoothingEnabled=this.imageSmoothing,this.isMoving!==!0&&this.resizeFilter&&this._needsResize()&&this.applyResizeFilters(),this._stroke(t),this._renderPaintInOrder(t)}drawCacheOnCanvas(t){t.imageSmoothingEnabled=this.imageSmoothing,super.drawCacheOnCanvas(t)}shouldCache(){return this.needsItsOwnCache()}_renderFill(t){const e=this._element;if(!e)return;const i=this._filterScalingX,s=this._filterScalingY,n=this.width,o=this.height,a=Math.max(this.cropX,0),h=Math.max(this.cropY,0),l=e.naturalWidth||e.width,c=e.naturalHeight||e.height,u=a*i,d=h*s,g=Math.min(n*i,l-u),f=Math.min(o*s,c-d),p=-n/2,v=-o/2,b=Math.min(n,l/i-a),w=Math.min(o,c/s-h);e&&t.drawImage(e,u,d,g,f,p,v,b,w)}_needsResize(){const t=this.getTotalObjectScaling();return t.x!==this._lastScaleX||t.y!==this._lastScaleY}_resetWidthHeight(){this.set(this.getOriginalSize())}_setWidthHeight(){let{width:t,height:e}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const i=this.getOriginalSize();this.width=t||i.width,this.height=e||i.height}parsePreserveAspectRatioAttribute(){const t=Ia(this.preserveAspectRatio||""),e=this.width,i=this.height,s={width:e,height:i};let n,o=this._element.width,a=this._element.height,h=1,l=1,c=0,u=0,d=0,g=0;return!t||t.alignX===xt&&t.alignY===xt?(h=e/o,l=i/a):(t.meetOrSlice==="meet"&&(h=l=Ch(this._element,s),n=(e-o*h)/2,t.alignX==="Min"&&(c=-n),t.alignX==="Max"&&(c=n),n=(i-a*l)/2,t.alignY==="Min"&&(u=-n),t.alignY==="Max"&&(u=n)),t.meetOrSlice==="slice"&&(h=l=wh(this._element,s),n=o-e/h,t.alignX==="Mid"&&(d=n/2),t.alignX==="Max"&&(d=n),n=a-i/l,t.alignY==="Mid"&&(g=n/2),t.alignY==="Max"&&(g=n),o=e/h,a=i/l)),{width:o,height:a,scaleX:h,scaleY:l,offsetLeft:c,offsetTop:u,cropX:d,cropY:g}}static fromObject(t,e){let{filters:i,resizeFilter:s,src:n,crossOrigin:o,type:a}=t,h=U(t,Nd);return Promise.all([zi(n,y(y({},e),{},{crossOrigin:o})),i&&pi(i,e),s&&pi([s],e),Hi(h,e)]).then(l=>{let[c,u=[],[d]=[],g={}]=l;return new this(c,y(y({},h),{},{src:n,filters:u,resizeFilter:d},g))})}static fromURL(t){let{crossOrigin:e=null,signal:i}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0;return zi(t,{crossOrigin:e,signal:i}).then(n=>new this(n,s))}static async fromElement(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=arguments.length>2?arguments[2]:void 0;const s=ge(t,this.ATTRIBUTE_NAMES,i);return this.fromURL(s["xlink:href"]||s.href,e,s).catch(n=>(Se("log","Unable to parse Image",n),null))}}m(Lt,"type","Image"),m(Lt,"cacheProperties",[...ue,...Zh]),m(Lt,"ownDefaults",{strokeWidth:0,srcFromAttribute:!1,minimumScaleTrigger:.5,cropX:0,cropY:0,imageSmoothing:!0}),m(Lt,"CSS_CANVAS","canvas-img"),m(Lt,"ATTRIBUTE_NAMES",[...Oe,"x","y","width","height","preserveAspectRatio","xlink:href","href","crossOrigin","image-rendering"]),D.setClass(Lt),D.setSVGClass(Lt),ks(["pattern","defs","symbol","metadata","clipPath","mask","desc"]);const Zs=r=>r.webgl!==void 0,Vr="precision highp float",Wd=`
    `.concat(Vr,`;
    varying vec2 vTexCoord;
    uniform sampler2D uTexture;
    void main() {
      gl_FragColor = texture2D(uTexture, vTexCoord);
    }`),Vd=["type"],Xd=["type"],Yd=new RegExp(Vr,"g");class ht{get type(){return this.constructor.type}constructor(){let t=U(arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},Vd);Object.assign(this,this.constructor.defaults,t)}getFragmentSource(){return Wd}getVertexSource(){return`
    attribute vec2 aPosition;
    varying vec2 vTexCoord;
    void main() {
      vTexCoord = aPosition;
      gl_Position = vec4(aPosition * 2.0 - 1.0, 0.0, 1.0);
    }`}createProgram(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getFragmentSource(),i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.getVertexSource();const{WebGLProbe:{GLPrecision:s="highp"}}=ee();s!=="highp"&&(e=e.replace(Yd,Vr.replace("highp",s)));const n=t.createShader(t.VERTEX_SHADER),o=t.createShader(t.FRAGMENT_SHADER),a=t.createProgram();if(!n||!o||!a)throw new te("Vertex, fragment shader or program creation error");if(t.shaderSource(n,i),t.compileShader(n),!t.getShaderParameter(n,t.COMPILE_STATUS))throw new te("Vertex shader compile error for ".concat(this.type,": ").concat(t.getShaderInfoLog(n)));if(t.shaderSource(o,e),t.compileShader(o),!t.getShaderParameter(o,t.COMPILE_STATUS))throw new te("Fragment shader compile error for ".concat(this.type,": ").concat(t.getShaderInfoLog(o)));if(t.attachShader(a,n),t.attachShader(a,o),t.linkProgram(a),!t.getProgramParameter(a,t.LINK_STATUS))throw new te('Shader link error for "'.concat(this.type,'" ').concat(t.getProgramInfoLog(a)));const h=this.getUniformLocations(t,a)||{};return h.uStepW=t.getUniformLocation(a,"uStepW"),h.uStepH=t.getUniformLocation(a,"uStepH"),{program:a,attributeLocations:this.getAttributeLocations(t,a),uniformLocations:h}}getAttributeLocations(t,e){return{aPosition:t.getAttribLocation(e,"aPosition")}}getUniformLocations(t,e){const i=this.constructor.uniformLocations,s={};for(let n=0;n<i.length;n++)s[i[n]]=t.getUniformLocation(e,i[n]);return s}sendAttributeData(t,e,i){const s=e.aPosition,n=t.createBuffer();t.bindBuffer(t.ARRAY_BUFFER,n),t.enableVertexAttribArray(s),t.vertexAttribPointer(s,2,t.FLOAT,!1,0,0),t.bufferData(t.ARRAY_BUFFER,i,t.STATIC_DRAW)}_setupFrameBuffer(t){const e=t.context;if(t.passes>1){const i=t.destinationWidth,s=t.destinationHeight;t.sourceWidth===i&&t.sourceHeight===s||(e.deleteTexture(t.targetTexture),t.targetTexture=t.filterBackend.createTexture(e,i,s)),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,t.targetTexture,0)}else e.bindFramebuffer(e.FRAMEBUFFER,null),e.finish()}_swapTextures(t){t.passes--,t.pass++;const e=t.targetTexture;t.targetTexture=t.sourceTexture,t.sourceTexture=e}isNeutralState(t){return!1}applyTo(t){Zs(t)?(this._setupFrameBuffer(t),this.applyToWebGL(t),this._swapTextures(t)):this.applyTo2d(t)}applyTo2d(t){}getCacheKey(){return this.type}retrieveShader(t){const e=this.getCacheKey();return t.programCache[e]||(t.programCache[e]=this.createProgram(t.context)),t.programCache[e]}applyToWebGL(t){const e=t.context,i=this.retrieveShader(t);t.pass===0&&t.originalTexture?e.bindTexture(e.TEXTURE_2D,t.originalTexture):e.bindTexture(e.TEXTURE_2D,t.sourceTexture),e.useProgram(i.program),this.sendAttributeData(e,i.attributeLocations,t.aPosition),e.uniform1f(i.uniformLocations.uStepW,1/t.sourceWidth),e.uniform1f(i.uniformLocations.uStepH,1/t.sourceHeight),this.sendUniformData(e,i.uniformLocations),e.viewport(0,0,t.destinationWidth,t.destinationHeight),e.drawArrays(e.TRIANGLE_STRIP,0,4)}bindAdditionalTexture(t,e,i){t.activeTexture(i),t.bindTexture(t.TEXTURE_2D,e),t.activeTexture(t.TEXTURE0)}unbindAdditionalTexture(t,e){t.activeTexture(e),t.bindTexture(t.TEXTURE_2D,null),t.activeTexture(t.TEXTURE0)}sendUniformData(t,e){}createHelpLayer(t){if(!t.helpLayer){const{sourceWidth:e,sourceHeight:i}=t,s=Mt({width:e,height:i});t.helpLayer=s}}toObject(){const t=Object.keys(this.constructor.defaults||{});return y({type:this.type},t.reduce((e,i)=>(e[i]=this[i],e),{}))}toJSON(){return this.toObject()}static async fromObject(t,e){return new this(U(t,Xd))}}m(ht,"type","BaseFilter"),m(ht,"uniformLocations",[]);const Gd={multiply:`gl_FragColor.rgb *= uColor.rgb;
`,screen:`gl_FragColor.rgb = 1.0 - (1.0 - gl_FragColor.rgb) * (1.0 - uColor.rgb);
`,add:`gl_FragColor.rgb += uColor.rgb;
`,difference:`gl_FragColor.rgb = abs(gl_FragColor.rgb - uColor.rgb);
`,subtract:`gl_FragColor.rgb -= uColor.rgb;
`,lighten:`gl_FragColor.rgb = max(gl_FragColor.rgb, uColor.rgb);
`,darken:`gl_FragColor.rgb = min(gl_FragColor.rgb, uColor.rgb);
`,exclusion:`gl_FragColor.rgb += uColor.rgb - 2.0 * (uColor.rgb * gl_FragColor.rgb);
`,overlay:`
    if (uColor.r < 0.5) {
      gl_FragColor.r *= 2.0 * uColor.r;
    } else {
      gl_FragColor.r = 1.0 - 2.0 * (1.0 - gl_FragColor.r) * (1.0 - uColor.r);
    }
    if (uColor.g < 0.5) {
      gl_FragColor.g *= 2.0 * uColor.g;
    } else {
      gl_FragColor.g = 1.0 - 2.0 * (1.0 - gl_FragColor.g) * (1.0 - uColor.g);
    }
    if (uColor.b < 0.5) {
      gl_FragColor.b *= 2.0 * uColor.b;
    } else {
      gl_FragColor.b = 1.0 - 2.0 * (1.0 - gl_FragColor.b) * (1.0 - uColor.b);
    }
    `,tint:`
    gl_FragColor.rgb *= (1.0 - uColor.a);
    gl_FragColor.rgb += uColor.rgb;
    `};class Js extends ht{getCacheKey(){return"".concat(this.type,"_").concat(this.mode)}getFragmentSource(){return`
      precision highp float;
      uniform sampler2D uTexture;
      uniform vec4 uColor;
      varying vec2 vTexCoord;
      void main() {
        vec4 color = texture2D(uTexture, vTexCoord);
        gl_FragColor = color;
        if (color.a > 0.0) {
          `.concat(Gd[this.mode],`
        }
      }
      `)}applyTo2d(t){let{imageData:{data:e}}=t;const i=new N(this.color).getSource(),s=this.alpha,n=i[0]*s,o=i[1]*s,a=i[2]*s,h=1-s;for(let l=0;l<e.length;l+=4){const c=e[l],u=e[l+1],d=e[l+2];let g,f,p;switch(this.mode){case"multiply":g=c*n/255,f=u*o/255,p=d*a/255;break;case"screen":g=255-(255-c)*(255-n)/255,f=255-(255-u)*(255-o)/255,p=255-(255-d)*(255-a)/255;break;case"add":g=c+n,f=u+o,p=d+a;break;case"difference":g=Math.abs(c-n),f=Math.abs(u-o),p=Math.abs(d-a);break;case"subtract":g=c-n,f=u-o,p=d-a;break;case"darken":g=Math.min(c,n),f=Math.min(u,o),p=Math.min(d,a);break;case"lighten":g=Math.max(c,n),f=Math.max(u,o),p=Math.max(d,a);break;case"overlay":g=n<128?2*c*n/255:255-2*(255-c)*(255-n)/255,f=o<128?2*u*o/255:255-2*(255-u)*(255-o)/255,p=a<128?2*d*a/255:255-2*(255-d)*(255-a)/255;break;case"exclusion":g=n+c-2*n*c/255,f=o+u-2*o*u/255,p=a+d-2*a*d/255;break;case"tint":g=n+c*h,f=o+u*h,p=a+d*h}e[l]=g,e[l+1]=f,e[l+2]=p}}sendUniformData(t,e){const i=new N(this.color).getSource();i[0]=this.alpha*i[0]/255,i[1]=this.alpha*i[1]/255,i[2]=this.alpha*i[2]/255,i[3]=this.alpha,t.uniform4fv(e.uColor,i)}}m(Js,"defaults",{color:"#F95C63",mode:"multiply",alpha:1}),m(Js,"type","BlendColor"),m(Js,"uniformLocations",["uColor"]),D.setClass(Js);const Ud={multiply:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform sampler2D uImage;
    uniform vec4 uColor;
    varying vec2 vTexCoord;
    varying vec2 vTexCoord2;
    void main() {
      vec4 color = texture2D(uTexture, vTexCoord);
      vec4 color2 = texture2D(uImage, vTexCoord2);
      color.rgba *= color2.rgba;
      gl_FragColor = color;
    }
    `,mask:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform sampler2D uImage;
    uniform vec4 uColor;
    varying vec2 vTexCoord;
    varying vec2 vTexCoord2;
    void main() {
      vec4 color = texture2D(uTexture, vTexCoord);
      vec4 color2 = texture2D(uImage, vTexCoord2);
      color.a = color2.a;
      gl_FragColor = color;
    }
    `},qd=["type","image"];class Qs extends ht{getCacheKey(){return"".concat(this.type,"_").concat(this.mode)}getFragmentSource(){return Ud[this.mode]}getVertexSource(){return`
    attribute vec2 aPosition;
    varying vec2 vTexCoord;
    varying vec2 vTexCoord2;
    uniform mat3 uTransformMatrix;
    void main() {
      vTexCoord = aPosition;
      vTexCoord2 = (uTransformMatrix * vec3(aPosition, 1.0)).xy;
      gl_Position = vec4(aPosition * 2.0 - 1.0, 0.0, 1.0);
    }
    `}applyToWebGL(t){const e=t.context,i=this.createTexture(t.filterBackend,this.image);this.bindAdditionalTexture(e,i,e.TEXTURE1),super.applyToWebGL(t),this.unbindAdditionalTexture(e,e.TEXTURE1)}createTexture(t,e){return t.getCachedTexture(e.cacheKey,e.getElement())}calculateMatrix(){const t=this.image,{width:e,height:i}=t.getElement();return[1/t.scaleX,0,0,0,1/t.scaleY,0,-t.left/e,-t.top/i,1]}applyTo2d(t){let{imageData:{data:e,width:i,height:s},filterBackend:{resources:n}}=t;const o=this.image;n.blendImage||(n.blendImage=jt());const a=n.blendImage,h=a.getContext("2d");a.width!==i||a.height!==s?(a.width=i,a.height=s):h.clearRect(0,0,i,s),h.setTransform(o.scaleX,0,0,o.scaleY,o.left,o.top),h.drawImage(o.getElement(),0,0,i,s);const l=h.getImageData(0,0,i,s).data;for(let c=0;c<e.length;c+=4){const u=e[c],d=e[c+1],g=e[c+2],f=e[c+3],p=l[c],v=l[c+1],b=l[c+2],w=l[c+3];switch(this.mode){case"multiply":e[c]=u*p/255,e[c+1]=d*v/255,e[c+2]=g*b/255,e[c+3]=f*w/255;break;case"mask":e[c+3]=w}}}sendUniformData(t,e){const i=this.calculateMatrix();t.uniform1i(e.uImage,1),t.uniformMatrix3fv(e.uTransformMatrix,!1,i)}toObject(){return y(y({},super.toObject()),{},{image:this.image&&this.image.toObject()})}static async fromObject(t,e){let{type:i,image:s}=t,n=U(t,qd);return Lt.fromObject(s,e).then(o=>new this(y(y({},n),{},{image:o})))}}m(Qs,"type","BlendImage"),m(Qs,"defaults",{mode:"multiply",alpha:1}),m(Qs,"uniformLocations",["uTransformMatrix","uImage"]),D.setClass(Qs);class tn extends ht{getFragmentSource(){return`
    precision highp float;
    uniform sampler2D uTexture;
    uniform vec2 uDelta;
    varying vec2 vTexCoord;
    const float nSamples = 15.0;
    vec3 v3offset = vec3(12.9898, 78.233, 151.7182);
    float random(vec3 scale) {
      /* use the fragment position for a different seed per-pixel */
      return fract(sin(dot(gl_FragCoord.xyz, scale)) * 43758.5453);
    }
    void main() {
      vec4 color = vec4(0.0);
      float total = 0.0;
      float offset = random(v3offset);
      for (float t = -nSamples; t <= nSamples; t++) {
        float percent = (t + offset - 0.5) / nSamples;
        float weight = 1.0 - abs(percent);
        color += texture2D(uTexture, vTexCoord + uDelta * percent) * weight;
        total += weight;
      }
      gl_FragColor = color / total;
    }
  `}applyTo(t){Zs(t)?(this.aspectRatio=t.sourceWidth/t.sourceHeight,t.passes++,this._setupFrameBuffer(t),this.horizontal=!0,this.applyToWebGL(t),this._swapTextures(t),this._setupFrameBuffer(t),this.horizontal=!1,this.applyToWebGL(t),this._swapTextures(t)):this.applyTo2d(t)}applyTo2d(t){t.imageData=this.simpleBlur(t)}simpleBlur(t){let{ctx:e,imageData:i,filterBackend:{resources:s}}=t;const{width:n,height:o}=i;s.blurLayer1||(s.blurLayer1=jt(),s.blurLayer2=jt());const a=s.blurLayer1,h=s.blurLayer2;a.width===n&&a.height===o||(h.width=a.width=n,h.height=a.height=o);const l=a.getContext("2d"),c=h.getContext("2d"),u=15,d=.06*this.blur*.5;let g,f,p,v;for(l.putImageData(i,0,0),c.clearRect(0,0,n,o),v=-15;v<=u;v++)g=(Math.random()-.5)/4,f=v/u,p=d*f*n+g,c.globalAlpha=1-Math.abs(f),c.drawImage(a,p,g),l.drawImage(h,0,0),c.globalAlpha=1,c.clearRect(0,0,h.width,h.height);for(v=-15;v<=u;v++)g=(Math.random()-.5)/4,f=v/u,p=d*f*o+g,c.globalAlpha=1-Math.abs(f),c.drawImage(a,g,p),l.drawImage(h,0,0),c.globalAlpha=1,c.clearRect(0,0,h.width,h.height);e.drawImage(a,0,0);const b=e.getImageData(0,0,a.width,a.height);return l.globalAlpha=1,l.clearRect(0,0,a.width,a.height),b}sendUniformData(t,e){const i=this.chooseRightDelta();t.uniform2fv(e.uDelta,i)}isNeutralState(){return this.blur===0}chooseRightDelta(){let t=1;const e=[0,0];this.horizontal?this.aspectRatio>1&&(t=1/this.aspectRatio):this.aspectRatio<1&&(t=this.aspectRatio);const i=t*this.blur*.12;return this.horizontal?e[0]=i:e[1]=i,e}}m(tn,"type","Blur"),m(tn,"defaults",{blur:0}),m(tn,"uniformLocations",["uDelta"]),D.setClass(tn);class en extends ht{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform float uBrightness;
  varying vec2 vTexCoord;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    color.rgb += uBrightness;
    gl_FragColor = color;
  }
`}applyTo2d(t){let{imageData:{data:e}}=t;const i=Math.round(255*this.brightness);for(let s=0;s<e.length;s+=4)e[s]+=i,e[s+1]+=i,e[s+2]+=i}isNeutralState(){return this.brightness===0}sendUniformData(t,e){t.uniform1f(e.uBrightness,this.brightness)}}m(en,"type","Brightness"),m(en,"defaults",{brightness:0}),m(en,"uniformLocations",["uBrightness"]),D.setClass(en);const Jh={matrix:[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],colorsOnly:!0};class Ei extends ht{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  varying vec2 vTexCoord;
  uniform mat4 uColorMatrix;
  uniform vec4 uConstants;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    color *= uColorMatrix;
    color += uConstants;
    gl_FragColor = color;
  }`}applyTo2d(t){const e=t.imageData.data,i=this.matrix,s=this.colorsOnly;for(let n=0;n<e.length;n+=4){const o=e[n],a=e[n+1],h=e[n+2];if(e[n]=o*i[0]+a*i[1]+h*i[2]+255*i[4],e[n+1]=o*i[5]+a*i[6]+h*i[7]+255*i[9],e[n+2]=o*i[10]+a*i[11]+h*i[12]+255*i[14],!s){const l=e[n+3];e[n]+=l*i[3],e[n+1]+=l*i[8],e[n+2]+=l*i[13],e[n+3]=o*i[15]+a*i[16]+h*i[17]+l*i[18]+255*i[19]}}}sendUniformData(t,e){const i=this.matrix,s=[i[0],i[1],i[2],i[3],i[5],i[6],i[7],i[8],i[10],i[11],i[12],i[13],i[15],i[16],i[17],i[18]],n=[i[4],i[9],i[14],i[19]];t.uniformMatrix4fv(e.uColorMatrix,!1,s),t.uniform4fv(e.uConstants,n)}toObject(){return y(y({},super.toObject()),{},{matrix:[...this.matrix]})}}function Ue(r,t){var e;const i=(m(e=class extends Ei{toObject(){return{type:this.type,colorsOnly:this.colorsOnly}}},"type",r),m(e,"defaults",{colorsOnly:!1,matrix:t}),e);return D.setClass(i,r),i}m(Ei,"type","ColorMatrix"),m(Ei,"defaults",Jh),m(Ei,"uniformLocations",["uColorMatrix","uConstants"]),D.setClass(Ei),Ue("Brownie",[.5997,.34553,-.27082,0,.186,-.0377,.86095,.15059,0,-.1449,.24113,-.07441,.44972,0,-.02965,0,0,0,1,0]),Ue("Vintage",[.62793,.32021,-.03965,0,.03784,.02578,.64411,.03259,0,.02926,.0466,-.08512,.52416,0,.02023,0,0,0,1,0]),Ue("Kodachrome",[1.12855,-.39673,-.03992,0,.24991,-.16404,1.08352,-.05498,0,.09698,-.16786,-.56034,1.60148,0,.13972,0,0,0,1,0]),Ue("Technicolor",[1.91252,-.85453,-.09155,0,.04624,-.30878,1.76589,-.10601,0,-.27589,-.2311,-.75018,1.84759,0,.12137,0,0,0,1,0]),Ue("Polaroid",[1.438,-.062,-.062,0,0,-.122,1.378,-.122,0,0,-.016,-.016,1.483,0,0,0,0,0,1,0]),Ue("Sepia",[.393,.769,.189,0,0,.349,.686,.168,0,0,.272,.534,.131,0,0,0,0,0,1,0]),Ue("BlackWhite",[1.5,1.5,1.5,0,-1,1.5,1.5,1.5,0,-1,1.5,1.5,1.5,0,-1,0,0,0,1,0]);class Qh extends ht{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};super(t),this.subFilters=t.subFilters||[]}applyTo(t){Zs(t)&&(t.passes+=this.subFilters.length-1),this.subFilters.forEach(e=>{e.applyTo(t)})}toObject(){return{type:this.type,subFilters:this.subFilters.map(t=>t.toObject())}}isNeutralState(){return!this.subFilters.some(t=>!t.isNeutralState())}static fromObject(t,e){return Promise.all((t.subFilters||[]).map(i=>D.getClass(i.type).fromObject(i,e))).then(i=>new this({subFilters:i}))}}m(Qh,"type","Composed"),D.setClass(Qh);class sn extends ht{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform float uContrast;
  varying vec2 vTexCoord;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    float contrastF = 1.015 * (uContrast + 1.0) / (1.0 * (1.015 - uContrast));
    color.rgb = contrastF * (color.rgb - 0.5) + 0.5;
    gl_FragColor = color;
  }`}isNeutralState(){return this.contrast===0}applyTo2d(t){let{imageData:{data:e}}=t;const i=Math.floor(255*this.contrast),s=259*(i+255)/(255*(259-i));for(let n=0;n<e.length;n+=4)e[n]=s*(e[n]-128)+128,e[n+1]=s*(e[n+1]-128)+128,e[n+2]=s*(e[n+2]-128)+128}sendUniformData(t,e){t.uniform1f(e.uContrast,this.contrast)}}m(sn,"type","Contrast"),m(sn,"defaults",{contrast:0}),m(sn,"uniformLocations",["uContrast"]),D.setClass(sn);const Kd={Convolute_3_1:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[9];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 0);
      for (float h = 0.0; h < 3.0; h+=1.0) {
        for (float w = 0.0; w < 3.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 1), uStepH * (h - 1));
          color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 3.0 + w)];
        }
      }
      gl_FragColor = color;
    }
    `,Convolute_3_0:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[9];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 1);
      for (float h = 0.0; h < 3.0; h+=1.0) {
        for (float w = 0.0; w < 3.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 1.0), uStepH * (h - 1.0));
          color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 3.0 + w)];
        }
      }
      float alpha = texture2D(uTexture, vTexCoord).a;
      gl_FragColor = color;
      gl_FragColor.a = alpha;
    }
    `,Convolute_5_1:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[25];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 0);
      for (float h = 0.0; h < 5.0; h+=1.0) {
        for (float w = 0.0; w < 5.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 2.0), uStepH * (h - 2.0));
          color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 5.0 + w)];
        }
      }
      gl_FragColor = color;
    }
    `,Convolute_5_0:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[25];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 1);
      for (float h = 0.0; h < 5.0; h+=1.0) {
        for (float w = 0.0; w < 5.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 2.0), uStepH * (h - 2.0));
          color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 5.0 + w)];
        }
      }
      float alpha = texture2D(uTexture, vTexCoord).a;
      gl_FragColor = color;
      gl_FragColor.a = alpha;
    }
    `,Convolute_7_1:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[49];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 0);
      for (float h = 0.0; h < 7.0; h+=1.0) {
        for (float w = 0.0; w < 7.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 3.0), uStepH * (h - 3.0));
          color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 7.0 + w)];
        }
      }
      gl_FragColor = color;
    }
    `,Convolute_7_0:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[49];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 1);
      for (float h = 0.0; h < 7.0; h+=1.0) {
        for (float w = 0.0; w < 7.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 3.0), uStepH * (h - 3.0));
          color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 7.0 + w)];
        }
      }
      float alpha = texture2D(uTexture, vTexCoord).a;
      gl_FragColor = color;
      gl_FragColor.a = alpha;
    }
    `,Convolute_9_1:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[81];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 0);
      for (float h = 0.0; h < 9.0; h+=1.0) {
        for (float w = 0.0; w < 9.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 4.0), uStepH * (h - 4.0));
          color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 9.0 + w)];
        }
      }
      gl_FragColor = color;
    }
    `,Convolute_9_0:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[81];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 1);
      for (float h = 0.0; h < 9.0; h+=1.0) {
        for (float w = 0.0; w < 9.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 4.0), uStepH * (h - 4.0));
          color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 9.0 + w)];
        }
      }
      float alpha = texture2D(uTexture, vTexCoord).a;
      gl_FragColor = color;
      gl_FragColor.a = alpha;
    }
    `};class nn extends ht{getCacheKey(){return"".concat(this.type,"_").concat(Math.sqrt(this.matrix.length),"_").concat(this.opaque?1:0)}getFragmentSource(){return Kd[this.getCacheKey()]}applyTo2d(t){const e=t.imageData,i=e.data,s=this.matrix,n=Math.round(Math.sqrt(s.length)),o=Math.floor(n/2),a=e.width,h=e.height,l=t.ctx.createImageData(a,h),c=l.data,u=this.opaque?1:0;let d,g,f,p,v,b,w,x,_,k,A,L,O;for(A=0;A<h;A++)for(k=0;k<a;k++){for(v=4*(A*a+k),d=0,g=0,f=0,p=0,O=0;O<n;O++)for(L=0;L<n;L++)w=A+O-o,b=k+L-o,w<0||w>=h||b<0||b>=a||(x=4*(w*a+b),_=s[O*n+L],d+=i[x]*_,g+=i[x+1]*_,f+=i[x+2]*_,u||(p+=i[x+3]*_));c[v]=d,c[v+1]=g,c[v+2]=f,c[v+3]=u?i[v+3]:p}t.imageData=l}sendUniformData(t,e){t.uniform1fv(e.uMatrix,this.matrix)}toObject(){return y(y({},super.toObject()),{},{opaque:this.opaque,matrix:[...this.matrix]})}}m(nn,"type","Convolute"),m(nn,"defaults",{opaque:!1,matrix:[0,0,0,0,1,0,0,0,0]}),m(nn,"uniformLocations",["uMatrix","uOpaque","uHalfSize","uSize"]),D.setClass(nn);const tl="Gamma";class rn extends ht{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform vec3 uGamma;
  varying vec2 vTexCoord;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    vec3 correction = (1.0 / uGamma);
    color.r = pow(color.r, correction.r);
    color.g = pow(color.g, correction.g);
    color.b = pow(color.b, correction.b);
    gl_FragColor = color;
    gl_FragColor.rgb *= color.a;
  }
`}constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};super(t),this.gamma=t.gamma||this.constructor.defaults.gamma.concat()}applyTo2d(t){let{imageData:{data:e}}=t;const i=this.gamma,s=1/i[0],n=1/i[1],o=1/i[2];this.rgbValues||(this.rgbValues={r:new Uint8Array(256),g:new Uint8Array(256),b:new Uint8Array(256)});const a=this.rgbValues;for(let h=0;h<256;h++)a.r[h]=255*Math.pow(h/255,s),a.g[h]=255*Math.pow(h/255,n),a.b[h]=255*Math.pow(h/255,o);for(let h=0;h<e.length;h+=4)e[h]=a.r[e[h]],e[h+1]=a.g[e[h+1]],e[h+2]=a.b[e[h+2]]}sendUniformData(t,e){t.uniform3fv(e.uGamma,this.gamma)}isNeutralState(){const{gamma:t}=this;return t[0]===1&&t[1]===1&&t[2]===1}toObject(){return{type:tl,gamma:this.gamma.concat()}}}m(rn,"type",tl),m(rn,"defaults",{gamma:[1,1,1]}),m(rn,"uniformLocations",["uGamma"]),D.setClass(rn);const Zd={average:`
    precision highp float;
    uniform sampler2D uTexture;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = texture2D(uTexture, vTexCoord);
      float average = (color.r + color.b + color.g) / 3.0;
      gl_FragColor = vec4(average, average, average, color.a);
    }
    `,lightness:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform int uMode;
    varying vec2 vTexCoord;
    void main() {
      vec4 col = texture2D(uTexture, vTexCoord);
      float average = (max(max(col.r, col.g),col.b) + min(min(col.r, col.g),col.b)) / 2.0;
      gl_FragColor = vec4(average, average, average, col.a);
    }
    `,luminosity:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform int uMode;
    varying vec2 vTexCoord;
    void main() {
      vec4 col = texture2D(uTexture, vTexCoord);
      float average = 0.21 * col.r + 0.72 * col.g + 0.07 * col.b;
      gl_FragColor = vec4(average, average, average, col.a);
    }
    `};class on extends ht{applyTo2d(t){let{imageData:{data:e}}=t;for(let i,s=0;s<e.length;s+=4){const n=e[s],o=e[s+1],a=e[s+2];switch(this.mode){case"average":i=(n+o+a)/3;break;case"lightness":i=(Math.min(n,o,a)+Math.max(n,o,a))/2;break;case"luminosity":i=.21*n+.72*o+.07*a}e[s+2]=e[s+1]=e[s]=i}}getCacheKey(){return"".concat(this.type,"_").concat(this.mode)}getFragmentSource(){return Zd[this.mode]}sendUniformData(t,e){t.uniform1i(e.uMode,1)}isNeutralState(){return!1}}m(on,"type","Grayscale"),m(on,"defaults",{mode:"average"}),m(on,"uniformLocations",["uMode"]),D.setClass(on);const Jd=y(y({},Jh),{},{rotation:0});class Xr extends Ei{calculateMatrix(){const t=this.rotation*Math.PI,e=Vt(t),i=Xt(t),s=1/3,n=Math.sqrt(s)*i,o=1-e;this.matrix=[e+o/3,s*o-n,s*o+n,0,0,s*o+n,e+s*o,s*o-n,0,0,s*o-n,s*o+n,e+s*o,0,0,0,0,0,1,0]}isNeutralState(){return this.rotation===0}applyTo(t){this.calculateMatrix(),super.applyTo(t)}toObject(){return{type:this.type,rotation:this.rotation}}}m(Xr,"type","HueRotation"),m(Xr,"defaults",Jd),D.setClass(Xr);class an extends ht{applyTo2d(t){let{imageData:{data:e}}=t;for(let i=0;i<e.length;i+=4)e[i]=255-e[i],e[i+1]=255-e[i+1],e[i+2]=255-e[i+2],this.alpha&&(e[i+3]=255-e[i+3])}getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform int uInvert;
  uniform int uAlpha;
  varying vec2 vTexCoord;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    if (uInvert == 1) {
      if (uAlpha == 1) {
        gl_FragColor = vec4(1.0 - color.r,1.0 -color.g,1.0 -color.b,1.0 -color.a);
      } else {
        gl_FragColor = vec4(1.0 - color.r,1.0 -color.g,1.0 -color.b,color.a);
      }
    } else {
      gl_FragColor = color;
    }
  }
`}isNeutralState(){return!this.invert}sendUniformData(t,e){t.uniform1i(e.uInvert,Number(this.invert)),t.uniform1i(e.uAlpha,Number(this.alpha))}}m(an,"type","Invert"),m(an,"defaults",{alpha:!1,invert:!0}),m(an,"uniformLocations",["uInvert","uAlpha"]),D.setClass(an);class hn extends ht{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform float uStepH;
  uniform float uNoise;
  uniform float uSeed;
  varying vec2 vTexCoord;
  float rand(vec2 co, float seed, float vScale) {
    return fract(sin(dot(co.xy * vScale ,vec2(12.9898 , 78.233))) * 43758.5453 * (seed + 0.01) / 2.0);
  }
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    color.rgb += (0.5 - rand(vTexCoord, uSeed, 0.1 / uStepH)) * uNoise;
    gl_FragColor = color;
  }
`}applyTo2d(t){let{imageData:{data:e}}=t;const i=this.noise;for(let s=0;s<e.length;s+=4){const n=(.5-Math.random())*i;e[s]+=n,e[s+1]+=n,e[s+2]+=n}}sendUniformData(t,e){t.uniform1f(e.uNoise,this.noise/255),t.uniform1f(e.uSeed,Math.random())}isNeutralState(){return this.noise===0}}m(hn,"type","Noise"),m(hn,"defaults",{noise:0}),m(hn,"uniformLocations",["uNoise","uSeed"]),D.setClass(hn);class ln extends ht{applyTo2d(t){let{imageData:{data:e,width:i,height:s}}=t;for(let n=0;n<s;n+=this.blocksize)for(let o=0;o<i;o+=this.blocksize){const a=4*n*i+4*o,h=e[a],l=e[a+1],c=e[a+2],u=e[a+3];for(let d=n;d<Math.min(n+this.blocksize,s);d++)for(let g=o;g<Math.min(o+this.blocksize,i);g++){const f=4*d*i+4*g;e[f]=h,e[f+1]=l,e[f+2]=c,e[f+3]=u}}}isNeutralState(){return this.blocksize===1}getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform float uBlocksize;
  uniform float uStepW;
  uniform float uStepH;
  varying vec2 vTexCoord;
  void main() {
    float blockW = uBlocksize * uStepW;
    float blockH = uBlocksize * uStepH;
    int posX = int(vTexCoord.x / blockW);
    int posY = int(vTexCoord.y / blockH);
    float fposX = float(posX);
    float fposY = float(posY);
    vec2 squareCoords = vec2(fposX * blockW, fposY * blockH);
    vec4 color = texture2D(uTexture, squareCoords);
    gl_FragColor = color;
  }
`}sendUniformData(t,e){t.uniform1f(e.uBlocksize,this.blocksize)}}m(ln,"type","Pixelate"),m(ln,"defaults",{blocksize:4}),m(ln,"uniformLocations",["uBlocksize"]),D.setClass(ln);class cn extends ht{getFragmentSource(){return`
precision highp float;
uniform sampler2D uTexture;
uniform vec4 uLow;
uniform vec4 uHigh;
varying vec2 vTexCoord;
void main() {
  gl_FragColor = texture2D(uTexture, vTexCoord);
  if(all(greaterThan(gl_FragColor.rgb,uLow.rgb)) && all(greaterThan(uHigh.rgb,gl_FragColor.rgb))) {
    gl_FragColor.a = 0.0;
  }
}
`}applyTo2d(t){let{imageData:{data:e}}=t;const i=255*this.distance,s=new N(this.color).getSource(),n=[s[0]-i,s[1]-i,s[2]-i],o=[s[0]+i,s[1]+i,s[2]+i];for(let a=0;a<e.length;a+=4){const h=e[a],l=e[a+1],c=e[a+2];h>n[0]&&l>n[1]&&c>n[2]&&h<o[0]&&l<o[1]&&c<o[2]&&(e[a+3]=0)}}sendUniformData(t,e){const i=new N(this.color).getSource(),s=this.distance,n=[0+i[0]/255-s,0+i[1]/255-s,0+i[2]/255-s,1],o=[i[0]/255+s,i[1]/255+s,i[2]/255+s,1];t.uniform4fv(e.uLow,n),t.uniform4fv(e.uHigh,o)}}m(cn,"type","RemoveColor"),m(cn,"defaults",{color:"#FFFFFF",distance:.02,useAlpha:!1}),m(cn,"uniformLocations",["uLow","uHigh"]),D.setClass(cn);class un extends ht{sendUniformData(t,e){t.uniform2fv(e.uDelta,this.horizontal?[1/this.width,0]:[0,1/this.height]),t.uniform1fv(e.uTaps,this.taps)}getFilterWindow(){const t=this.tempScale;return Math.ceil(this.lanczosLobes/t)}getCacheKey(){const t=this.getFilterWindow();return"".concat(this.type,"_").concat(t)}getFragmentSource(){const t=this.getFilterWindow();return this.generateShader(t)}getTaps(){const t=this.lanczosCreate(this.lanczosLobes),e=this.tempScale,i=this.getFilterWindow(),s=new Array(i);for(let n=1;n<=i;n++)s[n-1]=t(n*e);return s}generateShader(t){const e=new Array(t);for(let i=1;i<=t;i++)e[i-1]="".concat(i,".0 * uDelta");return`
      precision highp float;
      uniform sampler2D uTexture;
      uniform vec2 uDelta;
      varying vec2 vTexCoord;
      uniform float uTaps[`.concat(t,`];
      void main() {
        vec4 color = texture2D(uTexture, vTexCoord);
        float sum = 1.0;
        `).concat(e.map((i,s)=>`
              color += texture2D(uTexture, vTexCoord + `.concat(i,") * uTaps[").concat(s,"] + texture2D(uTexture, vTexCoord - ").concat(i,") * uTaps[").concat(s,`];
              sum += 2.0 * uTaps[`).concat(s,`];
            `)).join(`
`),`
        gl_FragColor = color / sum;
      }
    `)}applyToForWebgl(t){t.passes++,this.width=t.sourceWidth,this.horizontal=!0,this.dW=Math.round(this.width*this.scaleX),this.dH=t.sourceHeight,this.tempScale=this.dW/this.width,this.taps=this.getTaps(),t.destinationWidth=this.dW,super.applyTo(t),t.sourceWidth=t.destinationWidth,this.height=t.sourceHeight,this.horizontal=!1,this.dH=Math.round(this.height*this.scaleY),this.tempScale=this.dH/this.height,this.taps=this.getTaps(),t.destinationHeight=this.dH,super.applyTo(t),t.sourceHeight=t.destinationHeight}applyTo(t){Zs(t)?this.applyToForWebgl(t):this.applyTo2d(t)}isNeutralState(){return this.scaleX===1&&this.scaleY===1}lanczosCreate(t){return e=>{if(e>=t||e<=-t)return 0;if(e<11920929e-14&&e>-11920929e-14)return 1;const i=(e*=Math.PI)/t;return Math.sin(e)/e*Math.sin(i)/i}}applyTo2d(t){const e=t.imageData,i=this.scaleX,s=this.scaleY;this.rcpScaleX=1/i,this.rcpScaleY=1/s;const n=e.width,o=e.height,a=Math.round(n*i),h=Math.round(o*s);let l;l=this.resizeType==="sliceHack"?this.sliceByTwo(t,n,o,a,h):this.resizeType==="hermite"?this.hermiteFastResize(t,n,o,a,h):this.resizeType==="bilinear"?this.bilinearFiltering(t,n,o,a,h):this.resizeType==="lanczos"?this.lanczosResize(t,n,o,a,h):new ImageData(a,h),t.imageData=l}sliceByTwo(t,e,i,s,n){const o=t.imageData,a=.5;let h=!1,l=!1,c=e*a,u=i*a;const d=t.filterBackend.resources;let g=0,f=0;const p=e;let v=0;d.sliceByTwo||(d.sliceByTwo=jt());const b=d.sliceByTwo;(b.width<1.5*e||b.height<i)&&(b.width=1.5*e,b.height=i);const w=b.getContext("2d");for(w.clearRect(0,0,1.5*e,i),w.putImageData(o,0,0),s=Math.floor(s),n=Math.floor(n);!h||!l;)e=c,i=u,s<Math.floor(c*a)?c=Math.floor(c*a):(c=s,h=!0),n<Math.floor(u*a)?u=Math.floor(u*a):(u=n,l=!0),w.drawImage(b,g,f,e,i,p,v,c,u),g=p,f=v,v+=u;return w.getImageData(g,f,s,n)}lanczosResize(t,e,i,s,n){const o=t.imageData.data,a=t.ctx.createImageData(s,n),h=a.data,l=this.lanczosCreate(this.lanczosLobes),c=this.rcpScaleX,u=this.rcpScaleY,d=2/this.rcpScaleX,g=2/this.rcpScaleY,f=Math.ceil(c*this.lanczosLobes/2),p=Math.ceil(u*this.lanczosLobes/2),v={},b={x:0,y:0},w={x:0,y:0};return function x(_){let k,A,L,O,I,F,tt,Z,z,Y,ct;for(b.x=(_+.5)*c,w.x=Math.floor(b.x),k=0;k<n;k++){for(b.y=(k+.5)*u,w.y=Math.floor(b.y),I=0,F=0,tt=0,Z=0,z=0,A=w.x-f;A<=w.x+f;A++)if(!(A<0||A>=e)){Y=Math.floor(1e3*Math.abs(A-b.x)),v[Y]||(v[Y]={});for(let S=w.y-p;S<=w.y+p;S++)S<0||S>=i||(ct=Math.floor(1e3*Math.abs(S-b.y)),v[Y][ct]||(v[Y][ct]=l(Math.sqrt(Math.pow(Y*d,2)+Math.pow(ct*g,2))/1e3)),L=v[Y][ct],L>0&&(O=4*(S*e+A),I+=L,F+=L*o[O],tt+=L*o[O+1],Z+=L*o[O+2],z+=L*o[O+3]))}O=4*(k*s+_),h[O]=F/I,h[O+1]=tt/I,h[O+2]=Z/I,h[O+3]=z/I}return++_<s?x(_):a}(0)}bilinearFiltering(t,e,i,s,n){let o,a,h,l,c,u,d,g,f,p,v,b,w,x=0;const _=this.rcpScaleX,k=this.rcpScaleY,A=4*(e-1),L=t.imageData.data,O=t.ctx.createImageData(s,n),I=O.data;for(d=0;d<n;d++)for(g=0;g<s;g++)for(c=Math.floor(_*g),u=Math.floor(k*d),f=_*g-c,p=k*d-u,w=4*(u*e+c),v=0;v<4;v++)o=L[w+v],a=L[w+4+v],h=L[w+A+v],l=L[w+A+4+v],b=o*(1-f)*(1-p)+a*f*(1-p)+h*p*(1-f)+l*f*p,I[x++]=b;return O}hermiteFastResize(t,e,i,s,n){const o=this.rcpScaleX,a=this.rcpScaleY,h=Math.ceil(o/2),l=Math.ceil(a/2),c=t.imageData.data,u=t.ctx.createImageData(s,n),d=u.data;for(let g=0;g<n;g++)for(let f=0;f<s;f++){const p=4*(f+g*s);let v=0,b=0,w=0,x=0,_=0,k=0,A=0;const L=(g+.5)*a;for(let O=Math.floor(g*a);O<(g+1)*a;O++){const I=Math.abs(L-(O+.5))/l,F=(f+.5)*o,tt=I*I;for(let Z=Math.floor(f*o);Z<(f+1)*o;Z++){let z=Math.abs(F-(Z+.5))/h;const Y=Math.sqrt(tt+z*z);Y>1&&Y<-1||(v=2*Y*Y*Y-3*Y*Y+1,v>0&&(z=4*(Z+O*e),A+=v*c[z+3],w+=v,c[z+3]<255&&(v=v*c[z+3]/250),x+=v*c[z],_+=v*c[z+1],k+=v*c[z+2],b+=v))}}d[p]=x/b,d[p+1]=_/b,d[p+2]=k/b,d[p+3]=A/w}return u}}m(un,"type","Resize"),m(un,"defaults",{resizeType:"hermite",scaleX:1,scaleY:1,lanczosLobes:3}),m(un,"uniformLocations",["uDelta","uTaps"]),D.setClass(un);class dn extends ht{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform float uSaturation;
  varying vec2 vTexCoord;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    float rgMax = max(color.r, color.g);
    float rgbMax = max(rgMax, color.b);
    color.r += rgbMax != color.r ? (rgbMax - color.r) * uSaturation : 0.00;
    color.g += rgbMax != color.g ? (rgbMax - color.g) * uSaturation : 0.00;
    color.b += rgbMax != color.b ? (rgbMax - color.b) * uSaturation : 0.00;
    gl_FragColor = color;
  }
`}applyTo2d(t){let{imageData:{data:e}}=t;const i=-this.saturation;for(let s=0;s<e.length;s+=4){const n=e[s],o=e[s+1],a=e[s+2],h=Math.max(n,o,a);e[s]+=h!==n?(h-n)*i:0,e[s+1]+=h!==o?(h-o)*i:0,e[s+2]+=h!==a?(h-a)*i:0}}sendUniformData(t,e){t.uniform1f(e.uSaturation,-this.saturation)}isNeutralState(){return this.saturation===0}}m(dn,"type","Saturation"),m(dn,"defaults",{saturation:0}),m(dn,"uniformLocations",["uSaturation"]),D.setClass(dn);class gn extends ht{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform float uVibrance;
  varying vec2 vTexCoord;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    float max = max(color.r, max(color.g, color.b));
    float avg = (color.r + color.g + color.b) / 3.0;
    float amt = (abs(max - avg) * 2.0) * uVibrance;
    color.r += max != color.r ? (max - color.r) * amt : 0.00;
    color.g += max != color.g ? (max - color.g) * amt : 0.00;
    color.b += max != color.b ? (max - color.b) * amt : 0.00;
    gl_FragColor = color;
  }
`}applyTo2d(t){let{imageData:{data:e}}=t;const i=-this.vibrance;for(let s=0;s<e.length;s+=4){const n=e[s],o=e[s+1],a=e[s+2],h=Math.max(n,o,a),l=(n+o+a)/3,c=2*Math.abs(h-l)/255*i;e[s]+=h!==n?(h-n)*c:0,e[s+1]+=h!==o?(h-o)*c:0,e[s+2]+=h!==a?(h-a)*c:0}}sendUniformData(t,e){t.uniform1f(e.uVibrance,-this.vibrance)}isNeutralState(){return this.vibrance===0}}m(gn,"type","Vibrance"),m(gn,"defaults",{vibrance:0}),m(gn,"uniformLocations",["uVibrance"]),D.setClass(gn);const Mn=class Mn{constructor(){M(this,"commands",[]);M(this,"index",-1);M(this,"limit",0);M(this,"isExecuting",!1);M(this,"callback")}static getInstance(){return this.instance||(this.instance=new Mn),this.instance}removeFromTo(t,e,i){return i===void 0?t.splice(e,1):t.splice(e,i-e+1+(i<0!=e>=0?0:(i<0?-1:0)*t.length)),t.length}execute(t,e){return!t||typeof t[e]!="function"?this:(this.isExecuting=!0,t[e](),this.isExecuting=!1,this)}add(t){return this.isExecuting?this:(this.commands.splice(this.index+1,this.commands.length-this.index),this.commands.push(t),this.limit&&this.commands.length>this.limit&&this.removeFromTo(this.commands,0,-(this.limit+1)),this.index=this.commands.length-1,this.callback&&this.callback(),this)}setCallback(t){this.callback=t}undo(){let t=this.commands[this.index];if(!t)return this;const e=t.groupId;for(;t.groupId===e&&(this.execute(t,"undo"),this.index-=1,t=this.commands[this.index],!(!t||!t.groupId)););return this.callback&&this.callback(),this}redo(){let t=this.commands[this.index+1];if(!t)return this;const e=t.groupId;for(;t.groupId===e&&(this.execute(t,"redo"),this.index+=1,t=this.commands[this.index+1],!(!t||!t.groupId)););return this.callback&&this.callback(),this}clear(){const t=this.commands.length;this.commands=[],this.index=-1,this.callback&&t>0&&this.callback()}hasUndo(){return this.index!==-1}hasRedo(){return this.index<this.commands.length-1}getCommands(t){return t?this.commands.filter(e=>e.groupId===t):this.commands}getIndex(){return this.index}setLimit(t){this.limit=t}};M(Mn,"instance",null);let fn=Mn;function el(r,t,e){return t=zt(t),sg(r,il()?Reflect.construct(t,e||[],zt(r).constructor):t.apply(r,e))}function il(){try{var r=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(il=function(){return!!r})()}function Qd(r,t){var e=r==null?null:typeof Symbol<"u"&&r[Symbol.iterator]||r["@@iterator"];if(e!=null){var i,s,n,o,a=[],h=!0,l=!1;try{if(n=(e=e.call(r)).next,t===0){if(Object(e)!==e)return;h=!1}else for(;!(h=(i=n.call(e)).done)&&(a.push(i.value),a.length!==t);h=!0);}catch(c){l=!0,s=c}finally{try{if(!h&&e.return!=null&&(o=e.return(),Object(o)!==o))return}finally{if(l)throw s}}return a}}function sl(r,t){var e=Object.keys(r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(r);t&&(i=i.filter(function(s){return Object.getOwnPropertyDescriptor(r,s).enumerable})),e.push.apply(e,i)}return e}function tg(r){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?sl(Object(e),!0).forEach(function(i){Qi(r,i,e[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(e)):sl(Object(e)).forEach(function(i){Object.defineProperty(r,i,Object.getOwnPropertyDescriptor(e,i))})}return r}function Zt(){Zt=function(){return t};var r,t={},e=Object.prototype,i=e.hasOwnProperty,s=Object.defineProperty||function(S,T,E){S[T]=E.value},n=typeof Symbol=="function"?Symbol:{},o=n.iterator||"@@iterator",a=n.asyncIterator||"@@asyncIterator",h=n.toStringTag||"@@toStringTag";function l(S,T,E){return Object.defineProperty(S,T,{value:E,enumerable:!0,configurable:!0,writable:!0}),S[T]}try{l({},"")}catch{l=function(T,E,R){return T[E]=R}}function c(S,T,E,R){var P=T&&T.prototype instanceof b?T:b,H=Object.create(P.prototype),J=new Y(R||[]);return s(H,"_invoke",{value:F(S,E,J)}),H}function u(S,T,E){try{return{type:"normal",arg:S.call(T,E)}}catch(R){return{type:"throw",arg:R}}}t.wrap=c;var d="suspendedStart",g="suspendedYield",f="executing",p="completed",v={};function b(){}function w(){}function x(){}var _={};l(_,o,function(){return this});var k=Object.getPrototypeOf,A=k&&k(k(ct([])));A&&A!==e&&i.call(A,o)&&(_=A);var L=x.prototype=b.prototype=Object.create(_);function O(S){["next","throw","return"].forEach(function(T){l(S,T,function(E){return this._invoke(T,E)})})}function I(S,T){function E(P,H,J,lt){var et=u(S[P],S,H);if(et.type!=="throw"){var Jt=et.arg,Rt=Jt.value;return Rt&&typeof Rt=="object"&&i.call(Rt,"__await")?T.resolve(Rt.__await).then(function(It){E("next",It,J,lt)},function(It){E("throw",It,J,lt)}):T.resolve(Rt).then(function(It){Jt.value=It,J(Jt)},function(It){return E("throw",It,J,lt)})}lt(et.arg)}var R;s(this,"_invoke",{value:function(P,H){function J(){return new T(function(lt,et){E(P,H,lt,et)})}return R=R?R.then(J,J):J()}})}function F(S,T,E){var R=d;return function(P,H){if(R===f)throw new Error("Generator is already running");if(R===p){if(P==="throw")throw H;return{value:r,done:!0}}for(E.method=P,E.arg=H;;){var J=E.delegate;if(J){var lt=tt(J,E);if(lt){if(lt===v)continue;return lt}}if(E.method==="next")E.sent=E._sent=E.arg;else if(E.method==="throw"){if(R===d)throw R=p,E.arg;E.dispatchException(E.arg)}else E.method==="return"&&E.abrupt("return",E.arg);R=f;var et=u(S,T,E);if(et.type==="normal"){if(R=E.done?p:g,et.arg===v)continue;return{value:et.arg,done:E.done}}et.type==="throw"&&(R=p,E.method="throw",E.arg=et.arg)}}}function tt(S,T){var E=T.method,R=S.iterator[E];if(R===r)return T.delegate=null,E==="throw"&&S.iterator.return&&(T.method="return",T.arg=r,tt(S,T),T.method==="throw")||E!=="return"&&(T.method="throw",T.arg=new TypeError("The iterator does not provide a '"+E+"' method")),v;var P=u(R,S.iterator,T.arg);if(P.type==="throw")return T.method="throw",T.arg=P.arg,T.delegate=null,v;var H=P.arg;return H?H.done?(T[S.resultName]=H.value,T.next=S.nextLoc,T.method!=="return"&&(T.method="next",T.arg=r),T.delegate=null,v):H:(T.method="throw",T.arg=new TypeError("iterator result is not an object"),T.delegate=null,v)}function Z(S){var T={tryLoc:S[0]};1 in S&&(T.catchLoc=S[1]),2 in S&&(T.finallyLoc=S[2],T.afterLoc=S[3]),this.tryEntries.push(T)}function z(S){var T=S.completion||{};T.type="normal",delete T.arg,S.completion=T}function Y(S){this.tryEntries=[{tryLoc:"root"}],S.forEach(Z,this),this.reset(!0)}function ct(S){if(S||S===""){var T=S[o];if(T)return T.call(S);if(typeof S.next=="function")return S;if(!isNaN(S.length)){var E=-1,R=function P(){for(;++E<S.length;)if(i.call(S,E))return P.value=S[E],P.done=!1,P;return P.value=r,P.done=!0,P};return R.next=R}}throw new TypeError(typeof S+" is not iterable")}return w.prototype=x,s(L,"constructor",{value:x,configurable:!0}),s(x,"constructor",{value:w,configurable:!0}),w.displayName=l(x,h,"GeneratorFunction"),t.isGeneratorFunction=function(S){var T=typeof S=="function"&&S.constructor;return!!T&&(T===w||(T.displayName||T.name)==="GeneratorFunction")},t.mark=function(S){return Object.setPrototypeOf?Object.setPrototypeOf(S,x):(S.__proto__=x,l(S,h,"GeneratorFunction")),S.prototype=Object.create(L),S},t.awrap=function(S){return{__await:S}},O(I.prototype),l(I.prototype,a,function(){return this}),t.AsyncIterator=I,t.async=function(S,T,E,R,P){P===void 0&&(P=Promise);var H=new I(c(S,T,E,R),P);return t.isGeneratorFunction(T)?H:H.next().then(function(J){return J.done?J.value:H.next()})},O(L),l(L,h,"Generator"),l(L,o,function(){return this}),l(L,"toString",function(){return"[object Generator]"}),t.keys=function(S){var T=Object(S),E=[];for(var R in T)E.push(R);return E.reverse(),function P(){for(;E.length;){var H=E.pop();if(H in T)return P.value=H,P.done=!1,P}return P.done=!0,P}},t.values=ct,Y.prototype={constructor:Y,reset:function(S){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(z),!S)for(var T in this)T.charAt(0)==="t"&&i.call(this,T)&&!isNaN(+T.slice(1))&&(this[T]=r)},stop:function(){this.done=!0;var S=this.tryEntries[0].completion;if(S.type==="throw")throw S.arg;return this.rval},dispatchException:function(S){if(this.done)throw S;var T=this;function E(et,Jt){return H.type="throw",H.arg=S,T.next=et,Jt&&(T.method="next",T.arg=r),!!Jt}for(var R=this.tryEntries.length-1;R>=0;--R){var P=this.tryEntries[R],H=P.completion;if(P.tryLoc==="root")return E("end");if(P.tryLoc<=this.prev){var J=i.call(P,"catchLoc"),lt=i.call(P,"finallyLoc");if(J&&lt){if(this.prev<P.catchLoc)return E(P.catchLoc,!0);if(this.prev<P.finallyLoc)return E(P.finallyLoc)}else if(J){if(this.prev<P.catchLoc)return E(P.catchLoc,!0)}else{if(!lt)throw new Error("try statement without catch or finally");if(this.prev<P.finallyLoc)return E(P.finallyLoc)}}}},abrupt:function(S,T){for(var E=this.tryEntries.length-1;E>=0;--E){var R=this.tryEntries[E];if(R.tryLoc<=this.prev&&i.call(R,"finallyLoc")&&this.prev<R.finallyLoc){var P=R;break}}P&&(S==="break"||S==="continue")&&P.tryLoc<=T&&T<=P.finallyLoc&&(P=null);var H=P?P.completion:{};return H.type=S,H.arg=T,P?(this.method="next",this.next=P.finallyLoc,v):this.complete(H)},complete:function(S,T){if(S.type==="throw")throw S.arg;return S.type==="break"||S.type==="continue"?this.next=S.arg:S.type==="return"?(this.rval=this.arg=S.arg,this.method="return",this.next="end"):S.type==="normal"&&T&&(this.next=T),v},finish:function(S){for(var T=this.tryEntries.length-1;T>=0;--T){var E=this.tryEntries[T];if(E.finallyLoc===S)return this.complete(E.completion,E.afterLoc),z(E),v}},catch:function(S){for(var T=this.tryEntries.length-1;T>=0;--T){var E=this.tryEntries[T];if(E.tryLoc===S){var R=E.completion;if(R.type==="throw"){var P=R.arg;z(E)}return P}}throw new Error("illegal catch attempt")},delegateYield:function(S,T,E){return this.delegate={iterator:ct(S),resultName:T,nextLoc:E},this.method==="next"&&(this.arg=r),v}},t}function eg(r,t){if(typeof r!="object"||!r)return r;var e=r[Symbol.toPrimitive];if(e!==void 0){var i=e.call(r,t);if(typeof i!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(r)}function nl(r){var t=eg(r,"string");return typeof t=="symbol"?t:String(t)}function rl(r,t,e,i,s,n,o){try{var a=r[n](o),h=a.value}catch(l){e(l);return}a.done?t(h):Promise.resolve(h).then(i,s)}function Ji(r){return function(){var t=this,e=arguments;return new Promise(function(i,s){var n=r.apply(t,e);function o(h){rl(n,i,s,o,a,"next",h)}function a(h){rl(n,i,s,o,a,"throw",h)}o(void 0)})}}function ol(r,t){if(!(r instanceof t))throw new TypeError("Cannot call a class as a function")}function ig(r,t){for(var e=0;e<t.length;e++){var i=t[e];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(r,nl(i.key),i)}}function al(r,t,e){return t&&ig(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}function Qi(r,t,e){return t=nl(t),t in r?Object.defineProperty(r,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):r[t]=e,r}function hl(r,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&Yr(r,t)}function zt(r){return zt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},zt(r)}function Yr(r,t){return Yr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(i,s){return i.__proto__=s,i},Yr(r,t)}function pn(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function sg(r,t){if(t&&(typeof t=="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return pn(r)}function ng(r,t){for(;!Object.prototype.hasOwnProperty.call(r,t)&&(r=zt(r),r!==null););return r}function oe(){return typeof Reflect<"u"&&Reflect.get?oe=Reflect.get.bind():oe=function(t,e,i){var s=ng(t,e);if(s){var n=Object.getOwnPropertyDescriptor(s,e);return n.get?n.get.call(arguments.length<3?t:i):n.value}},oe.apply(this,arguments)}function ll(r,t){return og(r)||Qd(r,t)||cl(r,t)||lg()}function ts(r){return rg(r)||ag(r)||cl(r)||hg()}function rg(r){if(Array.isArray(r))return Gr(r)}function og(r){if(Array.isArray(r))return r}function ag(r){if(typeof Symbol<"u"&&r[Symbol.iterator]!=null||r["@@iterator"]!=null)return Array.from(r)}function cl(r,t){if(r){if(typeof r=="string")return Gr(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);if(e==="Object"&&r.constructor&&(e=r.constructor.name),e==="Map"||e==="Set")return Array.from(r);if(e==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return Gr(r,t)}}function Gr(r,t){(t==null||t>r.length)&&(t=r.length);for(var e=0,i=new Array(t);e<t;e++)i[e]=r[e];return i}function hg(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function lg(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var es=function(r){hl(t,r);function t(e,i){var s;return ol(this,t),s=el(this,t,[e,tg({originX:"center",originY:"center",left:0,top:0,layoutManager:new _i(new Hr)},i)]),Qi(pn(s),"blockErasing",!1),s}return al(t,[{key:"drawObject",value:function(i){var s=[],n=[];this._objects.forEach(function(o){return(o instanceof pe?s:n).push(o)}),i.save(),i.fillStyle="black",i.fillRect(-this.width/2,-this.height/2,this.width,this.height),i.restore(),!this.blockErasing&&s.forEach(function(o){o.render(i)}),n.forEach(function(o){o.globalCompositeOperation=o.inverted?"destination-out":"source-in",o.render(i)})}}]),t}(Ft);Qi(es,"type","clipping"),D.setClass(es);var ul=function(t,e){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"source-over";t.save(),t.imageSmoothingEnabled=!0,t.imageSmoothingQuality="high",t.globalCompositeOperation=i,t.resetTransform(),t.drawImage(e.canvas,0,0),t.restore()},cg=function(t,e,i){ul(t,e,"destination-out"),i?ul(e,i,"source-in"):(e.save(),e.resetTransform(),e.clearRect(0,0,e.canvas.width,e.canvas.height),e.restore())};function dl(r){return r.flatMap(function(t){return!t.erasable||t.isNotVisible()?[]:t instanceof Ft&&t.erasable==="deep"?dl(t.getObjects()):[t]})}function ug(r,t,e){t.clearContext(r),r.imageSmoothingEnabled=t.imageSmoothingEnabled,r.imageSmoothingQuality="high",r.patternQuality="best",t._renderBackground(r),r.save(),r.transform.apply(r,ts(t.viewportTransform)),e.forEach(function(s){return s.render(r)}),r.restore();var i=t.clipPath;i&&(i._set("canvas",t),i.shouldCache(),i._transformDone=!0,i.renderCache({forClipping:!0}),t.drawClipPathOnCanvas(r,i)),t._renderOverlay(r)}function dg(r,t,e){var i=t.inverted,s=t.opacity,n=e.canvas,o=e.objects,a=o===void 0?n._objectsToRender||n._objects:o,h=e.background,l=h===void 0?n.backgroundImage:h,c=e.overlay,u=c===void 0?n.overlayImage:c,d=1-s,g=dl([].concat(ts(a),ts([l,u].filter(function(f){return!!f})))).map(function(f){if(i){if(f.clipPath instanceof es)return f.clipPath.blockErasing=!0,f.clipPath.set("dirty",!0),f.set("dirty",!0),{object:f,clipPath:f.clipPath}}else{var p,v=f.opacity;return f.opacity*=d,(p=f.parent)===null||p===void 0||p.set("dirty",!0),{object:f,opacity:v}}});ug(r,n,a),g.forEach(function(f){if(f)if(f.opacity){var p;f.object.opacity=f.opacity,(p=f.object.parent)===null||p===void 0||p.set("dirty",!0)}else f.clipPath&&(f.clipPath.blockErasing=!1,f.clipPath.set("dirty",!0),f.object.set("dirty",!0))})}function gl(r,t){return r.flatMap(function(e){return!e.erasable||!e.intersectsWithObject(t)?[]:e instanceof Ft&&e.erasable==="deep"?gl(e.getObjects(),t):[e]})}var gg=function(t){var e=t.clipPath;if(e instanceof es)return e;var i=t.strokeWidth,s=new C(i,i),n=t.strokeUniform?s.divide(t.getObjectScaling()):s,o=new es([],{width:t.width+n.x,height:t.height+n.y});if(e){var a=e.translateToOriginPoint(new C,e.originX,e.originY),h=a.x,l=a.y;e.originX=e.originY="center",Ui.sendObjectToPlane(e,void 0,Ui.createTranslateMatrix(h,l)),o.add(e)}return t.clipPath=o};function fl(r,t){var e=gg(r);e.add(t),e.set("dirty",!0),r.set("dirty",!0)}function fg(r,t){return Ur.apply(this,arguments)}function Ur(){return Ur=Ji(Zt().mark(function r(t,e){var i;return Zt().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,e.clone();case 2:return i=n.sent,Ui.sendObjectToPlane(i,void 0,t.calcTransformMatrix()),fl(t,i),n.abrupt("return",i);case 6:case"end":return n.stop()}},r)})),Ur.apply(this,arguments)}function pg(r,t,e){return qr.apply(this,arguments)}function qr(){return qr=Ji(Zt().mark(function r(t,e,i){var s,n;return Zt().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,i.clone();case 2:return s=a.sent,n=e&&t.translateToOriginPoint(new C,t.originX,t.originY),Ui.sendObjectToPlane(s,void 0,n?Ui.multiplyTransformMatrixArray([[1,0,0,1,n.x,n.y],e,[1,0,0,1,-n.x,-n.y],t.calcTransformMatrix()]):t.calcTransformMatrix()),fl(t,s),a.abrupt("return",s);case 7:case"end":return a.stop()}},r)})),qr.apply(this,arguments)}var mg=function(t,e,i){var s=i.width,n=i.height,o=arguments.length>3&&arguments[3]!==void 0?arguments[3]:1;t.width=s,t.height=n,o>1&&(t.setAttribute("width",(s*o).toString()),t.setAttribute("height",(n*o).toString()),e.scale(o,o))},vg=function(r){hl(t,r);function t(e){var i;ol(this,t),i=el(this,t,[e]),Qi(pn(i),"inverted",!1),Qi(pn(i),"active",!1);var s=document.createElement("canvas"),n=s.getContext("2d");if(!n)throw new Error("Failed to get context");return mg(s,n,e,i.canvas.getRetinaScaling()),i.effectContext=n,i.eventEmitter=new EventTarget,i}return al(t,[{key:"on",value:function(i,s,n){var o=this;return this.eventEmitter.addEventListener(i,s,n),function(){return o.eventEmitter.removeEventListener(i,s,n)}}},{key:"drawEffect",value:function(){dg(this.effectContext,{opacity:new N(this.color).getAlpha(),inverted:this.inverted},{canvas:this.canvas})}},{key:"_setBrushStyles",value:function(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.canvas.contextTop;oe(zt(t.prototype),"_setBrushStyles",this).call(this,i),i.strokeStyle="black"}},{key:"needsFullRender",value:function(){return!0}},{key:"_render",value:function(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.canvas.getTopContext();oe(zt(t.prototype),"_render",this).call(this,i),cg(this.canvas.getContext(),i,this.effectContext)}},{key:"onMouseDown",value:function(i,s){var n=this;this.eventEmitter.dispatchEvent(new CustomEvent("start",{detail:s,cancelable:!0}))&&(this.active=!0,this.eventEmitter.dispatchEvent(new CustomEvent("redraw",{detail:{type:"start"},cancelable:!0}))&&this.drawEffect(),this._disposer=this.canvas.on("after:render",function(o){var a=o.ctx;a===n.canvas.getContext()&&(n.eventEmitter.dispatchEvent(new CustomEvent("redraw",{detail:{type:"render"},cancelable:!0}))&&n.drawEffect(),n._render())}),oe(zt(t.prototype),"onMouseDown",this).call(this,i,s))}},{key:"onMouseMove",value:function(i,s){this.active&&this.eventEmitter.dispatchEvent(new CustomEvent("move",{detail:s,cancelable:!0}))&&oe(zt(t.prototype),"onMouseMove",this).call(this,i,s)}},{key:"onMouseUp",value:function(i){var s;return this.active&&oe(zt(t.prototype),"onMouseUp",this).call(this,i),this.active=!1,(s=this._disposer)===null||s===void 0||s.call(this),delete this._disposer,!1}},{key:"convertPointsToSVGPath",value:function(i){return oe(zt(t.prototype),"convertPointsToSVGPath",this).call(this,this.decimate?this.decimatePoints(i,this.decimate):i)}},{key:"createPath",value:function(i){var s=oe(zt(t.prototype),"createPath",this).call(this,i);return s.set(this.inverted?{globalCompositeOperation:"source-over",stroke:"white"}:{globalCompositeOperation:"destination-out",stroke:"black",opacity:new N(this.color).getAlpha()}),s}},{key:"commit",value:function(){var e=Ji(Zt().mark(function s(n){var o,a;return Zt().wrap(function(l){for(;;)switch(l.prev=l.next){case 0:return o=n.path,a=n.targets,l.t0=Map,l.next=4,Promise.all([].concat(ts(a.map(function(){var c=Ji(Zt().mark(function u(d){return Zt().wrap(function(f){for(;;)switch(f.prev=f.next){case 0:return f.t0=d,f.next=3,fg(d,o);case 3:return f.t1=f.sent,f.abrupt("return",[f.t0,f.t1]);case 5:case"end":return f.stop()}},u)}));return function(u){return c.apply(this,arguments)}}())),ts([[this.canvas.backgroundImage,this.canvas.backgroundVpt?void 0:this.canvas.viewportTransform],[this.canvas.overlayImage,this.canvas.overlayVpt?void 0:this.canvas.viewportTransform]].filter(function(c){var u=ll(c,1),d=u[0];return!!(d!=null&&d.erasable)}).map(function(){var c=Ji(Zt().mark(function u(d){var g,f,p;return Zt().wrap(function(b){for(;;)switch(b.prev=b.next){case 0:return g=ll(d,2),f=g[0],p=g[1],b.t0=f,b.next=4,pg(f,p,o);case 4:return b.t1=b.sent,b.abrupt("return",[b.t0,b.t1]);case 6:case"end":return b.stop()}},u)}));return function(u){return c.apply(this,arguments)}}()))));case 4:return l.t1=l.sent,l.abrupt("return",new l.t0(l.t1));case 6:case"end":return l.stop()}},s,this)}));function i(s){return e.apply(this,arguments)}return i}()},{key:"_finalizeAndAddPath",value:function(){var i=this._points;if(i.length<2){this.eventEmitter.dispatchEvent(new CustomEvent("cancel",{cancelable:!1}));return}var s=this.createPath(this.convertPointsToSVGPath(i)),n=gl(this.canvas.getObjects(),s);this.eventEmitter.dispatchEvent(new CustomEvent("end",{detail:{path:s,targets:n},cancelable:!0}))&&this.commit({path:s,targets:n}),this.canvas.clearContext(this.canvas.contextTop),this.canvas.requestRenderAll(),this._resetShadow()}},{key:"dispose",value:function(){var i=this.effectContext.canvas;i.width=i.height=0}}]),t}(qi);const Dn=class Dn{constructor(){M(this,"editor",null);M(this,"fabricCanvas",null);M(this,"undoRedoManager",fn.getInstance())}static getInstance(){return this.instance||(this.instance=new Dn),this.instance}initialize(t){this.editor=t}getFabricCanvas(){return this.fabricCanvas}setFabricCanvas(t){this.fabricCanvas=t}undo(){this.undoRedoManager.undo()}redo(){this.undoRedoManager.redo()}updateUndoRedoButtons(t,e){const i=this.undoRedoManager.hasUndo(),s=this.undoRedoManager.hasRedo();t.disabled=!i,e.disabled=!s}attachUndoRedoHandlers(t,e){t.addEventListener("click",()=>{this.undo(),this.updateUndoRedoButtons(t,e)}),e.addEventListener("click",()=>{this.redo(),this.updateUndoRedoButtons(t,e)}),this.updateUndoRedoButtons(t,e)}getUndoRedoManager(){return this.undoRedoManager}handleAddTextButtonClick(){var a;const t=new Image;t.src=Pc,(a=this.fabricCanvas)!=null&&a.isDrawingMode&&(this.fabricCanvas.isDrawingMode=!1);const e=new De("Enter Text",{left:100,top:100,fontSize:24,objectCaching:!1,fill:"black",editable:!0,borderColor:"rgb(252, 207, 3)",cornerColor:"rgb(252, 207, 3)",cornerStrokeColor:"rgb(252, 207, 3)",transparentCorners:!1,hasControls:!0});["tl","tr","bl","br","mt","mb","ml","mr","mtr"].forEach(h=>{console.log(h)});let i;const s=new At({x:.5,y:-.5,offsetY:-16,offsetX:16,cursorStyle:"pointer",mouseUpHandler:(h,l)=>{i=l.target,this.undoRedoManager.add({undo:()=>{this.fabricCanvas.add(e),this.fabricCanvas.requestRenderAll()},redo:()=>{this.fabricCanvas.remove(i),this.fabricCanvas.requestRenderAll()}}),this.fabricCanvas.remove(i),this.fabricCanvas.requestRenderAll()},render:(h,l,c)=>{if(!t.complete){t.onload=()=>this.fabricCanvas.requestRenderAll();return}h.drawImage(t,l-15,c-12,15,15)}}),n=new Image;n.src='data:image/svg+xml;utf8,<svg width="15" height="15" xmlns="http://www.w3.org/2000/svg"><circle cx="7.5" cy="7.5" r="7" fill="orange" stroke="black" stroke-width="1"/></svg>';const o=new At({x:.5,y:-.5,offsetX:30,offsetY:-10,cursorStyle:"pointer",mouseUpHandler:(h,l)=>{console.log(l);const c=document.createElement("input");c.type="color",c.value=e.fill||"#000000",c.style.position="fixed",c.style.left="-9999px",document.body.appendChild(c),c.click(),c.oninput=()=>{e.set("fill",c.value),this.fabricCanvas.requestRenderAll(),document.body.removeChild(c)},c.onblur=()=>{document.body.removeChild(c)}},render:(h,l,c)=>{if(!n.complete){n.onload=()=>this.fabricCanvas.requestRenderAll();return}h.drawImage(n,l-15,c+8,15,15)}});this.undoRedoManager.add({undo:()=>{this.fabricCanvas.remove(e),this.fabricCanvas.requestRenderAll()},redo:()=>{this.fabricCanvas.add(e),this.fabricCanvas.setActiveObject(e),this.fabricCanvas.requestRenderAll()}}),e.controls.delete=s,e.controls.color=o,this.fabricCanvas.add(e),this.fabricCanvas.setActiveObject(e),this.fabricCanvas.requestRenderAll()}handleCropButtonClick(t,e){console.log(e);const i=this.editor.getEditorContainer();i.innerHTML="";let s=document.createElement("canvas");i.appendChild(s),this.fabricCanvas=new jr(s,{height:t.height,width:t.width});let n=new Image;n.src=t.toDataURL("image/png"),n.onload=()=>{(async()=>{try{const a=new Lt(n);a.set({left:0,top:0,scaleX:this.fabricCanvas.width/n.width,scaleY:this.fabricCanvas.height/n.height,selectable:!1}),this.fabricCanvas.add(a),this.fabricCanvas.renderAll()}catch(a){console.error("Error loading cropped image:",a)}})()},this.fabricCanvas.on("selection:created",o=>{const a=o.target,h=(a==null?void 0:a.type)==="activeSelection"?a._objects:[a],l=u=>{if(!u.clipPath||!u.annotation)return!1;const d=u.getBoundingRect(!0,!0),g=u.clipPath.getBoundingRect(!0,!0);return g.left<=d.left&&g.top<=d.top&&g.left+g.width>=d.left+d.width&&g.top+g.height>=d.top+d.height};if(h.filter(u=>u.annotation?!l(u):!0).length===0){this.fabricCanvas.discardActiveObject(),this.fabricCanvas.requestRenderAll(),console.warn("Selection discarded: All selected annotations are fully erased.");return}}),this.fabricCanvas.on("path:created",o=>{const a=o.path;console.log("New annotation path added:",a),o.path.set({erasable:!0,annotation:!0,selectable:!0}),this.undoRedoManager.add({undo:()=>{this.fabricCanvas.remove(a),this.fabricCanvas.requestRenderAll()},redo:()=>{this.fabricCanvas.add(a),this.fabricCanvas.requestRenderAll()}})})}enableEraserMode(){if(this.fabricCanvas){const t=new Ut({radius:50,erasable:!0});this.fabricCanvas.add(t);const e=new vg(this.fabricCanvas);e.width=30,this.fabricCanvas.freeDrawingBrush=e,this.fabricCanvas.isDrawingMode=!0,e.on("start",i=>{console.log(i)}),e.on("end",async i=>{var s,n;console.log(i),(s=this.fabricCanvas)==null||s.discardActiveObject(),(n=this.fabricCanvas)==null||n.requestRenderAll()})}else console.error("Fabric canvas is not initialized")}};M(Dn,"instance",null);let ae=Dn;const ti=class ti{constructor(){M(this,"element");M(this,"backButton");M(this,"saveButton");M(this,"cancelButton");M(this,"doneButton");M(this,"toolContainer");this.element=document.createElement("div"),this.element.style.backgroundColor="white",this.element.style.color="white",this.element.style.display="flex",this.element.style.justifyContent="space-between",this.element.style.alignItems="center";const t=document.createElement("div");t.style.display="flex",t.style.alignItems="center",this.backButton=document.createElement("button"),this.backButton.id="backButton",this.backButton.title="Back",this.backButton.style.background="none",this.backButton.style.border="none",this.backButton.style.cursor="pointer";const e=document.createElement("img");e.src=_c,e.alt="Back",e.style.width="20px",e.style.height="20px",this.backButton.appendChild(e),t.appendChild(this.backButton),this.toolContainer=document.createElement("div"),this.toolContainer.style.flex="1",this.toolContainer.style.display="flex",this.toolContainer.style.gap="10px";const i=document.createElement("div");i.style.display="flex",i.style.gap="10px",i.style.alignItems="center",this.saveButton=document.createElement("button"),this.saveButton.id="saveButton",this.saveButton.title="Save",this.saveButton.style.background="none",this.saveButton.style.border="none",this.saveButton.style.cursor="pointer",this.saveButton.style.display="none";const s=document.createElement("img");s.src=wc,s.alt="Save",s.style.width="15px",s.style.height="15px",this.saveButton.appendChild(s),this.cancelButton=document.createElement("button"),this.cancelButton.id="cancelButton",this.cancelButton.title="Cancel",this.cancelButton.style.background="none",this.cancelButton.style.border="none",this.cancelButton.style.cursor="pointer",this.cancelButton.style.display="none";const n=document.createElement("img");n.src=xc,n.alt="Cancel",n.style.width="15px",n.style.height="15px",this.cancelButton.appendChild(n),this.doneButton=document.createElement("button"),this.doneButton.id="doneButton",this.doneButton.title="Done",this.doneButton.style.background="none",this.doneButton.style.border="none",this.doneButton.style.cursor="pointer",this.doneButton.style.color="black",this.doneButton.style.display="none",this.doneButton.textContent="Done",i.appendChild(this.saveButton),i.appendChild(this.cancelButton),i.appendChild(this.doneButton),this.element.appendChild(t),this.element.appendChild(this.toolContainer),this.element.appendChild(i),this.bindEvents(),this.subscribeToStore()}static getInstance(){return ti.instance||(ti.instance=new ti),ti.instance}bindEvents(){this.backButton.onclick=()=>{this.element.dispatchEvent(new CustomEvent("editor:back",{bubbles:!0}))},this.saveButton.onclick=()=>{this.element.dispatchEvent(new CustomEvent("editor:save",{bubbles:!0}))},this.cancelButton.onclick=()=>{this.element.dispatchEvent(new CustomEvent("editor:cancel",{bubbles:!0}))},this.doneButton.onclick=()=>{let t=ss.getInstance();if(!t)return;const e=kt.get();if(e==="annotate"){const i=t.getEditorContainer(),s=t.getImageContainer(),n=t.getImageElement(),o=window.fabricCanvas||ae.getInstance().getFabricCanvas();if(o&&s){const a=o.toDataURL({format:"png"});if(n)n.src=a,n.style.display="block",n.alt="Annotated Image",s.innerHTML="",s.appendChild(n),i.innerHTML="",i.appendChild(s);else{i.innerHTML="";const h=document.createElement("img");h.src=a,h.alt="Annotated Image",h.style.maxWidth="100%",h.style.maxHeight="100%",h.style.objectFit="contain",s.appendChild(h),i.appendChild(s)}le()}return}else if(e==="crop"){const i=t.getCropper();let s=i==null?void 0:i.getCropperSelection();if(!s)return;const n=t.getEditorContainer(),o=t.getImageContainer(),a=t.getImageElement(),h=n.getBoundingClientRect(),l=h.width,c=h.height;s.$toCanvas({width:l,height:c}).then(u=>{const d=u.toDataURL("image/png");if(a&&o)a.src=d,a.style.display="block",a.alt="Cropped Image",o.innerHTML="",o.appendChild(a),n.innerHTML="",n.appendChild(o);else{n.innerHTML="";const g=document.createElement("img");g.src=d,g.alt="Cropped Image",g.style.maxWidth="100%",g.style.maxHeight="100%",g.style.objectFit="contain",n.appendChild(g)}le(),t.setCropper(null)})}}}subscribeToStore(){kt.subscribe(t=>{t==="crop"||t==="annotate"?(this.backButton.style.display="",this.saveButton.style.display="none",this.cancelButton.style.display="none",this.doneButton.style.display=""):t==="home"?(this.backButton.style.display="none",this.saveButton.style.display="block",this.cancelButton.style.display="block",this.doneButton.style.display="none"):(this.backButton.style.display="none",this.saveButton.style.display="none",this.cancelButton.style.display="none",this.doneButton.style.display="none")})}addTool(t){this.toolContainer.appendChild(t)}};M(ti,"instance",null);let mn=ti;const ei=class ei{constructor(t){M(this,"toolbar");M(this,"eraserButton");M(this,"cropButton");M(this,"annotateButton");M(this,"imageScaleButton");M(this,"rotateLeftButton");M(this,"rotateRightButton");M(this,"undoButton");M(this,"redoButton");M(this,"zoomOutButton");M(this,"zoomInButton");M(this,"zoomLabel");M(this,"addTextButton");M(this,"reportbutton");M(this,"resetButton");M(this,"addAnnotationButton");M(this,"onShapeChange");M(this,"undoRedoContainer");M(this,"zoomContainer");M(this,"rotateContainer");M(this,"shapeDropdownContainer");this.toolbar=document.createElement("div"),this.toolbar.className="bottom-toolbar-container";let e=document.createElement("button");e.className="btn btn-accent btn-outline rounded-full",e.innerHTML="sample",this.toolbar.appendChild(e),this.cropButton=this.createButton(t.crop,"Crop","crop-button"),this.toolbar.appendChild(this.cropButton);const i=document.createElement("div");i.className="relative",this.shapeDropdownContainer=i;const s=document.createElement("button");s.className="w-40 bg-slate-950 text-white px-4 py-2 rounded-[15px] shadow hover:bg-slate-800 flex items-center justify-between";const n=document.createElement("span");n.textContent="Rectangle",n.className="selected-text",s.appendChild(n);const o=document.createElementNS("http://www.w3.org/2000/svg","svg");o.setAttribute("class","w-4 h-4 ml-2 fill-current"),o.setAttribute("viewBox","0 0 20 20"),o.innerHTML='<path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"/>',s.appendChild(o);const a=document.createElement("div");a.className="shape-dropdown-portal text-slate-950 hidden bg-white border border-gray-300 rounded shadow w-40";const h=document.createElement("ul"),l=["Rectangle","Oval"],c=[];l.forEach(x=>{const _=document.createElement("li"),k=document.createElement("button");k.setAttribute("data-value",x),k.className="dropdown-option block w-full text-left px-4 py-2 hover:bg-gray-100",k.textContent=x,_.appendChild(k),h.appendChild(_),c.push(k)}),a.appendChild(h),i.appendChild(s);let u=!1;function d(){a.classList.add("hidden"),u=!1,document.removeEventListener("mousedown",f),window.removeEventListener("resize",d),window.removeEventListener("scroll",d,!0)}function g(){const x=s.getBoundingClientRect();a.style.left=`${x.left+window.scrollX}px`,a.style.top=`${x.top+window.scrollY-a.offsetHeight}px`}function f(x){!s.contains(x.target)&&!a.contains(x.target)&&d()}s.addEventListener("click",()=>{if(u)d();else{const x=s.getRootNode();x instanceof ShadowRoot?x.appendChild(a):document.body.appendChild(a),a.classList.remove("hidden"),setTimeout(()=>{g()},0),u=!0,document.addEventListener("mousedown",f),window.addEventListener("resize",d),window.addEventListener("scroll",d,!0)}}),c.forEach(x=>{x.addEventListener("mousedown",_=>{_.stopPropagation()}),x.addEventListener("click",()=>{var k;const _=x.getAttribute("data-value")||"Rectangle";console.log("Dropdown clicked:",_),n.textContent!==_&&Un.set(_),n.textContent=_,d(),(k=this.onShapeChange)==null||k.call(this,_)})}),this.toolbar.appendChild(i),this.annotateButton=this.createButton(t.annotate,"Annotate","annotate-button"),this.toolbar.appendChild(this.annotateButton),this.addAnnotationButton=this.createButton(t.addAnnotation,"add Annotation","add-annotate-button"),this.toolbar.appendChild(this.addAnnotationButton),this.eraserButton=this.createButton(t.eraser,"Eraser","eraser-button"),this.toolbar.appendChild(this.eraserButton),this.addTextButton=this.createButton(t!=null&&t.addText?t==null?void 0:t.addText:"","Add text","add-text-button"),this.addTextButton.id="addTextBtn",this.toolbar.appendChild(this.addTextButton),this.imageScaleButton=this.createButton(t.imageScale,"Image Scale","image-scale-button"),this.toolbar.appendChild(this.imageScaleButton);const p=document.createElement("div");p.className="buttons-group-container",this.rotateContainer=p,this.rotateLeftButton=this.createButton(t.rotateLeft,"Rotate Left","rotate-left-button"),p.appendChild(this.rotateLeftButton),this.rotateRightButton=this.createButton(t.rotateRight,"Rotate Right","rotate-right-button"),p.appendChild(this.rotateRightButton),this.toolbar.appendChild(p);const v=document.createElement("div");v.className="buttons-group-container",this.undoRedoContainer=v,this.undoButton=this.createButton(t.undo,"Undo","undo-button bottom-bar-button"),v.appendChild(this.undoButton),this.redoButton=this.createButton(t.redo,"Redo","redo-button"),v.appendChild(this.redoButton),this.toolbar.appendChild(v);const b=document.createElement("div");b.className="buttons-group-container",this.zoomContainer=b,this.zoomOutButton=this.createButton(t.zoomOut,"Zoom Out","zoom-out-button"),b.appendChild(this.zoomOutButton),this.zoomLabel=document.createElement("span"),this.zoomLabel.className="zoom-label",this.zoomLabel.textContent="10%",b.appendChild(this.zoomLabel),this.zoomInButton=this.createButton(t.zoomIn,"Zoom In","zoom-in-button"),b.appendChild(this.zoomInButton),this.toolbar.appendChild(b),this.reportbutton=document.createElement("div"),this.reportbutton.innerHTML=`
                                    <label class="switch">
                                        <input type="checkbox">
                                        <span class="slider"></span>
                                    </label>`,this.toolbar.appendChild(this.reportbutton),this.resetButton=this.createButton(t!=null&&t.resetIcon?t==null?void 0:t.resetIcon:"","Reset","resetBtn");let w=document.createElement("div");w.className="bottom-toolbar",w.appendChild(this.toolbar),this.toolbar=w,this.cropButton.addEventListener("click",()=>{Ko()}),le(),kt.subscribe(x=>{this.showToolbarState(x)})}createButton(t,e,i){const s=document.createElement("button");return s.className=i+" bottom-bar-button",s.innerHTML=`<img src="${t}" alt="${e}" class="button-icon">`,s}getToolbar(){return this.toolbar}showToolbarState(t){t==="crop"?(this.cropButton.style.display="",this.shapeDropdownContainer.style.display="",this.zoomContainer.style.display="",this.rotateContainer.style.display="",this.undoRedoContainer.style.display="none",this.annotateButton.style.display="none",this.addTextButton.style.display="none",this.eraserButton.style.display="none",this.reportbutton.style.display="none",this.imageScaleButton.style.display="none",this.addAnnotationButton.style.display="none"):t==="annotate"?(this.annotateButton.style.display="",this.rotateContainer.style.display="",this.addTextButton.style.display="",this.eraserButton.style.display="",this.undoRedoContainer.style.display="",this.redoButton.style.display="",this.undoButton.style.display="",this.addAnnotationButton.style.display="",this.annotateButton.style.display="none",this.cropButton.style.display="none",this.shapeDropdownContainer.style.display="none",this.zoomContainer.style.display="none",this.reportbutton.style.display="none",this.imageScaleButton.style.display="none"):(this.cropButton.style.display="",this.annotateButton.style.display="",this.imageScaleButton.style.display="",this.reportbutton.style.display="",this.shapeDropdownContainer.style.display="none",this.zoomContainer.style.display="none",this.rotateContainer.style.display="none",this.undoRedoContainer.style.display="none",this.addTextButton.style.display="none",this.eraserButton.style.display="none",this.addAnnotationButton.style.display="none")}static getInstance(t){return ei.instance||(ei.instance=new ei(t)),ei.instance}};M(ei,"instance");let vn=ei;const yg="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20fill='%23000000'%20width='800px'%20height='800px'%20viewBox='0%200%2024%2024'%20id='turn-around-right-direction-2'%20data-name='Flat%20Color'%20xmlns='http://www.w3.org/2000/svg'%20class='icon%20flat-color'%3e%3cpath%20id='primary'%20d='M21.71,8.29l-3-3a1,1,0,0,0-1.42,1.42L18.59,8H7.5a5.5,5.5,0,0,0,0,11H11a1,1,0,0,0,0-2H7.5a3.5,3.5,0,0,1,0-7H18.59l-1.3,1.29a1,1,0,0,0,0,1.42,1,1,0,0,0,1.42,0l3-3A1,1,0,0,0,21.71,8.29Z'%20style='fill:%20rgb(0,%200,%200);'%3e%3c/path%3e%3c/svg%3e",bg="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20fill='%23000000'%20width='800px'%20height='800px'%20viewBox='0%200%2024%2024'%20id='turn-around-left-top-direction-2'%20data-name='Flat%20Color'%20xmlns='http://www.w3.org/2000/svg'%20class='icon%20flat-color'%3e%3cpath%20id='primary'%20d='M16.5,8H5.41l1.3-1.29A1,1,0,0,0,5.29,5.29l-3,3a1,1,0,0,0,0,1.42l3,3a1,1,0,0,0,1.42,0,1,1,0,0,0,0-1.42L5.41,10H16.5a3.5,3.5,0,0,1,0,7H13a1,1,0,0,0,0,2h3.5a5.5,5.5,0,0,0,0-11Z'%20style='fill:%20rgb(0,%200,%200);'%3e%3c/path%3e%3c/svg%3e",Cg="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20width='800px'%20height='800px'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M4%2011C4%207.13401%207.13401%204%2011%204C14.866%204%2018%207.13401%2018%2011C18%2014.866%2014.866%2018%2011%2018C7.13401%2018%204%2014.866%204%2011ZM11%202C6.02944%202%202%206.02944%202%2011C2%2015.9706%206.02944%2020%2011%2020C13.125%2020%2015.078%2019.2635%2016.6177%2018.0319L20.2929%2021.7071C20.6834%2022.0976%2021.3166%2022.0976%2021.7071%2021.7071C22.0976%2021.3166%2022.0976%2020.6834%2021.7071%2020.2929L18.0319%2016.6177C19.2635%2015.078%2020%2013.125%2020%2011C20%206.02944%2015.9706%202%2011%202Z'%20fill='%23000000'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M10%2014C10%2014.5523%2010.4477%2015%2011%2015C11.5523%2015%2012%2014.5523%2012%2014V12H14C14.5523%2012%2015%2011.5523%2015%2011C15%2010.4477%2014.5523%2010%2014%2010H12V8C12%207.44772%2011.5523%207%2011%207C10.4477%207%2010%207.44772%2010%208V10H8C7.44772%2010%207%2010.4477%207%2011C7%2011.5523%207.44772%2012%208%2012H10V14Z'%20fill='%23000000'/%3e%3c/svg%3e",wg="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20width='800px'%20height='800px'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M4%2011C4%207.13401%207.13401%204%2011%204C14.866%204%2018%207.13401%2018%2011C18%2014.866%2014.866%2018%2011%2018C7.13401%2018%204%2014.866%204%2011ZM11%202C6.02944%202%202%206.02944%202%2011C2%2015.9706%206.02944%2020%2011%2020C13.125%2020%2015.078%2019.2635%2016.6177%2018.0319L20.2929%2021.7071C20.6834%2022.0976%2021.3166%2022.0976%2021.7071%2021.7071C22.0976%2021.3166%2022.0976%2020.6834%2021.7071%2020.2929L18.0319%2016.6177C19.2635%2015.078%2020%2013.125%2020%2011C20%206.02944%2015.9706%202%2011%202Z'%20fill='%23000000'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M7%2011C7%2010.4477%207.44772%2010%208%2010H14C14.5523%2010%2015%2010.4477%2015%2011C15%2011.5523%2014.5523%2012%2014%2012H8C7.44772%2012%207%2011.5523%207%2011Z'%20fill='%23000000'/%3e%3c/svg%3e",xg="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20fill='%23000000'%20width='800px'%20height='800px'%20viewBox='0%200%2024%2024'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20d='M20.4852814,14%20L12,22.4852814%20L3.51471863,14%20L12,5.51471863%20L20.4852814,14%20Z%20M6.34314575,14%20L12,19.6568542%20L17.6568542,14%20L12,8.34314575%20L6.34314575,14%20Z%20M4.15672797,4.28593933%20C6.18580699,2.22227176%208.98673097,1%2012,1%20C15.9721115,1%2019.5752376,3.12390371%2021.5277839,6.49927404%20L19.7965722,7.50072596%20C18.1979627,4.73720691%2015.2508538,3%2012,3%20C9.38603956,3%206.96846584,4.12319378%205.29042786,6%20L8,6%20L8,8%20L2.40310647,8%20L2.00227469,2.0674107%20L3.99772531,1.9325893%20L4.15672797,4.28593933%20Z'/%3e%3c/svg%3e",_g="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20fill='%23000000'%20width='800px'%20height='800px'%20viewBox='0%200%2024%2024'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20d='M3.04477723,14%20L11.5300586,5.51471863%20L20.01534,14%20L11.5300586,22.4852814%20L3.04477723,14%20Z%20M17.1869129,14%20L11.5300586,8.34314575%20L5.87320435,14%20L11.5300586,19.6568542%20L17.1869129,14%20Z%20M19.3733306,4.28593933%20L19.5323333,1.9325893%20L21.5277839,2.0674107%20L21.1269521,8%20L15.5300586,8%20L15.5300586,6%20L18.2396307,6%20C16.5615928,4.12319378%2014.144019,3%2011.5300586,3%20C8.27920479,3%205.33209587,4.73720691%203.73348642,7.50072596%20L2.00227469,6.49927404%20C3.954821,3.12390371%207.5579471,1%2011.5300586,1%20C14.5433276,1%2017.3442516,2.22227176%2019.3733306,4.28593933%20Z'/%3e%3c/svg%3e",Sg="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20width='800px'%20height='800px'%20viewBox='0%200%201920%201920'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20fill-rule='evenodd'%20clip-rule='evenodd'%20stroke='none'%20stroke-width='1'%3e%3cpath%20d='M1468.235%20113v99.388l-112.94%20112.941v-99.388H112.94v1468.235h1242.353v-438.211l112.941-112.941v664.094H0V113h1468.235ZM421.271%201163.353c96-9.035%20154.729%20108.423%20190.87%20197.647%2028.235%2068.894%2038.4%2092.612%2072.283%2096%2033.882%203.388%2089.223-58.73%20112.94-101.647%2016.53-26.51%2051.42-34.6%2077.93-18.07%2026.51%2016.529%2034.6%2051.42%2018.07%2077.929-9.035%2016.94-92.611%20160.376-205.552%20160.376h-9.036c-70.023-4.517-121.976-48.564-169.411-166.023-47.436-117.46-77.93-127.624-77.93-127.624a484.518%20484.518%200%200%200-97.13%20225.883c-6.549%2031.187-37.14%2051.16-68.329%2044.611-31.187-6.55-51.16-37.141-44.611-68.33%2020.33-94.87%2079.059-310.587%20199.906-320.752Zm256.376-485.647v112.941H338.824V677.706h338.823ZM903.53%20451.824v112.94H338.824v-112.94h564.705Z'/%3e%3cpath%20d='m1903.059%20468.765-225.883-225.883a56.47%2056.47%200%200%200-80.188%200L919.341%20920.53a56.476%2056.476%200%200%200-15.813%2039.53v282.353h282.354a56.47%2056.47%200%200%200%2039.53-16.941l677.647-677.647c21.523-21.959%2021.523-57.101%200-79.06Zm-740.894%20660.706H1016.47V983.776l451.764-451.764%20145.694%20145.694-451.764%20451.765Zm531.953-531.953-145.694-145.694%2089.223-89.224%20145.694%20145.694-89.223%2089.224Z'/%3e%3c/g%3e%3c/svg%3e",Tg="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20fill='%23000000'%20width='800px'%20height='800px'%20viewBox='0%200%2032%2032'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M30.133%201.552c-1.090-1.044-2.291-1.573-3.574-1.573-2.006%200-3.47%201.296-3.87%201.693-0.564%200.558-19.786%2019.788-19.786%2019.788-0.126%200.126-0.217%200.284-0.264%200.456-0.433%201.602-2.605%208.71-2.627%208.782-0.112%200.364-0.012%200.761%200.256%201.029%200.193%200.192%200.45%200.295%200.713%200.295%200.104%200%200.208-0.016%200.31-0.049%200.073-0.024%207.41-2.395%208.618-2.756%200.159-0.048%200.305-0.134%200.423-0.251%200.763-0.754%2018.691-18.483%2019.881-19.712%201.231-1.268%201.843-2.59%201.819-3.925-0.025-1.319-0.664-2.589-1.901-3.776zM22.37%204.87c0.509%200.123%201.711%200.527%202.938%201.765%201.24%201.251%201.575%202.681%201.638%203.007-3.932%203.912-12.983%2012.867-16.551%2016.396-0.329-0.767-0.862-1.692-1.719-2.555-1.046-1.054-2.111-1.649-2.932-1.984%203.531-3.532%2012.753-12.757%2016.625-16.628zM4.387%2023.186c0.55%200.146%201.691%200.57%202.854%201.742%200.896%200.904%201.319%201.9%201.509%202.508-1.39%200.447-4.434%201.497-6.367%202.121%200.573-1.886%201.541-4.822%202.004-6.371zM28.763%207.824c-0.041%200.042-0.109%200.11-0.19%200.192-0.316-0.814-0.87-1.86-1.831-2.828-0.981-0.989-1.976-1.572-2.773-1.917%200.068-0.067%200.12-0.12%200.141-0.14%200.114-0.113%201.153-1.106%202.447-1.106%200.745%200%201.477%200.34%202.175%201.010%200.828%200.795%201.256%201.579%201.27%202.331%200.014%200.768-0.404%201.595-1.24%202.458z'%3e%3c/path%3e%3c/svg%3e",Eg="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20width='800px'%20height='800px'%20viewBox='0%200%2024%2024'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M3%2018h7v1H2V2h17v7h-1V3H3zm15.917%200h-4.834l-1.756%204h-1.093l4.808-10.951h.916L21.766%2022h-1.093zm-.439-1L16.5%2012.494%2014.522%2017z'/%3e%3cpath%20fill='none'%20d='M0%200h24v24H0z'/%3e%3c/svg%3e",kg="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20width='800px'%20height='800px'%20viewBox='0%200%2025%2025'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M4.56189%2013.5L4.14285%2013.9294L4.5724%2014.3486L4.99144%2013.9189L4.56189%2013.5ZM9.92427%2015.9243L15.9243%209.92427L15.0757%209.07574L9.07574%2015.0757L9.92427%2015.9243ZM9.07574%209.92426L15.0757%2015.9243L15.9243%2015.0757L9.92426%209.07574L9.07574%209.92426ZM19.9%2012.5C19.9%2016.5869%2016.5869%2019.9%2012.5%2019.9V21.1C17.2496%2021.1%2021.1%2017.2496%2021.1%2012.5H19.9ZM5.1%2012.5C5.1%208.41309%208.41309%205.1%2012.5%205.1V3.9C7.75035%203.9%203.9%207.75035%203.9%2012.5H5.1ZM12.5%205.1C16.5869%205.1%2019.9%208.41309%2019.9%2012.5H21.1C21.1%207.75035%2017.2496%203.9%2012.5%203.9V5.1ZM5.15728%2013.4258C5.1195%2013.1227%205.1%2012.8138%205.1%2012.5H3.9C3.9%2012.8635%203.92259%2013.2221%203.9665%2013.5742L5.15728%2013.4258ZM12.5%2019.9C9.9571%2019.9%207.71347%2018.6179%206.38048%2016.6621L5.38888%2017.3379C6.93584%2019.6076%209.54355%2021.1%2012.5%2021.1V19.9ZM4.99144%2013.9189L7.42955%2011.4189L6.57045%2010.5811L4.13235%2013.0811L4.99144%2013.9189ZM4.98094%2013.0706L2.41905%2010.5706L1.58095%2011.4294L4.14285%2013.9294L4.98094%2013.0706Z'%20fill='%23121923'/%3e%3c/svg%3e",Og="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20width='800px'%20height='800px'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M5.50506%2011.4096L6.03539%2011.9399L5.50506%2011.4096ZM3%2014.9522H2.25H3ZM12.5904%2018.4949L12.0601%2017.9646L12.5904%2018.4949ZM9.04776%2021V21.75V21ZM11.4096%205.50506L10.8792%204.97473L11.4096%205.50506ZM13.241%2017.8444C13.5339%2018.1373%2014.0088%2018.1373%2014.3017%2017.8444C14.5946%2017.5515%2014.5946%2017.0766%2014.3017%2016.7837L13.241%2017.8444ZM7.21629%209.69832C6.9234%209.40543%206.44852%209.40543%206.15563%209.69832C5.86274%209.99122%205.86274%2010.4661%206.15563%2010.759L7.21629%209.69832ZM16.073%2016.073C16.3659%2015.7801%2016.3659%2015.3053%2016.073%2015.0124C15.7801%2014.7195%2015.3053%2014.7195%2015.0124%2015.0124L16.073%2016.073ZM18.4676%2011.5559C18.1759%2011.8499%2018.1777%2012.3248%2018.4718%2012.6165C18.7658%2012.9083%2019.2407%2012.9064%2019.5324%2012.6124L18.4676%2011.5559ZM6.03539%2011.9399L11.9399%206.03539L10.8792%204.97473L4.97473%2010.8792L6.03539%2011.9399ZM6.03539%2017.9646C5.18538%2017.1146%204.60235%2016.5293%204.22253%2016.0315C3.85592%2015.551%203.75%2015.2411%203.75%2014.9522H2.25C2.25%2015.701%202.56159%2016.3274%203.03%2016.9414C3.48521%2017.538%204.1547%2018.2052%204.97473%2019.0253L6.03539%2017.9646ZM4.97473%2010.8792C4.1547%2011.6993%203.48521%2012.3665%203.03%2012.9631C2.56159%2013.577%202.25%2014.2035%202.25%2014.9522H3.75C3.75%2014.6633%203.85592%2014.3535%204.22253%2013.873C4.60235%2013.3752%205.18538%2012.7899%206.03539%2011.9399L4.97473%2010.8792ZM12.0601%2017.9646C11.2101%2018.8146%2010.6248%2019.3977%2010.127%2019.7775C9.64651%2020.1441%209.33665%2020.25%209.04776%2020.25V21.75C9.79649%2021.75%2010.423%2021.4384%2011.0369%2020.97C11.6335%2020.5148%2012.3008%2019.8453%2013.1208%2019.0253L12.0601%2017.9646ZM4.97473%2019.0253C5.79476%2019.8453%206.46201%2020.5148%207.05863%2020.97C7.67256%2021.4384%208.29902%2021.75%209.04776%2021.75V20.25C8.75886%2020.25%208.449%2020.1441%207.9685%2019.7775C7.47069%2019.3977%206.88541%2018.8146%206.03539%2017.9646L4.97473%2019.0253ZM17.9646%206.03539C18.8146%206.88541%2019.3977%207.47069%2019.7775%207.9685C20.1441%208.449%2020.25%208.75886%2020.25%209.04776H21.75C21.75%208.29902%2021.4384%207.67256%2020.97%207.05863C20.5148%206.46201%2019.8453%205.79476%2019.0253%204.97473L17.9646%206.03539ZM19.0253%204.97473C18.2052%204.1547%2017.538%203.48521%2016.9414%203.03C16.3274%202.56159%2015.701%202.25%2014.9522%202.25V3.75C15.2411%203.75%2015.551%203.85592%2016.0315%204.22253C16.5293%204.60235%2017.1146%205.18538%2017.9646%206.03539L19.0253%204.97473ZM11.9399%206.03539C12.7899%205.18538%2013.3752%204.60235%2013.873%204.22253C14.3535%203.85592%2014.6633%203.75%2014.9522%203.75V2.25C14.2035%202.25%2013.577%202.56159%2012.9631%203.03C12.3665%203.48521%2011.6993%204.1547%2010.8792%204.97473L11.9399%206.03539ZM14.3017%2016.7837L7.21629%209.69832L6.15563%2010.759L13.241%2017.8444L14.3017%2016.7837ZM15.0124%2015.0124L12.0601%2017.9646L13.1208%2019.0253L16.073%2016.073L15.0124%2015.0124ZM19.5324%2012.6124C20.1932%2011.9464%2020.7384%2011.3759%2021.114%2010.8404C21.5023%2010.2869%2021.75%209.71511%2021.75%209.04776H20.25C20.25%209.30755%2020.1644%209.58207%2019.886%209.979C19.5949%2010.394%2019.1401%2010.8781%2018.4676%2011.5559L19.5324%2012.6124Z'%20fill='%231C274C'/%3e%3cpath%20d='M9%2021H21'%20stroke='%231C274C'%20stroke-width='1.5'%20stroke-linecap='round'/%3e%3c/svg%3e",Mg="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20fill='%23000000'%20width='800px'%20height='800px'%20viewBox='0%200%2032%2032'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%3e%3ctitle%3ecrop%3c/title%3e%3cpath%20d='M0%2028v4h4v-1.984h-1.984v-2.016h-2.016zM0%204h2.016v-1.984h1.984v-2.016h-4v4zM4%2028h24v-24h-24v24zM8%2024v-16h16v16h-16zM10.016%2022.016h2.656q-0.352-0.16-0.768-0.608t-1.056-1.28-0.832-1.12v3.008zM10.016%2012q0%200.832%200.576%201.44t1.408%200.576%201.408-0.576%200.608-1.44-0.608-1.408-1.408-0.576-1.408%200.576-0.576%201.408zM13.216%2022.016h8.8v-7.328q-0.48-0.512-0.96-0.608t-0.992%200.16-0.96%200.8-1.024%201.184-0.992%201.408-1.024%201.472-0.96%201.344-0.96%201.024-0.928%200.544zM28%2030.016v1.984h4v-4h-1.984v2.016h-2.016zM28%202.016h2.016v1.984h1.984v-4h-4v2.016z'%3e%3c/path%3e%3c/svg%3e",Dg="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20fill='%23000000'%20width='800px'%20height='800px'%20viewBox='0%200%2030%2030'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M19.5%209c-.492-.004-.916.242-1.092.47l-2.737%203.457c-.17.208-.55.073-.727-.03l-2.455-1.547c-.29-.19-.62-.35-.988-.35-.38%200-.786.114-1.072.434l-3.293%203.724c-.445.498.3%201.166.746.668l3.294-3.724c.218-.234.535-.05.765.084l2.46%201.552.012.006c.306.19.65.252.988.256.34.004.71-.027.985-.36l2.767-3.5c.217-.263.534-.14.744.04l2.254%201.688c.527.477%201.205-.375.62-.78l-2.252-1.69C20.252%209.188%2019.913%209%2019.5%209zm-12%208h15c.277%200%20.5.223.5.5s-.223.5-.5.5h-15c-.277%200-.5-.223-.5-.5s.223-.5.5-.5zM11%205c-1.1%200-2%20.9-2%202s.9%202%202%202%202-.9%202-2-.9-2-2-2zm0%201c.558%200%201%20.442%201%201s-.442%201-1%201-1-.442-1-1%20.442-1%201-1zm14%2019.5a.5.5%200%200%201-.5.5.5.5%200%200%201-.5-.5.5.5%200%200%201%20.5-.5.5.5%200%200%201%20.5.5zm-19%200a.5.5%200%200%201-.5.5.5.5%200%200%201-.5-.5.5.5%200%200%201%20.5-.5.5.5%200%200%201%20.5.5zm9-1.5c-1.1%200-2%20.9-2%202s.9%202%202%202%202-.9%202-2-.9-2-2-2zm0%201c.563%200%201%20.437%201%201s-.437%201-1%201-1-.437-1-1%20.437-1%201-1zM26.5%203c-.665%200-.648%201%200%201h2c.286%200%20.5.214.5.5v14c0%20.286-.214.5-.5.5h-2c-.654%200-.66%201%200%201h2c.822%200%201.5-.678%201.5-1.5v-14c0-.822-.678-1.5-1.5-1.5zm-25%200C.678%203%200%203.678%200%204.5v14c0%20.822.678%201.5%201.5%201.5h2c.66%200%20.665-1%200-1h-2c-.286%200-.5-.214-.5-.5v-14c0-.286.214-.5.5-.5h2c.66%200%20.66-1%200-1zm5-1C5.678%202%205%202.678%205%203.5v16c0%20.822.678%201.5%201.5%201.5h17c.822%200%201.5-.678%201.5-1.5v-16c0-.822-.678-1.5-1.5-1.5zm0%201h17c.286%200%20.5.214.5.5v16c0%20.286-.214.5-.5.5h-17c-.286%200-.5-.214-.5-.5v-16c0-.286.214-.5.5-.5z'/%3e%3c/svg%3e",Ag={"AMSTEEL-BLUE":{External:{files:["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]},Internal:{files:["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]}},"K-100":{External:{files:["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]},Internal:{files:["<EMAIL>","<EMAIL>","<EMAIL>"]}},"SATURN-12":{External:{files:["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]},Internal:{files:["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]}},TENEX:{External:{files:["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]}}},ii=class ii{constructor(t,e,i){M(this,"container");this.category=t,this.type=e,this.container=document.createElement("div"),this.container.className="image-viewer-container",this.container.id="myImageViewer",this.container.innerHTML=this.generateImageViewerHTML(),this.addEventListeners(),this.ensureImageScaleInView(),console.log(i==null?void 0:i.getImageElement())}generateImageViewerHTML(){var s;return`
            <div class="image-scroller">
                ${(((s=Ag[this.category][this.type])==null?void 0:s.files)||[]).map(n=>`<img src="assets/RatingImages/${this.category}/${this.type}/${n}" alt="${n}" onclick="()=> { console.log('image clicked'); }">`).join("")}
            </div>
            <button class="close-button">Close</button>
        `}addEventListeners(){const t=this.container,e=t.querySelectorAll("img"),i=this.container.querySelector(".close-button");let s=!1,n={x:0,y:0};const o=(l,c)=>{s=!0,n.x=l-this.container.offsetLeft,n.y=c-this.container.offsetTop,this.container.style.cursor="grabbing"},a=(l,c)=>{if(!s)return;const u=l-n.x,d=c-n.y,g=this.container.getBoundingClientRect(),f=window.innerWidth,p=window.innerHeight;(g.left>=0||u>g.left)&&(this.container.style.left=`${u}px`),(g.top>=0||d>g.top)&&(this.container.style.top=`${d}px`),(g.right<=f||u<g.left)&&(this.container.style.left=`${u}px`),(g.bottom<=p||d<g.top)&&(this.container.style.top=`${d}px`)},h=()=>{s=!1,this.container.style.cursor="move"};t.addEventListener("mousedown",l=>{s=!0,t.style.cursor="grabbing",l.preventDefault()}),document.addEventListener("mousemove",l=>{if(!s)return;let c=l.clientX-n.x,u=l.clientY-n.y;const d=window.innerWidth,g=window.innerHeight,f=document.querySelector(".bottom-toolbar"),p=f?f.offsetHeight:0;c+t.offsetWidth>d&&(c=d-t.offsetWidth),u+t.offsetHeight>g-p&&(u=g-p-t.offsetHeight),t.style.left=`${c}px`,t.style.top=`${u}px`}),document.addEventListener("mouseup",l=>{l.preventDefault(),h()}),document.addEventListener("mouseleave",()=>{s&&(s=!1,t.style.cursor="grab")}),this.container.addEventListener("touchstart",l=>{if(l.target===this.container&&l.touches.length===1){l.preventDefault(),l.stopPropagation();const c=l.touches[0];o(c.clientX,c.clientY)}}),document.addEventListener("touchmove",l=>{if(s&&l.touches.length===1){l.preventDefault();const c=l.touches[0];a(c.clientX,c.clientY)}}),document.addEventListener("touchend",l=>{s&&(l.preventDefault(),h())}),i.addEventListener("click",()=>{this.container.style.display="none"}),e.forEach((l,c)=>{l.addEventListener("click",()=>{e.forEach(u=>u.classList.remove("selected")),l.classList.add("selected"),console.log("Image index:",c+1)})}),t.style.position="absolute",t.style.cursor="grab",t.style.top="0px",t.style.left="0px"}ensureImageScaleInView(){const t=this.container.getBoundingClientRect(),e=window.innerWidth,i=window.innerHeight;t.right>e&&(this.container.style.left=`${e-t.width}px`),t.bottom>i&&(this.container.style.top=`${i-t.height}px`),t.left<0&&(this.container.style.left="0px"),t.top<0&&(this.container.style.top="0px")}show(){this.container.style.display="flex",document.body.appendChild(this.container)}hide(){this.container.remove()}onClose(t){this.container.querySelector(".close-button").addEventListener("click",()=>{t()})}static getInstance(t,e,i){return ii.instance||(ii.instance=new ii(t,e,i)),ii.instance}};M(ii,"instance");let yn=ii;/*!
 * iro.js v5.5.2
 * 2016-2021 James Daniel
 * Licensed under MPL 2.0
 * github.com/jaames/iro.js
 */var K,bn,pl,Kr,ml,Ae={},Zr=[],Pg=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i;function Pe(r,t){for(var e in t)r[e]=t[e];return r}function vl(r){var t=r.parentNode;t&&t.removeChild(r)}function X(r,t,e){var i,s,n,o,a=arguments;if(t=Pe({},t),arguments.length>3)for(e=[e],i=3;i<arguments.length;i++)e.push(a[i]);if(e!=null&&(t.children=e),r!=null&&r.defaultProps!=null)for(s in r.defaultProps)t[s]===void 0&&(t[s]=r.defaultProps[s]);return o=t.key,(n=t.ref)!=null&&delete t.ref,o!=null&&delete t.key,Jr(r,t,o,n)}function Jr(r,t,e,i){var s={type:r,props:t,key:e,ref:i,__k:null,__p:null,__b:0,__e:null,l:null,__c:null,constructor:void 0};return K.vnode&&K.vnode(s),s}function Cn(r){return r.children}function Lg(r){if(r==null||typeof r=="boolean")return null;if(typeof r=="string"||typeof r=="number")return Jr(null,r,null,null);if(r.__e!=null||r.__c!=null){var t=Jr(r.type,r.props,r.key,null);return t.__e=r.__e,t}return r}function ki(r,t){this.props=r,this.context=t}function wn(r,t){if(t==null)return r.__p?wn(r.__p,r.__p.__k.indexOf(r)+1):null;for(var e;t<r.__k.length;t++)if((e=r.__k[t])!=null&&e.__e!=null)return e.__e;return typeof r.type=="function"?wn(r):null}function yl(r){var t,e;if((r=r.__p)!=null&&r.__c!=null){for(r.__e=r.__c.base=null,t=0;t<r.__k.length;t++)if((e=r.__k[t])!=null&&e.__e!=null){r.__e=r.__c.base=e.__e;break}return yl(r)}}function Qr(r){(!r.__d&&(r.__d=!0)&&bn.push(r)===1||Kr!==K.debounceRendering)&&(Kr=K.debounceRendering,(K.debounceRendering||pl)(Rg))}function Rg(){var r,t,e,i,s,n,o,a;for(bn.sort(function(h,l){return l.__v.__b-h.__v.__b});r=bn.pop();)r.__d&&(e=void 0,i=void 0,n=(s=(t=r).__v).__e,o=t.__P,a=t.u,t.u=!1,o&&(e=[],i=eo(o,s,Pe({},s),t.__n,o.ownerSVGElement!==void 0,null,e,a,n??wn(s)),_l(e,s),i!=n&&yl(s)))}function bl(r,t,e,i,s,n,o,a,h){var l,c,u,d,g,f,p,v=e&&e.__k||Zr,b=v.length;if(a==Ae&&(a=n!=null?n[0]:b?wn(e,0):null),l=0,t.__k=to(t.__k,function(w){if(w!=null){if(w.__p=t,w.__b=t.__b+1,(u=v[l])===null||u&&w.key==u.key&&w.type===u.type)v[l]=void 0;else for(c=0;c<b;c++){if((u=v[c])&&w.key==u.key&&w.type===u.type){v[c]=void 0;break}u=null}if(d=eo(r,w,u=u||Ae,i,s,n,o,null,a,h),(c=w.ref)&&u.ref!=c&&(p||(p=[])).push(c,w.__c||d,w),d!=null){if(f==null&&(f=d),w.l!=null)d=w.l,w.l=null;else if(n==u||d!=a||d.parentNode==null){t:if(a==null||a.parentNode!==r)r.appendChild(d);else{for(g=a,c=0;(g=g.nextSibling)&&c<b;c+=2)if(g==d)break t;r.insertBefore(d,a)}t.type=="option"&&(r.value="")}a=d.nextSibling,typeof t.type=="function"&&(t.l=d)}}return l++,w}),t.__e=f,n!=null&&typeof t.type!="function")for(l=n.length;l--;)n[l]!=null&&vl(n[l]);for(l=b;l--;)v[l]!=null&&Tl(v[l],v[l]);if(p)for(l=0;l<p.length;l++)Sl(p[l],p[++l],p[++l])}function to(r,t,e){if(e==null&&(e=[]),r==null||typeof r=="boolean")t&&e.push(t(null));else if(Array.isArray(r))for(var i=0;i<r.length;i++)to(r[i],t,e);else e.push(t?t(Lg(r)):r);return e}function Ig(r,t,e,i,s){var n;for(n in e)n in t||wl(r,n,null,e[n],i);for(n in t)s&&typeof t[n]!="function"||n==="value"||n==="checked"||e[n]===t[n]||wl(r,n,t[n],e[n],i)}function Cl(r,t,e){t[0]==="-"?r.setProperty(t,e):r[t]=typeof e=="number"&&Pg.test(t)===!1?e+"px":e??""}function wl(r,t,e,i,s){var n,o,a,h,l;if(!((t=s?t==="className"?"class":t:t==="class"?"className":t)==="key"||t==="children"))if(t==="style")if(n=r.style,typeof e=="string")n.cssText=e;else{if(typeof i=="string"&&(n.cssText="",i=null),i)for(o in i)e&&o in e||Cl(n,o,"");if(e)for(a in e)i&&e[a]===i[a]||Cl(n,a,e[a])}else t[0]==="o"&&t[1]==="n"?(h=t!==(t=t.replace(/Capture$/,"")),l=t.toLowerCase(),t=(l in r?l:t).slice(2),e?(i||r.addEventListener(t,xl,h),(r.t||(r.t={}))[t]=e):r.removeEventListener(t,xl,h)):t!=="list"&&t!=="tagName"&&t!=="form"&&!s&&t in r?r[t]=e??"":typeof e!="function"&&t!=="dangerouslySetInnerHTML"&&(t!==(t=t.replace(/^xlink:?/,""))?e==null||e===!1?r.removeAttributeNS("http://www.w3.org/1999/xlink",t.toLowerCase()):r.setAttributeNS("http://www.w3.org/1999/xlink",t.toLowerCase(),e):e==null||e===!1?r.removeAttribute(t):r.setAttribute(t,e))}function xl(r){return this.t[r.type](K.event?K.event(r):r)}function eo(r,t,e,i,s,n,o,a,h,l){var c,u,d,g,f,p,v,b,w,x,_=t.type;if(t.constructor!==void 0)return null;(c=K.__b)&&c(t);try{t:if(typeof _=="function"){if(b=t.props,w=(c=_.contextType)&&i[c.__c],x=c?w?w.props.value:c.__p:i,e.__c?v=(u=t.__c=e.__c).__p=u.__E:("prototype"in _&&_.prototype.render?t.__c=u=new _(b,x):(t.__c=u=new ki(b,x),u.constructor=_,u.render=Bg),w&&w.sub(u),u.props=b,u.state||(u.state={}),u.context=x,u.__n=i,d=u.__d=!0,u.__h=[]),u.__s==null&&(u.__s=u.state),_.getDerivedStateFromProps!=null&&Pe(u.__s==u.state?u.__s=Pe({},u.__s):u.__s,_.getDerivedStateFromProps(b,u.__s)),d)_.getDerivedStateFromProps==null&&u.componentWillMount!=null&&u.componentWillMount(),u.componentDidMount!=null&&o.push(u);else{if(_.getDerivedStateFromProps==null&&a==null&&u.componentWillReceiveProps!=null&&u.componentWillReceiveProps(b,x),!a&&u.shouldComponentUpdate!=null&&u.shouldComponentUpdate(b,u.__s,x)===!1){for(u.props=b,u.state=u.__s,u.__d=!1,u.__v=t,t.__e=h!=null?h!==e.__e?h:e.__e:null,t.__k=e.__k,c=0;c<t.__k.length;c++)t.__k[c]&&(t.__k[c].__p=t);break t}u.componentWillUpdate!=null&&u.componentWillUpdate(b,u.__s,x)}for(g=u.props,f=u.state,u.context=x,u.props=b,u.state=u.__s,(c=K.__r)&&c(t),u.__d=!1,u.__v=t,u.__P=r,c=u.render(u.props,u.state,u.context),t.__k=to(c!=null&&c.type==Cn&&c.key==null?c.props.children:c),u.getChildContext!=null&&(i=Pe(Pe({},i),u.getChildContext())),d||u.getSnapshotBeforeUpdate==null||(p=u.getSnapshotBeforeUpdate(g,f)),bl(r,t,e,i,s,n,o,h,l),u.base=t.__e;c=u.__h.pop();)u.__s&&(u.state=u.__s),c.call(u);d||g==null||u.componentDidUpdate==null||u.componentDidUpdate(g,f,p),v&&(u.__E=u.__p=null)}else t.__e=jg(e.__e,t,e,i,s,n,o,l);(c=K.diffed)&&c(t)}catch(k){K.__e(k,t,e)}return t.__e}function _l(r,t){for(var e;e=r.pop();)try{e.componentDidMount()}catch(i){K.__e(i,e.__v)}K.__c&&K.__c(t)}function jg(r,t,e,i,s,n,o,a){var h,l,c,u,d=e.props,g=t.props;if(s=t.type==="svg"||s,r==null&&n!=null){for(h=0;h<n.length;h++)if((l=n[h])!=null&&(t.type===null?l.nodeType===3:l.localName===t.type)){r=l,n[h]=null;break}}if(r==null){if(t.type===null)return document.createTextNode(g);r=s?document.createElementNS("http://www.w3.org/2000/svg",t.type):document.createElement(t.type),n=null}return t.type===null?d!==g&&(n!=null&&(n[n.indexOf(r)]=null),r.data=g):t!==e&&(n!=null&&(n=Zr.slice.call(r.childNodes)),c=(d=e.props||Ae).dangerouslySetInnerHTML,u=g.dangerouslySetInnerHTML,a||(u||c)&&(u&&c&&u.__html==c.__html||(r.innerHTML=u&&u.__html||"")),Ig(r,g,d,s,a),t.__k=t.props.children,u||bl(r,t,e,i,t.type!=="foreignObject"&&s,n,o,Ae,a),a||("value"in g&&g.value!==void 0&&g.value!==r.value&&(r.value=g.value==null?"":g.value),"checked"in g&&g.checked!==void 0&&g.checked!==r.checked&&(r.checked=g.checked))),r}function Sl(r,t,e){try{typeof r=="function"?r(t):r.current=t}catch(i){K.__e(i,e)}}function Tl(r,t,e){var i,s,n;if(K.unmount&&K.unmount(r),(i=r.ref)&&Sl(i,null,t),e||typeof r.type=="function"||(e=(s=r.__e)!=null),r.__e=r.l=null,(i=r.__c)!=null){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(o){K.__e(o,t)}i.base=i.__P=null}if(i=r.__k)for(n=0;n<i.length;n++)i[n]&&Tl(i[n],t,e);s!=null&&vl(s)}function Bg(r,t,e){return this.constructor(r,e)}function Fg(r,t,e){var i,s,n;K.__p&&K.__p(r,t),s=(i=e===ml)?null:t.__k,r=X(Cn,null,[r]),n=[],eo(t,t.__k=r,s||Ae,Ae,t.ownerSVGElement!==void 0,s?null:Zr.slice.call(t.childNodes),n,!1,Ae,i),_l(n,r)}K={},ki.prototype.setState=function(r,t){var e=this.__s!==this.state&&this.__s||(this.__s=Pe({},this.state));(typeof r!="function"||(r=r(e,this.props)))&&Pe(e,r),r!=null&&this.__v&&(this.u=!1,t&&this.__h.push(t),Qr(this))},ki.prototype.forceUpdate=function(r){this.__v&&(r&&this.__h.push(r),this.u=!0,Qr(this))},ki.prototype.render=Cn,bn=[],pl=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Kr=K.debounceRendering,K.__e=function(r,t,e){for(var i;t=t.__p;)if((i=t.__c)&&!i.__p)try{if(i.constructor&&i.constructor.getDerivedStateFromError!=null)i.setState(i.constructor.getDerivedStateFromError(r));else{if(i.componentDidCatch==null)continue;i.componentDidCatch(r)}return Qr(i.__E=i)}catch(s){r=s}throw r},ml=Ae;function $g(r,t){for(var e=0;e<t.length;e++){var i=t[e];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(r,i.key,i)}}function zg(r,t,e){return t&&$g(r.prototype,t),r}function St(){return St=Object.assign||function(r){for(var t=arguments,e=1;e<arguments.length;e++){var i=t[e];for(var s in i)Object.prototype.hasOwnProperty.call(i,s)&&(r[s]=i[s])}return r},St.apply(this,arguments)}var Hg="[-\\+]?\\d+%?",Ng="[-\\+]?\\d*\\.\\d+%?",qe="(?:"+Ng+")|(?:"+Hg+")",El="[\\s|\\(]+("+qe+")[,|\\s]+("+qe+")[,|\\s]+("+qe+")\\s*\\)?",kl="[\\s|\\(]+("+qe+")[,|\\s]+("+qe+")[,|\\s]+("+qe+")[,|\\s]+("+qe+")\\s*\\)?",Wg=new RegExp("rgb"+El),Vg=new RegExp("rgba"+kl),Xg=new RegExp("hsl"+El),Yg=new RegExp("hsla"+kl),xn="^(?:#?|0x?)",Ke="([0-9a-fA-F]{1})",Ze="([0-9a-fA-F]{2})",Gg=new RegExp(xn+Ke+Ke+Ke+"$"),Ug=new RegExp(xn+Ke+Ke+Ke+Ke+"$"),qg=new RegExp(xn+Ze+Ze+Ze+"$"),Kg=new RegExp(xn+Ze+Ze+Ze+Ze+"$"),Zg=2e3,Jg=4e4,_n=Math.log,Oi=Math.round,is=Math.floor;function Ht(r,t,e){return Math.min(Math.max(r,t),e)}function Tt(r,t){var e=r.indexOf("%")>-1,i=parseFloat(r);return e?t/100*i:i}function Et(r){return parseInt(r,16)}function Je(r){return r.toString(16).padStart(2,"0")}var Mi=function(){function r(e,i){this.$={h:0,s:0,v:0,a:1},e&&this.set(e),this.onChange=i,this.initialValue=St({},this.$)}var t=r.prototype;return t.set=function(i){if(typeof i=="string")/^(?:#?|0x?)[0-9a-fA-F]{3,8}$/.test(i)?this.hexString=i:/^rgba?/.test(i)?this.rgbString=i:/^hsla?/.test(i)&&(this.hslString=i);else if(typeof i=="object")i instanceof r?this.hsva=i.hsva:"r"in i&&"g"in i&&"b"in i?this.rgb=i:"h"in i&&"s"in i&&"v"in i?this.hsv=i:"h"in i&&"s"in i&&"l"in i?this.hsl=i:"kelvin"in i&&(this.kelvin=i.kelvin);else throw new Error("Invalid color value")},t.setChannel=function(i,s,n){var o;this[i]=St({},this[i],(o={},o[s]=n,o))},t.reset=function(){this.hsva=this.initialValue},t.clone=function(){return new r(this)},t.unbind=function(){this.onChange=void 0},r.hsvToRgb=function(i){var s=i.h/60,n=i.s/100,o=i.v/100,a=is(s),h=s-a,l=o*(1-n),c=o*(1-h*n),u=o*(1-(1-h)*n),d=a%6,g=[o,c,l,l,u,o][d],f=[u,o,o,c,l,l][d],p=[l,l,u,o,o,c][d];return{r:Ht(g*255,0,255),g:Ht(f*255,0,255),b:Ht(p*255,0,255)}},r.rgbToHsv=function(i){var s=i.r/255,n=i.g/255,o=i.b/255,a=Math.max(s,n,o),h=Math.min(s,n,o),l=a-h,c=0,u=a,d=a===0?0:l/a;switch(a){case h:c=0;break;case s:c=(n-o)/l+(n<o?6:0);break;case n:c=(o-s)/l+2;break;case o:c=(s-n)/l+4;break}return{h:c*60%360,s:Ht(d*100,0,100),v:Ht(u*100,0,100)}},r.hsvToHsl=function(i){var s=i.s/100,n=i.v/100,o=(2-s)*n,a=o<=1?o:2-o,h=a<1e-9?0:s*n/a;return{h:i.h,s:Ht(h*100,0,100),l:Ht(o*50,0,100)}},r.hslToHsv=function(i){var s=i.l*2,n=i.s*(s<=100?s:200-s)/100,o=s+n<1e-9?0:2*n/(s+n);return{h:i.h,s:Ht(o*100,0,100),v:Ht((s+n)/2,0,100)}},r.kelvinToRgb=function(i){var s=i/100,n,o,a;return s<66?(n=255,o=-155.25485562709179-.44596950469579133*(o=s-2)+104.49216199393888*_n(o),a=s<20?0:-254.76935184120902+.8274096064007395*(a=s-10)+115.67994401066147*_n(a)):(n=351.97690566805693+.114206453784165*(n=s-55)-40.25366309332127*_n(n),o=325.4494125711974+.07943456536662342*(o=s-50)-28.0852963507957*_n(o),a=255),{r:Ht(is(n),0,255),g:Ht(is(o),0,255),b:Ht(is(a),0,255)}},r.rgbToKelvin=function(i){for(var s=i.r,n=i.b,o=.4,a=Zg,h=Jg,l;h-a>o;){l=(h+a)*.5;var c=r.kelvinToRgb(l);c.b/c.r>=n/s?h=l:a=l}return l},zg(r,[{key:"hsv",get:function(){var i=this.$;return{h:i.h,s:i.s,v:i.v}},set:function(i){var s=this.$;if(i=St({},s,i),this.onChange){var n={h:!1,v:!1,s:!1,a:!1};for(var o in s)n[o]=i[o]!=s[o];this.$=i,(n.h||n.s||n.v||n.a)&&this.onChange(this,n)}else this.$=i}},{key:"hsva",get:function(){return St({},this.$)},set:function(i){this.hsv=i}},{key:"hue",get:function(){return this.$.h},set:function(i){this.hsv={h:i}}},{key:"saturation",get:function(){return this.$.s},set:function(i){this.hsv={s:i}}},{key:"value",get:function(){return this.$.v},set:function(i){this.hsv={v:i}}},{key:"alpha",get:function(){return this.$.a},set:function(i){this.hsv=St({},this.hsv,{a:i})}},{key:"kelvin",get:function(){return r.rgbToKelvin(this.rgb)},set:function(i){this.rgb=r.kelvinToRgb(i)}},{key:"red",get:function(){var i=this.rgb;return i.r},set:function(i){this.rgb=St({},this.rgb,{r:i})}},{key:"green",get:function(){var i=this.rgb;return i.g},set:function(i){this.rgb=St({},this.rgb,{g:i})}},{key:"blue",get:function(){var i=this.rgb;return i.b},set:function(i){this.rgb=St({},this.rgb,{b:i})}},{key:"rgb",get:function(){var i=r.hsvToRgb(this.$),s=i.r,n=i.g,o=i.b;return{r:Oi(s),g:Oi(n),b:Oi(o)}},set:function(i){this.hsv=St({},r.rgbToHsv(i),{a:i.a===void 0?1:i.a})}},{key:"rgba",get:function(){return St({},this.rgb,{a:this.alpha})},set:function(i){this.rgb=i}},{key:"hsl",get:function(){var i=r.hsvToHsl(this.$),s=i.h,n=i.s,o=i.l;return{h:Oi(s),s:Oi(n),l:Oi(o)}},set:function(i){this.hsv=St({},r.hslToHsv(i),{a:i.a===void 0?1:i.a})}},{key:"hsla",get:function(){return St({},this.hsl,{a:this.alpha})},set:function(i){this.hsl=i}},{key:"rgbString",get:function(){var i=this.rgb;return"rgb("+i.r+", "+i.g+", "+i.b+")"},set:function(i){var s,n,o,a,h=1;if((s=Wg.exec(i))?(n=Tt(s[1],255),o=Tt(s[2],255),a=Tt(s[3],255)):(s=Vg.exec(i))&&(n=Tt(s[1],255),o=Tt(s[2],255),a=Tt(s[3],255),h=Tt(s[4],1)),s)this.rgb={r:n,g:o,b:a,a:h};else throw new Error("Invalid rgb string")}},{key:"rgbaString",get:function(){var i=this.rgba;return"rgba("+i.r+", "+i.g+", "+i.b+", "+i.a+")"},set:function(i){this.rgbString=i}},{key:"hexString",get:function(){var i=this.rgb;return"#"+Je(i.r)+Je(i.g)+Je(i.b)},set:function(i){var s,n,o,a,h=255;if((s=Gg.exec(i))?(n=Et(s[1])*17,o=Et(s[2])*17,a=Et(s[3])*17):(s=Ug.exec(i))?(n=Et(s[1])*17,o=Et(s[2])*17,a=Et(s[3])*17,h=Et(s[4])*17):(s=qg.exec(i))?(n=Et(s[1]),o=Et(s[2]),a=Et(s[3])):(s=Kg.exec(i))&&(n=Et(s[1]),o=Et(s[2]),a=Et(s[3]),h=Et(s[4])),s)this.rgb={r:n,g:o,b:a,a:h/255};else throw new Error("Invalid hex string")}},{key:"hex8String",get:function(){var i=this.rgba;return"#"+Je(i.r)+Je(i.g)+Je(i.b)+Je(is(i.a*255))},set:function(i){this.hexString=i}},{key:"hslString",get:function(){var i=this.hsl;return"hsl("+i.h+", "+i.s+"%, "+i.l+"%)"},set:function(i){var s,n,o,a,h=1;if((s=Xg.exec(i))?(n=Tt(s[1],360),o=Tt(s[2],100),a=Tt(s[3],100)):(s=Yg.exec(i))&&(n=Tt(s[1],360),o=Tt(s[2],100),a=Tt(s[3],100),h=Tt(s[4],1)),s)this.hsl={h:n,s:o,l:a,a:h};else throw new Error("Invalid hsl string")}},{key:"hslaString",get:function(){var i=this.hsla;return"hsla("+i.h+", "+i.s+"%, "+i.l+"%, "+i.a+")"},set:function(i){this.hslString=i}}]),r}(),Qg={sliderShape:"bar",sliderType:"value",minTemperature:2200,maxTemperature:11e3};function io(r){var t,e=r.width,i=r.sliderSize,s=r.borderWidth,n=r.handleRadius,o=r.padding,a=r.sliderShape,h=r.layoutDirection==="horizontal";return i=(t=i)!=null?t:o*2+n*2,a==="circle"?{handleStart:r.padding+r.handleRadius,handleRange:e-o*2-n*2,width:e,height:e,cx:e/2,cy:e/2,radius:e/2-s/2}:{handleStart:i/2,handleRange:e-i,radius:i/2,x:0,y:0,width:h?i:e,height:h?e:i}}function t0(r,t){var e=t.hsva,i=t.rgb;switch(r.sliderType){case"red":return i.r/2.55;case"green":return i.g/2.55;case"blue":return i.b/2.55;case"alpha":return e.a*100;case"kelvin":var s=r.minTemperature,n=r.maxTemperature,o=n-s,a=(t.kelvin-s)/o*100;return Math.max(0,Math.min(a,100));case"hue":return e.h/=3.6;case"saturation":return e.s;case"value":default:return e.v}}function e0(r,t,e){var i=io(r),s=i.handleRange,n=i.handleStart,o;r.layoutDirection==="horizontal"?o=-1*e+s+n:o=t-n,o=Math.max(Math.min(o,s),0);var a=Math.round(100/s*o);switch(r.sliderType){case"kelvin":var h=r.minTemperature,l=r.maxTemperature,c=l-h;return h+c*(a/100);case"alpha":return a/100;case"hue":return a*3.6;case"red":case"blue":case"green":return a*2.55;default:return a}}function i0(r,t){var e=io(r),i=e.width,s=e.height,n=e.handleRange,o=e.handleStart,a=r.layoutDirection==="horizontal",h=t0(r,t),l=a?i/2:s/2,c=o+h/100*n;return a&&(c=-1*c+n+o*2),{x:a?l:c,y:a?c:l}}function s0(r,t){var e=t.hsv,i=t.rgb;switch(r.sliderType){case"red":return[[0,"rgb(0,"+i.g+","+i.b+")"],[100,"rgb(255,"+i.g+","+i.b+")"]];case"green":return[[0,"rgb("+i.r+",0,"+i.b+")"],[100,"rgb("+i.r+",255,"+i.b+")"]];case"blue":return[[0,"rgb("+i.r+","+i.g+",0)"],[100,"rgb("+i.r+","+i.g+",255)"]];case"alpha":return[[0,"rgba("+i.r+","+i.g+","+i.b+",0)"],[100,"rgb("+i.r+","+i.g+","+i.b+")"]];case"kelvin":for(var s=[],n=r.minTemperature,o=r.maxTemperature,a=8,h=o-n,l=n,c=0;l<o;l+=h/a,c+=1){var u=Mi.kelvinToRgb(l),d=u.r,g=u.g,f=u.b;s.push([100/a*c,"rgb("+d+","+g+","+f+")"])}return s;case"hue":return[[0,"#f00"],[16.666,"#ff0"],[33.333,"#0f0"],[50,"#0ff"],[66.666,"#00f"],[83.333,"#f0f"],[100,"#f00"]];case"saturation":var p=Mi.hsvToHsl({h:e.h,s:0,v:e.v}),v=Mi.hsvToHsl({h:e.h,s:100,v:e.v});return[[0,"hsl("+p.h+","+p.s+"%,"+p.l+"%)"],[100,"hsl("+v.h+","+v.s+"%,"+v.l+"%)"]];case"value":default:var b=Mi.hsvToHsl({h:e.h,s:e.s,v:100});return[[0,"#000"],[100,"hsl("+b.h+","+b.s+"%,"+b.l+"%)"]]}}var Ol=Math.PI*2,n0=function(t,e){return(t%e+e)%e},Ml=function(t,e){return Math.sqrt(t*t+e*e)};function Dl(r){return r.width/2-r.padding-r.handleRadius-r.borderWidth}function r0(r,t,e){var i=Sn(r),s=i.cx,n=i.cy,o=r.width/2;return Ml(s-t,n-e)<o}function Sn(r){var t=r.width/2;return{width:r.width,radius:t-r.borderWidth,cx:t,cy:t}}function Al(r,t,e){var i=r.wheelAngle,s=r.wheelDirection;return e&&s==="clockwise"?t=i+t:s==="clockwise"?t=360-i+t:e&&s==="anticlockwise"?t=i+180-t:s==="anticlockwise"&&(t=i-t),n0(t,360)}function o0(r,t){var e=t.hsv,i=Sn(r),s=i.cx,n=i.cy,o=Dl(r),a=(180+Al(r,e.h,!0))*(Ol/360),h=e.s/100*o,l=r.wheelDirection==="clockwise"?-1:1;return{x:s+h*Math.cos(a)*l,y:n+h*Math.sin(a)*l}}function Pl(r,t,e){var i=Sn(r),s=i.cx,n=i.cy,o=Dl(r);t=s-t,e=n-e;var a=Al(r,Math.atan2(-e,-t)*(360/Ol)),h=Math.min(Ml(t,e),o);return{h:Math.round(a),s:Math.round(100/o*h)}}function so(r){var t=r.width,e=r.boxHeight,i=r.padding,s=r.handleRadius;return{width:t,height:e??t,radius:i+s}}function Ll(r,t,e){var i=so(r),s=i.width,n=i.height,o=i.radius,a=o,h=s-o*2,l=n-o*2,c=(t-a)/h*100,u=(e-a)/l*100;return{s:Math.max(0,Math.min(c,100)),v:Math.max(0,Math.min(100-u,100))}}function a0(r,t){var e=so(r),i=e.width,s=e.height,n=e.radius,o=t.hsv,a=n,h=i-n*2,l=s-n*2;return{x:a+o.s/100*h,y:a+(l-o.v/100*l)}}function h0(r,t){var e=t.hue;return[[[0,"#fff"],[100,"hsl("+e+",100%,50%)"]],[[0,"rgba(0,0,0,0)"],[100,"#000"]]]}var no;function l0(r){no||(no=document.getElementsByTagName("base"));var t=window.navigator.userAgent,e=/^((?!chrome|android).)*safari/i.test(t),i=/iPhone|iPod|iPad/i.test(t),s=window.location;return(e||i)&&no.length>0?s.protocol+"//"+s.host+s.pathname+s.search+r:r}function Rl(r,t,e,i){for(var s=0;s<i.length;s++){var n=i[s].x-t,o=i[s].y-e,a=Math.sqrt(n*n+o*o);if(a<r.handleRadius)return s}return null}function ro(r){return{boxSizing:"border-box",border:r.borderWidth+"px solid "+r.borderColor}}function oo(r,t,e){return r+"-gradient("+t+", "+e.map(function(i){var s=i[0],n=i[1];return n+" "+s+"%"}).join(",")+")"}function bt(r){return typeof r=="string"?r:r+"px"}var c0={width:300,height:300,color:"#fff",colors:[],padding:6,layoutDirection:"vertical",borderColor:"#fff",borderWidth:0,handleRadius:8,activeHandleRadius:null,handleSvg:null,handleProps:{x:0,y:0},wheelLightness:!0,wheelAngle:0,wheelDirection:"anticlockwise",sliderSize:null,sliderMargin:12,boxHeight:null},Il=["mousemove","touchmove","mouseup","touchend"],Tn=function(r){function t(e){r.call(this,e),this.uid=(Math.random()+1).toString(36).substring(5)}return r&&(t.__proto__=r),t.prototype=Object.create(r&&r.prototype),t.prototype.constructor=t,t.prototype.render=function(i){var s=this.handleEvent.bind(this),n={onMouseDown:s,ontouchstart:s},o=i.layoutDirection==="horizontal",a=i.margin===null?i.sliderMargin:i.margin,h={overflow:"visible",display:o?"inline-block":"block"};return i.index>0&&(h[o?"marginLeft":"marginTop"]=a),X(Cn,null,i.children(this.uid,n,h))},t.prototype.handleEvent=function(i){var s=this,n=this.props.onInput,o=this.base.getBoundingClientRect();i.preventDefault();var a=i.touches?i.changedTouches[0]:i,h=a.clientX-o.left,l=a.clientY-o.top;switch(i.type){case"mousedown":case"touchstart":var c=n(h,l,0);c!==!1&&Il.forEach(function(u){document.addEventListener(u,s,{passive:!1})});break;case"mousemove":case"touchmove":n(h,l,1);break;case"mouseup":case"touchend":n(h,l,2),Il.forEach(function(u){document.removeEventListener(u,s,{passive:!1})});break}},t}(ki);function Qe(r){var t=r.r,e=r.url,i=t,s=t;return X("svg",{className:"IroHandle IroHandle--"+r.index+" "+(r.isActive?"IroHandle--isActive":""),style:{"-webkit-tap-highlight-color":"rgba(0, 0, 0, 0);",transform:"translate("+bt(r.x)+", "+bt(r.y)+")",willChange:"transform",top:bt(-t),left:bt(-t),width:bt(t*2),height:bt(t*2),position:"absolute",overflow:"visible"}},e&&X("use",Object.assign({xlinkHref:l0(e)},r.props)),!e&&X("circle",{cx:i,cy:s,r:t,fill:"none","stroke-width":2,stroke:"#000"}),!e&&X("circle",{cx:i,cy:s,r:t-2,fill:r.fill,"stroke-width":2,stroke:"#fff"}))}Qe.defaultProps={fill:"none",x:0,y:0,r:8,url:null,props:{x:0,y:0}};function En(r){var t=r.activeIndex,e=t!==void 0&&t<r.colors.length?r.colors[t]:r.color,i=io(r),s=i.width,n=i.height,o=i.radius,a=i0(r,e),h=s0(r,e);function l(c,u,d){var g=e0(r,c,u);r.parent.inputActive=!0,e[r.sliderType]=g,r.onInput(d,r.id)}return X(Tn,Object.assign({},r,{onInput:l}),function(c,u,d){return X("div",Object.assign({},u,{className:"IroSlider",style:Object.assign({},{position:"relative",width:bt(s),height:bt(n),borderRadius:bt(o),background:"conic-gradient(#ccc 25%, #fff 0 50%, #ccc 0 75%, #fff 0)",backgroundSize:"8px 8px"},d)}),X("div",{className:"IroSliderGradient",style:Object.assign({},{position:"absolute",top:0,left:0,width:"100%",height:"100%",borderRadius:bt(o),background:oo("linear",r.layoutDirection==="horizontal"?"to top":"to right",h)},ro(r))}),X(Qe,{isActive:!0,index:e.index,r:r.handleRadius,url:r.handleSvg,props:r.handleProps,x:a.x,y:a.y}))})}En.defaultProps=Object.assign({},Qg);function u0(r){var t=so(r),e=t.width,i=t.height,s=t.radius,n=r.colors,o=r.parent,a=r.activeIndex,h=a!==void 0&&a<r.colors.length?r.colors[a]:r.color,l=h0(r,h),c=n.map(function(d){return a0(r,d)});function u(d,g,f){if(f===0){var p=Rl(r,d,g,c);p!==null?o.setActiveColor(p):(o.inputActive=!0,h.hsv=Ll(r,d,g),r.onInput(f,r.id))}else f===1&&(o.inputActive=!0,h.hsv=Ll(r,d,g));r.onInput(f,r.id)}return X(Tn,Object.assign({},r,{onInput:u}),function(d,g,f){return X("div",Object.assign({},g,{className:"IroBox",style:Object.assign({},{width:bt(e),height:bt(i),position:"relative"},f)}),X("div",{className:"IroBox",style:Object.assign({},{width:"100%",height:"100%",borderRadius:bt(s)},ro(r),{background:oo("linear","to bottom",l[1])+","+oo("linear","to right",l[0])})}),n.filter(function(p){return p!==h}).map(function(p){return X(Qe,{isActive:!1,index:p.index,fill:p.hslString,r:r.handleRadius,url:r.handleSvg,props:r.handleProps,x:c[p.index].x,y:c[p.index].y})}),X(Qe,{isActive:!0,index:h.index,fill:h.hslString,r:r.activeHandleRadius||r.handleRadius,url:r.handleSvg,props:r.handleProps,x:c[h.index].x,y:c[h.index].y}))})}var d0="conic-gradient(red, yellow, lime, aqua, blue, magenta, red)",g0="conic-gradient(red, magenta, blue, aqua, lime, yellow, red)";function jl(r){var t=Sn(r),e=t.width,i=r.colors;r.borderWidth;var s=r.parent,n=r.color,o=n.hsv,a=i.map(function(c){return o0(r,c)}),h={position:"absolute",top:0,left:0,width:"100%",height:"100%",borderRadius:"50%",boxSizing:"border-box"};function l(c,u,d){if(d===0){if(!r0(r,c,u))return!1;var g=Rl(r,c,u,a);g!==null?s.setActiveColor(g):(s.inputActive=!0,n.hsv=Pl(r,c,u),r.onInput(d,r.id))}else d===1&&(s.inputActive=!0,n.hsv=Pl(r,c,u));r.onInput(d,r.id)}return X(Tn,Object.assign({},r,{onInput:l}),function(c,u,d){return X("div",Object.assign({},u,{className:"IroWheel",style:Object.assign({},{width:bt(e),height:bt(e),position:"relative"},d)}),X("div",{className:"IroWheelHue",style:Object.assign({},h,{transform:"rotateZ("+(r.wheelAngle+90)+"deg)",background:r.wheelDirection==="clockwise"?d0:g0})}),X("div",{className:"IroWheelSaturation",style:Object.assign({},h,{background:"radial-gradient(circle closest-side, #fff, transparent)"})}),r.wheelLightness&&X("div",{className:"IroWheelLightness",style:Object.assign({},h,{background:"#000",opacity:1-o.v/100})}),X("div",{className:"IroWheelBorder",style:Object.assign({},h,ro(r))}),i.filter(function(g){return g!==n}).map(function(g){return X(Qe,{isActive:!1,index:g.index,fill:g.hslString,r:r.handleRadius,url:r.handleSvg,props:r.handleProps,x:a[g.index].x,y:a[g.index].y})}),X(Qe,{isActive:!0,index:n.index,fill:n.hslString,r:r.activeHandleRadius||r.handleRadius,url:r.handleSvg,props:r.handleProps,x:a[n.index].x,y:a[n.index].y}))})}function f0(r){var t=function(e,i){var s,n=document.createElement("div");Fg(X(r,Object.assign({},{ref:function(a){return s=a}},i)),n);function o(){var a=e instanceof Element?e:document.querySelector(e);a.appendChild(s.base),s.onMount(a)}return document.readyState!=="loading"?o():document.addEventListener("DOMContentLoaded",o),s};return t.prototype=r.prototype,Object.assign(t,r),t.__component=r,t}var Bl=function(r){function t(e){var i=this;r.call(this,e),this.colors=[],this.inputActive=!1,this.events={},this.activeEvents={},this.deferredEvents={},this.id=e.id;var s=e.colors.length>0?e.colors:[e.color];s.forEach(function(n){return i.addColor(n)}),this.setActiveColor(0),this.state=Object.assign({},e,{color:this.color,colors:this.colors,layout:e.layout})}return r&&(t.__proto__=r),t.prototype=Object.create(r&&r.prototype),t.prototype.constructor=t,t.prototype.addColor=function(i,s){s===void 0&&(s=this.colors.length);var n=new Mi(i,this.onColorChange.bind(this));this.colors.splice(s,0,n),this.colors.forEach(function(o,a){return o.index=a}),this.state&&this.setState({colors:this.colors}),this.deferredEmit("color:init",n)},t.prototype.removeColor=function(i){var s=this.colors.splice(i,1)[0];s.unbind(),this.colors.forEach(function(n,o){return n.index=o}),this.state&&this.setState({colors:this.colors}),s.index===this.color.index&&this.setActiveColor(0),this.emit("color:remove",s)},t.prototype.setActiveColor=function(i){this.color=this.colors[i],this.state&&this.setState({color:this.color}),this.emit("color:setActive",this.color)},t.prototype.setColors=function(i,s){var n=this;s===void 0&&(s=0),this.colors.forEach(function(o){return o.unbind()}),this.colors=[],i.forEach(function(o){return n.addColor(o)}),this.setActiveColor(s),this.emit("color:setAll",this.colors)},t.prototype.on=function(i,s){var n=this,o=this.events;(Array.isArray(i)?i:[i]).forEach(function(a){(o[a]||(o[a]=[])).push(s),n.deferredEvents[a]&&(n.deferredEvents[a].forEach(function(h){s.apply(null,h)}),n.deferredEvents[a]=[])})},t.prototype.off=function(i,s){var n=this;(Array.isArray(i)?i:[i]).forEach(function(o){var a=n.events[o];a&&a.splice(a.indexOf(s),1)})},t.prototype.emit=function(i){for(var s=this,n=[],o=arguments.length-1;o-- >0;)n[o]=arguments[o+1];var a=this.activeEvents,h=a.hasOwnProperty(i)?a[i]:!1;if(!h){a[i]=!0;var l=this.events[i]||[];l.forEach(function(c){return c.apply(s,n)}),a[i]=!1}},t.prototype.deferredEmit=function(i){for(var s,n=[],o=arguments.length-1;o-- >0;)n[o]=arguments[o+1];var a=this.deferredEvents;(s=this).emit.apply(s,[i].concat(n)),(a[i]||(a[i]=[])).push(n)},t.prototype.setOptions=function(i){this.setState(i)},t.prototype.resize=function(i){this.setOptions({width:i})},t.prototype.reset=function(){this.colors.forEach(function(i){return i.reset()}),this.setState({colors:this.colors})},t.prototype.onMount=function(i){this.el=i,this.deferredEmit("mount",this)},t.prototype.onColorChange=function(i,s){this.setState({color:this.color}),this.inputActive&&(this.inputActive=!1,this.emit("input:change",i,s)),this.emit("color:change",i,s)},t.prototype.emitInputEvent=function(i,s){i===0?this.emit("input:start",this.color,s):i===1?this.emit("input:move",this.color,s):i===2&&this.emit("input:end",this.color,s)},t.prototype.render=function(i,s){var n=this,o=s.layout;return Array.isArray(o)||(o=[{component:jl},{component:En}],s.transparency&&o.push({component:En,options:{sliderType:"alpha"}})),X("div",{class:"IroColorPicker",id:s.id,style:{display:s.display}},o.map(function(a,h){var l=a.component,c=a.options;return X(l,Object.assign({},s,c,{ref:void 0,onInput:n.emitInputEvent.bind(n),parent:n,index:h}))}))},t}(ki);Bl.defaultProps=Object.assign({},c0,{colors:[],display:"block",id:null,layout:"default",margin:null});var p0=f0(Bl),ao;(function(r){r.version="5.5.2",r.Color=Mi,r.ColorPicker=p0,function(t){t.h=X,t.ComponentBase=Tn,t.Handle=Qe,t.Slider=En,t.Wheel=jl,t.Box=u0}(r.ui||(r.ui={}))})(ao||(ao={}));var Fl=ao;let ho=null;class kn{constructor(){M(this,"brushRange");M(this,"brushPath");M(this,"opacityRange");M(this,"modalContainer");M(this,"modal");M(this,"openModalBtn");M(this,"closeModalBtn");M(this,"fabricCanvas","");this.brushRange=void 0,this.brushPath=void 0,this.opacityRange=void 0,this.modalContainer=void 0,this.modal=void 0,this.openModalBtn=void 0,this.closeModalBtn=void 0,this.createUI(),this.addEventListeners(),this.initializeColorPicker()}static getInstance(){return ho||(ho=new kn),ho}show(t){if(this.hide(),this.createUI(),this.initializeColorPicker(),this.modalContainer&&this.modal){this.modalContainer.style.display="flex";const e=t.getBoundingClientRect(),i=this.modal.offsetHeight;this.modal.style.position="absolute",this.modal.style.top=`${e.top-i-10}px`,this.modal.style.left=`${e.left}px`}}hide(){this.modalContainer&&(this.modalContainer.style.display="none",this.modalContainer.remove(),this.modalContainer=void 0,this.modal=void 0)}createUI(){const t=document.body;this.modalContainer=document.createElement("div"),this.modalContainer.className="annotation-modal-container",this.modalContainer.id="modalContainer",t.appendChild(this.modalContainer),this.modal=document.createElement("div"),this.modal.className="annotation-modal",this.modal.id="modal",this.modalContainer.appendChild(this.modal);const e=document.createElement("div");e.className="brush-container",this.modal.appendChild(e);const i=document.createElement("h4");i.textContent="Brush Thickness:",e.appendChild(i),this.brushRange=document.createElement("input"),this.brushRange.type="range",this.brushRange.id="brushRange",this.brushRange.min="1",this.brushRange.max="25",this.brushRange.step="1",this.brushRange.value="5",this.brushRange.oninput=()=>this.updateBrushSize(this.brushRange.value),e.appendChild(this.brushRange);const s=document.createElementNS("http://www.w3.org/2000/svg","svg");s.id="brushPreview",s.setAttribute("xmlns","http://www.w3.org/2000/svg"),e.appendChild(s),this.brushPath=document.createElementNS("http://www.w3.org/2000/svg","path"),this.brushPath.setAttribute("d","M 10 40 Q 50 10, 90 40 T 190 40"),this.brushPath.setAttribute("stroke","#4CAF50"),this.brushPath.setAttribute("stroke-width","5"),this.brushPath.setAttribute("fill","none"),s.appendChild(this.brushPath);const n=document.createElement("div");n.className="ColorPicker",n.id="sliderPicker",this.modal.appendChild(n);const o=document.createElement("h3");o.textContent="Select Color",n.appendChild(o);const a=document.createElement("div");a.className="opacity-container",this.modal.appendChild(a);const h=document.createElement("h4");h.textContent="Choose Opacity:",a.appendChild(h),this.opacityRange=document.createElement("input"),this.opacityRange.type="range",this.opacityRange.id="opacityRange",this.opacityRange.min="0",this.opacityRange.max="1",this.opacityRange.step="0.01",this.opacityRange.value="1",this.opacityRange.oninput=()=>this.updateOpacity(this.opacityRange.value),a.appendChild(this.opacityRange),this.closeModalBtn=document.createElement("button"),this.closeModalBtn.className="annotation-close-button",this.closeModalBtn.id="closeModalBtn",this.closeModalBtn.textContent="X",this.closeModalBtn.style.position="absolute",this.closeModalBtn.style.top="10px",this.closeModalBtn.style.right="10px",this.modal.appendChild(this.closeModalBtn)}addEventListeners(){this.openModalBtn&&this.openModalBtn.addEventListener("click",()=>{this.modalContainer&&this.modalContainer.style.display==="flex"||(this.updateModalPosition(),this.modalContainer&&(this.modalContainer.style.display="flex"))}),this.closeModalBtn&&this.closeModalBtn.addEventListener("click",()=>{this.modalContainer&&(this.modalContainer.style.display="none")}),window.addEventListener("click",t=>{t.target===this.modalContainer&&this.modalContainer&&(this.modalContainer.style.display="none")}),window.addEventListener("resize",()=>this.updateModalPosition()),window.addEventListener("scroll",()=>this.updateModalPosition())}updateModalPosition(){if(this.openModalBtn&&this.modal){const t=this.openModalBtn.getBoundingClientRect(),e=this.modal.offsetHeight;this.modal.style.position="absolute",this.modal.style.top=`${t.top-e-10+window.scrollY}px`,this.modal.style.left=`${t.left+window.scrollX}px`}}updateBrushSize(t){const e=(+this.brushRange.value-+this.brushRange.min)/(+this.brushRange.max-+this.brushRange.min)*100;this.brushRange.style.background=`linear-gradient(to right, #4CAF50 ${e}%, #ddd ${e}%)`,this.brushPath.style.strokeWidth=t}updateOpacity(t){this.brushPath.style.opacity=t}initializeColorPicker(){var i,s;Fl.ColorPicker("#sliderPicker",{width:250,color:"rgb(255, 0, 0)",borderWidth:1,borderColor:"#fff",layout:[{component:Fl.ui.Slider,options:{sliderType:"hue"}}]}).on("color:change",n=>{if(this.brushPath){const o=n.rgba,a=`rgba(${o.r}, ${o.g}, ${o.b}, ${o.a})`;this.brushPath.setAttribute("stroke",a),this.opacityRange&&(this.fabricCanvas.freeDrawingBrush&&(this.fabricCanvas.freeDrawingBrush.color=a),this.opacityRange.style.background=`linear-gradient(to right, rgba(255, 0, 0, 0) 0%, ${a} 100%)`)}});const e=ae.getInstance().getFabricCanvas();if(e){this.fabricCanvas=e,this.fabricCanvas.isDrawingMode=!0;const n=new qi(e);n.limitedToCanvasSize=!0,this.fabricCanvas.freeDrawingBrush=n,(i=this.brushRange)==null||i.addEventListener("input",()=>{this.fabricCanvas.freeDrawingBrush&&(this.fabricCanvas.freeDrawingBrush.width=parseInt(this.brushRange.value,10))}),(s=this.opacityRange)==null||s.addEventListener("input",()=>{this.fabricCanvas.freeDrawingBrush&&(this.fabricCanvas.freeDrawingBrush.color=this.opacityRange.value)});const o=document.querySelector("#sliderPicker");o&&o.addEventListener("color:change",a=>{console.log(a)})}}}const si=class si{constructor(t){M(this,"view");M(this,"editor");M(this,"zoomLevel",10);M(this,"scaleDisplayed",!1);M(this,"editorContainer");this.editor=t,this.view=vn.getInstance({eraser:Og,crop:Mg,annotate:Sg,imageScale:Dg,rotateLeft:xg,rotateRight:_g,undo:bg,redo:yg,zoomOut:wg,zoomIn:Cg,addText:Eg,resetIcon:kg,addAnnotation:Tg}),this.wireEvents()}static getInstance(t){return si.instance||(si.instance=new si(t)),si.instance}wireEvents(){this.view.eraserButton.onclick=()=>{ae.getInstance().enableEraserMode()},this.view.cropButton.onclick=()=>{this.editor.initializeCropper()},this.view.annotateButton.onclick=()=>{Zo();const t=ae.getInstance();t.initialize(this.editor);const e=this.editor.getEditorContainer().querySelector(".image-container");if(e){const i=e.querySelector("img");if(i instanceof HTMLImageElement){const s=i.getBoundingClientRect(),n=e.getBoundingClientRect(),o=i.naturalWidth/i.width,a=i.naturalHeight/i.height,h=Math.max(0,n.left-s.left),l=Math.max(0,n.top-s.top),c=Math.min(s.width,n.width),u=Math.min(s.height,n.height),d=h*o,g=l*a,f=c*o,p=u*a,v=document.createElement("canvas");v.width=c,v.height=u;const b=v.getContext("2d");b&&(b.drawImage(i,d,g,f,p,0,0,c,u),t.handleCropButtonClick(v,"Recctangle"))}else return}else return},this.view.addTextButton.onclick=()=>{ae.getInstance().handleAddTextButtonClick()},this.view.addAnnotationButton.onclick=t=>{const e=kn.getInstance(),i=t.currentTarget;e.show(i)},this.view.imageScaleButton.onclick=()=>{if(!this.scaleDisplayed){this.scaleDisplayed=!0;const t=yn.getInstance("AMSTEEL-BLUE","External",this.editor);t.show(),t.onClose(()=>{this.scaleDisplayed=!1})}},this.view.rotateLeftButton.onclick=()=>{const t=this.editor.getCropper();if(t){const e=t.getCropperImage();if(e){const i=-90*(Math.PI/180);e.$rotate(i)}}},this.view.rotateRightButton.onclick=()=>{const t=this.editor.getCropper();if(t){const e=t.getCropperImage();if(e){const i=90*(Math.PI/180);e.$rotate(i)}}},this.view.undoButton.onclick=()=>{ae.getInstance().getUndoRedoManager().undo()},this.view.redoButton.onclick=()=>{ae.getInstance().getUndoRedoManager().redo()},this.view.zoomOutButton.onclick=()=>{const t=this.editor.getCropper();if(t){const e=t.getCropperImage();e&&(e.$scale(.9),this.zoomLevel=Math.max(this.zoomLevel-10,10),this.view.zoomLabel.textContent=`${this.zoomLevel}%`)}},this.view.zoomInButton.onclick=()=>{const t=this.editor.getCropper();if(t){const e=t.getCropperImage();e&&(e.$scale(1.1),this.zoomLevel=Math.min(this.zoomLevel+10,200),this.view.zoomLabel.textContent=`${this.zoomLevel}%`)}},Un.subscribe(t=>{var e,i;if(t==="Oval"){console.log("Oval shape selected");const s=(e=this.editor.getCropper())==null?void 0:e.getCropperSelection();s==null||s.$addStyles(`
                    :host {
                        border-radius: 50%;
                    }
                `),s&&(s.style.borderRadius="50%",s.style.overflow="hidden")}else if(t==="Rectangle"){console.log("Rectangle shape selected");const s=(i=this.editor.getCropper())==null?void 0:i.getCropperSelection();s==null||s.$addStyles(`
                    :host {
                        border-radius: 0;
                    }
                `),s&&(s.style.borderRadius="0",s.style.overflow="hidden")}})}getToolbar(){return this.view.getToolbar()}};M(si,"instance");let On=si;class $l{constructor(t){M(this,"controller");this.controller=On.getInstance(t)}getToolbar(){return this.controller.getToolbar()}}const m0="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='iso-8859-1'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20version='1.1'%20id='Layer_1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20viewBox='0%200%20512%20512'%20xml:space='preserve'%3e%3cpath%20style='fill:%23999999;'%20d='M512,0h-40v16h24v32h16V0z%20M432,0h-40v16h40V0z%20M352,0h-40v16h40V0z%20M272,0h-40v16h40V0z%20M192,0h-40%20v16h40V0z%20M112,0H72v16h40V0z%20M32,0H0v16h32V0z%20M16,48H0v40h16V48z%20M16,128H0v40h16V128z%20M16,208H0v40h16V208z%20M16,288H0v40h16V288z%20M16,368H0v40h16V368z%20M16,448H0v40h16V448z%20M56,496H16v16h40V496z%20M136,496H96v16h40V496z%20M216,496h-40v16h40V496z%20M296,496h-40v16%20h40V496z%20M376,496h-40v16h40V496z%20M456,496h-40v16h40V496z%20M512,488h-16v8l0,0v16h16V488z%20M512,408h-16v40h16V408z%20M512,328h-16v40%20h16V328z%20M512,248h-16v40h16V248z%20M512,168h-16v40h16V168z%20M512,88h-16v40h16V88z'/%3e%3cg%3e%3crect%20x='244'%20y='175.976'%20style='fill:%23E21B1B;'%20width='24'%20height='160.08'/%3e%3crect%20x='175.976'%20y='244'%20style='fill:%23E21B1B;'%20width='160.08'%20height='24'/%3e%3c/g%3e%3c/svg%3e",Nt=class Nt{constructor(t){M(this,"listeners",{});M(this,"options");M(this,"_isDestroyed",!1);M(this,"container");M(this,"topToolbar");M(this,"editorContainer");M(this,"bottomToolbar");M(this,"overlay");M(this,"cropper",null);M(this,"shadowRoot");M(this,"imageContainer",null);M(this,"imageElement",null);this.options=t,document.addEventListener("dblclick",function(e){e.preventDefault()},{passive:!1}),this.overlay=document.createElement("div"),this.overlay.className="editor-fullscreen-overlay",Object.assign(this.overlay.style,{position:"fixed",top:"0",left:"0",width:"100vw",height:"100vh",zIndex:"99999",background:"rgba(0,0,0,0.85)",display:"flex",justifyContent:"center",alignItems:"center"}),document.body.appendChild(this.overlay),this.shadowRoot=this.overlay.attachShadow({mode:"open"}),this.container=document.createElement("div"),this.container.className="editor-global-container",this.topToolbar=mn.getInstance().element,this.editorContainer=document.createElement("div"),this.topToolbar.addEventListener("editor:back",()=>{const e=kt.get(),i=this.getImageContainer();if(i){if(e==="crop"){const s=this.editorContainer.querySelector(".cropper-container");s&&this.editorContainer.removeChild(s),this.editorContainer.innerHTML="",i.querySelectorAll("img").forEach(o=>{o.style.display=""}),this.editorContainer.appendChild(i)}else e==="annotate"&&(this.editorContainer.innerHTML="",this.editorContainer.appendChild(i));le()}}),this.topToolbar.addEventListener("editor:save",()=>{var e,i;if(kt.get()==="crop")this.finalizeCrop(),le();else if(kt.get()==="annotate")le();else if(kt.get()==="home"){const s=this.getImageContainer();let n="";if(s){const o=s.querySelector("img");o&&o.src&&(n=o.src)}this.emit("success",{image:n,report:(e=this.options)==null?void 0:e.report,rating:(i=this.options)==null?void 0:i.currentRating}),gs().then(()=>this.destroy())}}),this.topToolbar.addEventListener("editor:done",()=>{const e=kt.get();if(e==="crop"){if(this.cropper){const i=this.cropper.getCropperSelection();if(i){const s=this.getEditorContainer(),n=s.getBoundingClientRect(),o=n.width,a=n.height;i.$toCanvas({width:o,height:a}).then(h=>{const l=h.toDataURL("image/png"),c=document.createElement("img");c.src=l,c.alt="Cropped Image",c.style.maxWidth="100%",c.style.maxHeight="100%",c.style.objectFit="contain";const u=document.createElement("div");u.className="image-container",u.appendChild(c),s.innerHTML="",s.appendChild(u),this.imageContainer=u,this.cropper=null,le()});return}}}else e==="annotate"&&le()}),this.topToolbar.addEventListener("editor:cancel",()=>{var i;const e=((i=window.Editor)==null?void 0:i.instance)||window.editor||void 0;try{Promise.resolve().then(()=>v0).then(s=>{var n;s.Editor&&s.Editor.instance&&((n=s.Editor.instance)==null||n.destroy())})}catch{e&&typeof e.destroy=="function"&&e.destroy()}this.emit("cancelled",{})}),this.editorContainer.className="editor-container",this.bottomToolbar=new $l(this).getToolbar(),this.setupStyles(),t!=null&&t.imageUrl?this.loadImageReference(t.imageUrl):this.showUploadUI(),this.shadowRoot.appendChild(this.container),window.addEventListener("resize",()=>this.adjustEditorContainerHeight())}on(t,e){this.listeners[t]||(this.listeners[t]=[]),this.listeners[t].push(e)}off(t,e){this.listeners[t]&&(this.listeners[t]=this.listeners[t].filter(i=>i!==e))}emit(t,e){this._isDestroyed||((this.listeners[t]||[]).forEach(i=>i(e)),(t==="success"||t==="error"||t==="cancelled")&&(this._isDestroyed=!0,gs().then(()=>{this.overlay&&document.body.contains(this.overlay)&&document.body.removeChild(this.overlay),Nt.instance=null})))}destroy(){this._isDestroyed||(this._isDestroyed=!0,this.overlay&&document.body.contains(this.overlay)&&document.body.removeChild(this.overlay),Nt.instance=null,gs())}static getInstance(t){return Nt.instance||(t!=null&&t.imageUrl?Nt.instance=new Nt(t):Nt.instance=new Nt),Nt.instance}static init(t){const e=Nt.getInstance(t);return e.show(),e}show(){document.body.contains(this.overlay)||document.body.appendChild(this.overlay)}getCropContainer(){return this.container}getEditorContainer(){return this.editorContainer}getShadowRoot(){return this.shadowRoot}setupStyles(){document.body.style.margin="0",document.body.style.padding="0",document.body.style.height="100vh",document.body.style.overflow="hidden";{const t=document.createElement("link");t.setAttribute("rel","stylesheet"),t.setAttribute("href","./typescript-image-editor.css"),this.shadowRoot.appendChild(t)}}adjustEditorContainerHeight(){}showUploadUI(){var u;let t=document.createElement("div");t.className="h-screen flex items-end justify-center bg-gray-100";const e=document.createElement("div");e.className="relative";const i=document.createElement("button");i.id="dropdownButton",i.className="w-40 bg-blue-600 text-white px-4 py-2 rounded shadow hover:bg-blue-700 flex items-center justify-between",i.innerHTML=`
            <span id="selectedText">Rectangle</span>
            <svg class="w-4 h-4 ml-2 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"/>
            </svg>
        `;const s=document.createElement("div");s.id="dropdownMenu",s.className="absolute hidden bg-white border border-gray-300 rounded shadow w-40",s.innerHTML=`
            <ul>
                <li><button data-value="Rectangle" class="dropdown-option block w-full text-left px-4 py-2 hover:bg-gray-100">Rectangle</button></li>
                <li><button data-value="Oval" class="dropdown-option block w-full text-left px-4 py-2 hover:bg-gray-100">Oval</button></li>
            </ul>
        `,e.appendChild(i),e.appendChild(s);const n=i.firstElementChild,o=((u=s.firstElementChild)==null?void 0:u.children)??[];i.addEventListener("click",()=>{const d=i.getBoundingClientRect(),g=window.innerHeight-d.bottom,f=d.top;s.classList.remove("bottom-full","mb-2","top-full","mt-2"),g<100&&f>=100?s.classList.add("bottom-full","mb-2"):s.classList.add("top-full","mt-2"),s.classList.toggle("hidden")}),Array.from(o).forEach(d=>{d.addEventListener("click",()=>{const g=d.getAttribute("data-value");n&&(n.textContent=g),s.classList.add("hidden")})}),window.addEventListener("click",d=>{!i.contains(d.target)&&!s.contains(d.target)&&s.classList.add("hidden")}),this.container.innerHTML="";const a=document.createElement("div");a.style.display="flex",a.style.flexDirection="column",a.style.justifyContent="center",a.style.alignItems="center",a.style.height="100%",a.style.position="absolute",a.style.top="50%",a.style.left="50%",a.style.transform="translate(-50%, -50%)";const h=document.createElement("button");h.style.border="none",h.style.background="none",h.style.cursor="pointer";const l=document.createElement("img");l.src=m0,l.alt="Add Image",l.style.width="100px",l.style.height="100px",h.appendChild(l),h.addEventListener("click",()=>{const d=document.createElement("input");d.type="file",d.accept="image/*",d.style.display="none",d.addEventListener("change",g=>{const f=g.target;if(f.files&&f.files[0]){const p=new FileReader;p.onload=()=>{const v=p.result;typeof v=="string"?this.loadImage(v):console.error("Unexpected result type from FileReader")},p.readAsDataURL(f.files[0])}}),d.click()});const c=document.createElement("div");c.style.marginTop="20px",c.textContent="Image Scale: 100%",a.appendChild(h),a.appendChild(c),this.overlay.innerHTML="",this.overlay.appendChild(a),this.container.appendChild(this.overlay),this.adjustEditorContainerHeight()}loadImage(t){this.container.innerHTML="",this.overlay.innerHTML="";const e=25,i=45,s=window.innerHeight,n=s-e-i;this.container.className="editor-global-container",this.container.style.height=`${s}px`,this.topToolbar.className="editor-top-toolbar",this.topToolbar.style.height=`${e}px`,this.editorContainer.className="editor-container",this.editorContainer.style.height=`${n}px`,this.bottomToolbar.style.height=`${i}px`,this.imageContainer=document.createElement("div"),this.imageContainer.className="image-container",this.imageElement=document.createElement("img"),this.imageElement.src=t,this.imageElement.alt="Image for editing",this.imageElement.style.maxWidth="100%",this.imageElement.style.maxHeight="100%",this.imageElement.style.objectFit="contain",this.imageElement.onload=()=>{this.imageContainer&&(this.imageContainer.innerHTML="",this.imageContainer.appendChild(this.imageElement)),this.container.appendChild(this.topToolbar),this.editorContainer.innerHTML="",this.editorContainer.appendChild(this.imageContainer),this.container.appendChild(this.editorContainer),this.container.appendChild(this.bottomToolbar)},new ResizeObserver(()=>{const a=window.innerHeight,h=a-e-i;this.container.style.height=`${a}px`,this.editorContainer.style.height=`${h}px`}).observe(this.container)}base64ToBlob(t,e="image/jpeg"){const i=atob(t.split(",")[1]),s=new Uint8Array(i.length);for(let n=0;n<i.length;n++)s[n]=i.charCodeAt(n);return new Blob([s],{type:e})}async loadImageReference(t){let e;if(typeof t=="string")if(t.startsWith("http://")||t.startsWith("https://"))e=t;else if(t.startsWith("data:image")){const i=this.base64ToBlob(t);e=URL.createObjectURL(i)}else try{const s=await(await fetch(t)).blob();e=URL.createObjectURL(s)}catch(i){console.error("Failed to load local image:",i),e=""}else if(t instanceof Blob)e=URL.createObjectURL(t);else if(t instanceof ArrayBuffer){const i=new Blob([t]);e=URL.createObjectURL(i)}else{console.error("Unsupported image reference type");return}this.loadImage(e)}getCropper(){return this.cropper}setCropper(t){this.cropper=t}switchCropperSelection(){var s,n;const t=(s=this.cropper)==null?void 0:s.getCropperCanvas(),e=(n=this.cropper)==null?void 0:n.getCropperSelection();e&&(t==null||t.removeChild(e));const i=document.createElement("cropper-selection",{is:"oval-cropper-selection"});i.setAttribute("movable",""),i.setAttribute("resizable",""),t==null||t.appendChild(i)}initializeCropper(){const t=this.editorContainer.querySelector(".image-container"),e=t==null?void 0:t.querySelector("img");if(!e)return;this.imageElement=e;const i=document.createElement("div");i.className="cropper-container",this.editorContainer.innerHTML="",this.editorContainer.appendChild(i),this.cropper=new Yo(this.imageElement,{container:i});const s=this.cropper.getCropperImage(),n=this.cropper.getCropperSelection();if(s){const a=s.getBoundingClientRect();a&&n&&(n.$addStyles(":host { }"),n.$change(a.x,a.y,a.width,a.height,16/9,!0),n.$render())}n&&n.addEventListener("change",()=>{});const o=this.cropper.getCropperCanvas();o&&(o.style.width="100%",o.style.height="100%",o.$addStyles(":host { background: white !important; }")),this.restrictCropperSelection()}finalizeCrop(){if(!this.cropper){this.emit("error",{message:"No cropper instance."});return}const t=this.cropper.getCropperSelection();if(!t){this.emit("error",{message:"No cropper selection."});return}const i=this.getEditorContainer().getBoundingClientRect(),s=i.width,n=i.height;t.$toCanvas({width:s,height:n}).then(o=>{const a=o.toDataURL("image/png");this.editorContainer.innerHTML="";const h=document.createElement("img");h.src=a,h.alt="Cropped Image",h.style.maxWidth="100%",h.style.maxHeight="100%",h.style.objectFit="contain",this.editorContainer.appendChild(h),this.cropper=null})}restrictCropperSelection(){var i,s;const t=(i=this.cropper)==null?void 0:i.getCropperSelection(),e=(s=this.cropper)==null?void 0:s.getCropperImage();if(!t||!e){console.error("Cropper selection or image not found");return}t.addEventListener("change",n=>{const o=n,{x:a,y:h,width:l,height:c}=o.detail,u=e.getBoundingClientRect(),d=this.editorContainer.querySelector(".cropper-container"),g=d==null?void 0:d.getBoundingClientRect();if(!g)return;const f=a,p=h,v=a+l,b=h+c,w=u.left-g.left,x=u.top-g.top,_=w+u.width,k=x+u.height;(f<w||p<x||v>_||b>k)&&n.preventDefault()})}getImageContainer(){return this.imageContainer}getImageElement(){return this.imageElement}};M(Nt,"instance",null);let ss=Nt;const v0=Object.freeze(Object.defineProperty({__proto__:null,Editor:ss},Symbol.toStringTag,{value:"Module"}));function y0(r){const t=document.createElement("div");t.style.backgroundColor="white",t.style.padding="20px",t.style.borderRadius="8px",t.style.textAlign="center",t.style.boxShadow="0 4px 8px rgba(0, 0, 0, 0.2)";const e=document.createElement("p");e.textContent="Click to upload an image or drag and drop an image here.",e.style.marginBottom="20px";const i=document.createElement("button");i.textContent="Upload Image",i.style.padding="10px 20px",i.style.fontSize="14px",i.style.cursor="pointer";const s=document.createElement("input");return s.type="file",s.accept="image/*",s.style.display="none",i.addEventListener("click",()=>s.click()),s.addEventListener("change",n=>{var a;const o=(a=n.target.files)==null?void 0:a[0];o&&r(o)}),t.appendChild(e),t.appendChild(i),t.appendChild(s),t}function b0(){const r=document.createElement("div");r.style.position="sticky",r.style.top="0",r.style.backgroundColor="#333",r.style.color="white",r.style.padding="10px",r.style.display="flex",r.style.justifyContent="space-between",r.style.alignItems="center",r.style.zIndex="1000",r.innerText="Top Bar";const t=document.createElement("div");return t.style.position="sticky",t.style.bottom="0",t.style.backgroundColor="#333",t.style.color="white",t.style.padding="10px",t.style.display="flex",t.style.justifyContent="space-between",t.style.alignItems="center",t.style.zIndex="1000",t.innerText="Bottom Bar",{topBar:r,bottomBar:t}}function C0(r,t,e){const i=document.createElement("div");i.className="cropped-image-modal";const s=document.createElement("img");s.src=r,s.alt="Cropped Image",s.className="cropped-image-preview",i.appendChild(s);const n=document.createElement("div");n.className="modal-buttons-container";const o=document.createElement("button");o.textContent="Edit",o.className="modal-button edit-button",o.onclick=()=>{t.onEdit(),e.removeChild(i)},n.appendChild(o);const a=document.createElement("button");a.textContent="Continue",a.className="modal-button continue-button",a.onclick=()=>{t.onContinue(),e.removeChild(i)},n.appendChild(a);const h=document.createElement("button");h.textContent="Cancel",h.className="modal-button cancel-button",h.onclick=()=>{t.onCancel(),e.removeChild(i)},n.appendChild(h),i.appendChild(n),e.appendChild(i)}function w0(r){const t=r.getCropperSelection();t==null||t.$addStyles(`
    :host {
        border-radius: 50%;
    }
    `)}const lo={};async function zl(r){return new Promise((t,e)=>{lo.readdir(r,{withFileTypes:!0},(i,s)=>{if(i){e(i);return}const n=[];s.forEach(o=>{const a=lo.join(r,o.name);o.isDirectory()?zl(a).then(h=>{n.push(...h),n.length===s.length&&t(n)}).catch(e):o.isFile()&&x0(o.name)&&n.push(a)}),t(n)})})}function x0(r){return[".png",".jpg",".jpeg",".gif",".bmp",".svg"].includes(lo.extname(r).toLowerCase())}B.BottomToolbar=$l,B.BottomToolbarController=On,B.BottomToolbarView=vn,B.CropButtonHandler=ae,B.Editor=ss,B.ImagesScale=yn,B.ModalUI=kn,B.TopToolbar=mn,B.UndoRedoManager=fn,B.clearImages=gs,B.createAddImageUI=y0,B.createToolBars=b0,B.editorMode=kt,B.editorModeType=qo,B.getEditorMode=Oc,B.getImagesFromFolder=zl,B.image=kc,B.loadImageByKey=Ac,B.modifiedImageKey=Kn,B.originalImageKey=qn,B.setAnnotateMode=Zo,B.setCropMode=Ko,B.setHomeMode=le,B.setModifiedImage=Dc,B.setOriginalImage=Mc,B.shapeStore=Un,B.showCroppedImageModal=C0,B.switchToOvalSelection=w0,Object.defineProperty(B,Symbol.toStringTag,{value:"Module"})});
//# sourceMappingURL=image-editor.umd.js.map
