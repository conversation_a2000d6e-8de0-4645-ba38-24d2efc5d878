import { describe, it, expect, beforeEach, vi } from 'vitest';
import { DeviceInfo } from '../DeviceInfo';

// Mock DOM methods
const mockGetComputedStyle = vi.fn();
const mockCreateElement = vi.fn();
const mockAppendChild = vi.fn();
const mockRemoveChild = vi.fn();

// Mock document and window
Object.defineProperty(global, 'document', {
  value: {
    createElement: mockCreateElement,
    body: {
      appendChild: mockAppendChild,
      removeChild: mockRemoveChild,
    },
    documentElement: {},
  },
  writable: true,
});

Object.defineProperty(global, 'window', {
  value: {
    getComputedStyle: mockGetComputedStyle,
    navigator: {
      userAgent: '',
      platform: '',
      maxTouchPoints: 0,
    },
    screen: {
      height: 800,
    },
  },
  writable: true,
});

Object.defineProperty(global, 'navigator', {
  value: {
    userAgent: '',
    platform: '',
    maxTouchPoints: 0,
  },
  writable: true,
});

describe('DeviceInfo', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset DeviceInfo singleton
    (DeviceInfo as any)._info = null;
    (DeviceInfo as any)._promise = null;
    
    // Setup default mocks
    mockCreateElement.mockReturnValue({
      style: { cssText: '' },
    });
    mockGetComputedStyle.mockReturnValue({
      height: '0px',
    });
  });

  describe('iOS Detection', () => {
    it('should detect iPhone', async () => {
      global.navigator.userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)';
      
      const info = await DeviceInfo.getInfo();
      
      expect(info.isIPhone).toBe(true);
      expect(info.isIOS).toBe(true);
      expect(info.isAndroid).toBe(false);
    });

    it('should detect iPad', async () => {
      global.navigator.userAgent = 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X)';
      
      const info = await DeviceInfo.getInfo();
      
      expect(info.isIPad).toBe(true);
      expect(info.isIOS).toBe(true);
      expect(info.isAndroid).toBe(false);
    });

    it('should detect iPad with MacIntel platform and touch points', async () => {
      global.navigator.userAgent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)';
      global.navigator.platform = 'MacIntel';
      global.navigator.maxTouchPoints = 5;
      
      const info = await DeviceInfo.getInfo();
      
      expect(info.isIPad).toBe(true);
      expect(info.isIOS).toBe(true);
    });
  });

  describe('Android Detection', () => {
    it('should detect Android', async () => {
      global.navigator.userAgent = 'Mozilla/5.0 (Linux; Android 10; SM-G975F)';
      global.navigator.platform = 'Linux armv8l';
      global.navigator.maxTouchPoints = 0;

      const info = await DeviceInfo.getInfo();

      expect(info.isAndroid).toBe(true);
      expect(info.isIOS).toBe(false);
    });
  });

  describe('WebView Detection', () => {
    it('should detect iOS WebView', async () => {
      global.navigator.userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15';
      
      const info = await DeviceInfo.getInfo();
      
      expect(info.isIOSWebView).toBe(true);
      expect(info.isWebView).toBe(true);
    });

    it('should not detect iOS Safari as WebView', async () => {
      global.navigator.userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1';
      
      const info = await DeviceInfo.getInfo();
      
      expect(info.isIOSWebView).toBe(false);
      expect(info.isSafari).toBe(true);
    });

    it('should detect Android WebView', async () => {
      global.navigator.userAgent = 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.93 Mobile Safari/537.36; wv';
      
      const info = await DeviceInfo.getInfo();
      
      expect(info.isAndroidWebView).toBe(true);
      expect(info.isWebView).toBe(true);
    });
  });

  describe('Browser Detection', () => {
    it('should detect Safari', async () => {
      global.navigator.userAgent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Safari/605.1.15';
      
      const info = await DeviceInfo.getInfo();
      
      expect(info.isSafari).toBe(true);
      expect(info.isChrome).toBe(false);
    });

    it('should detect Chrome', async () => {
      global.navigator.userAgent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
      
      const info = await DeviceInfo.getInfo();
      
      expect(info.isChrome).toBe(true);
      expect(info.isSafari).toBe(false);
    });
  });

  describe('Home Indicator Detection', () => {
    it('should detect home indicator when safe area bottom > 0', async () => {
      global.navigator.userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15';
      mockGetComputedStyle.mockReturnValue({
        height: '34px',
      });
      
      const info = await DeviceInfo.getInfo();
      
      expect(info.hasHomeIndicator).toBe(true);
    });

    it('should not detect home indicator when safe area bottom = 0', async () => {
      global.navigator.userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15';
      mockGetComputedStyle.mockReturnValue({
        height: '0px',
      });
      
      const info = await DeviceInfo.getInfo();
      
      expect(info.hasHomeIndicator).toBe(false);
    });
  });

  describe('Safe Area Insets', () => {
    it('should get safe area insets from CSS env variables', async () => {
      global.navigator.userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15';
      global.navigator.platform = 'iPhone';
      global.navigator.maxTouchPoints = 0;

      // Mock different heights for top and bottom test elements
      let callCount = 0;
      mockGetComputedStyle.mockImplementation(() => {
        callCount++;
        if (callCount <= 2) return { height: '44px' }; // top inset calls
        if (callCount <= 4) return { height: '34px' }; // bottom inset calls
        if (callCount <= 6) return { height: '34px' }; // home indicator detection calls
        return { height: '0px' };
      });

      const info = await DeviceInfo.getInfo();

      expect(info.safeAreaInsetTop).toBe(44);
      expect(info.safeAreaInsetBottom).toBe(34);
    });
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance on multiple calls', async () => {
      global.navigator.userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)';
      
      const info1 = await DeviceInfo.getInfo();
      const info2 = await DeviceInfo.getInfo();
      
      expect(info1).toBe(info2);
    });
  });
});
