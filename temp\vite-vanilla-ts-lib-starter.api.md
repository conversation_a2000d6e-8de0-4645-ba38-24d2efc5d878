## API Report File for "vite-vanilla-ts-lib-starter"

> Do not edit this file. It is a report generated by [API Extractor](https://api-extractor.com/).

```ts

import Cropper from 'cropperjs';

// @public (undocumented)
export interface CropOptions {
    	// (undocumented)
    currentRating?: number | string;
    	// (undocumented)
    fileName?: string;
    	// (undocumented)
    imageUrl?: string | Blob | ArrayBuffer;
    	// (undocumented)
    isDemo?: string | boolean;
    	// (undocumented)
    maxRating?: number | string;
    	// (undocumented)
    mode?: string;
    	// (undocumented)
    productName?: string;
    	// (undocumented)
    report?: boolean;
}

// @public
export class Editor {
    	// @internal
    destroy(): void;
    	// @internal (undocumented)
    getCropContainer(): HTMLDivElement;
    	// @internal
    getCropper(): Cropper | null;
    	// @internal (undocumented)
    getEditorContainer(): HTMLDivElement;
    	// @internal
    getImageContainer(): HTMLDivElement | null;
    	// @internal
    getImageElement(): HTMLImageElement | null;
    	// @internal
    static getInstance(options?: CropOptions): Editor;
    	// @internal (undocumented)
    getShadowRoot(): ShadowRoot;
    	static init(options?: CropOptions): Editor;
    	// @internal (undocumented)
    initializeCropper(): void;
    	on(event: "success" | "error" | "cancelled", callback: (data?: unknown) => void): void;
    	// @internal (undocumented)
    setCropper(cropper: Cropper | null): void;
    	show(): void;
    	// @internal (undocumented)
    switchCropperSelection(): void;
}

// @public (undocumented)
export interface resultOptions {
    	// (undocumented)
    image: string | Blob | ArrayBuffer;
    	// (undocumented)
    latitude: string | number;
    	// (undocumented)
    longitude: string | number;
    	// (undocumented)
    rating?: number | string;
    	// (undocumented)
    report?: boolean;
    	// (undocumented)
    texts: string[];
}

// (No @packageDocumentation comment for this package)

```
