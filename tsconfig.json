{"compilerOptions": {"rootDir": ".", "paths": {"@/*": ["./src/*"], "@@/*": ["./*"]}, "target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "lib": ["ESNext", "DOM"], "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "declaration": true, "declarationDir": "./dist", "emitDeclarationOnly": true, "noEmit": false, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "types": ["vite/client"]}, "include": ["src"], "exclude": ["**/*.test.ts", "node_modules", "test/**", ".history/**"]}