{"$schema": "https://developer.microsoft.com/json-schemas/api-extractor/v7/api-extractor.schema.json", "mainEntryPointFilePath": "./dist/src/index.d.ts", "bundledPackages": [], "compiler": {"overrideTsconfig": {"compilerOptions": {"declaration": true, "declarationMap": false}}}, "apiReport": {"enabled": false, "reportFolder": "./build/api"}, "docModel": {"enabled": false, "apiJsonFilePath": "./build/api/vite-vanilla-ts-lib-starter.api.json"}, "dtsRollup": {"enabled": true, "publicTrimmedFilePath": "./build/@unvired/camcrop/camcrop.d.ts", "omitTrimmingComments": true}, "messages": {"compilerMessageReporting": {"default": {"logLevel": "warning"}}, "extractorMessageReporting": {"default": {"logLevel": "warning"}, "ae-missing-release-tag": {"logLevel": "warning"}, "ae-internal-missing-underscore": {"logLevel": "warning"}}, "tsdocMessageReporting": {"default": {"logLevel": "warning"}}}}