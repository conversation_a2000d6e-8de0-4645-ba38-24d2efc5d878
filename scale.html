<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scrollable Image Viewer</title>
    <style>
        .image-viewer-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
            padding: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
            cursor: move;
            width: fit-content;
            max-width: 95%;
            z-index: 10;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .image-viewer-container::-webkit-scrollbar {
            width: 0;
            height: 0;
            display: none;
        }

        .image-viewer-container:hover {
            scrollbar-width: thin;
            -ms-overflow-style: auto;
        }

        .image-viewer-container:hover::-webkit-scrollbar {
            width: 8px;
            height: 8px;
            display: block;
        }

        .image-viewer-container::-webkit-scrollbar-track {
            background: #222;
            border-radius: 10px;
        }

        .image-viewer-container::-webkit-scrollbar-thumb {
            background-color: #888;
            border-radius: 10px;
        }

        .image-viewer-container::-webkit-scrollbar-thumb:hover {
            background-color: #ddd;
        }

        .image-scroller {
            display: flex;
            overflow-x: auto;
            overflow-y: hidden;
            scroll-snap-type: x mandatory;
            width: 100%;
            max-width: calc(100vw - 20px);
            margin-bottom: 10px;
            position: relative;
        }

        .image-scroller img {
            margin-right: 5px;
            border-radius: 5px;
            max-height: 200px;
            scroll-snap-align: start;
            transition: border-color 0.3s ease, transform 0.3s ease;
            position: relative;
            cursor: pointer;
        }

        .image-scroller img:last-child {
            margin-right: 0;
        }

        .image-scroller img.selected {
            border: 3px solid #007bff;
            transform: scale(0.95);
            z-index: 11;
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
        }

        .image-scroller img.selected::after{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            pointer-events: none;
        }

        .close-button {
            background-color: rgba(255, 0, 0, 0.7);
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.3s ease;
            align-self: flex-end;
        }

        .close-button:hover {
            background-color: rgba(255, 0, 0, 1);
        }

        .image-viewer-container h2{
            color: #eee;
            font-size: 1em;
            margin-bottom: 0.5em;
            white-space: nowrap;
        }

        .drag-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 24px;
            color: #fff;
            opacity: 0.5;
            pointer-events: none;
            user-select: none;
            cursor: move;
            z-index: 12;
        }

    </style>
</head>
<body>
    <div class="image-viewer-container" id="myImageViewer">
        <div class="image-scroller">
            <img src="https://picsum.photos/200" alt="Image 1">
            <img src="https://picsum.photos/200" alt="Image 2">
            <span class="drag-icon">&#x2195;</span>
            <img src="https://picsum.photos/200" alt="Image 3">
            <img src="https://picsum.photos/200" alt="Image 4">
            <img src="https://picsum.photos/200" alt="Image 5">
        </div>
        <button class="close-button" onclick="closeImageViewer()">Close</button>
    </div>
    <script>
        const imageViewerContainer = document.getElementById('myImageViewer');
        const imageScroller = imageViewerContainer.querySelector('.image-scroller');
        const images = imageScroller.querySelectorAll('img');

        let isDragging = false;
        let offset = { x: 0, y: 0 };

        // Start dragging on mousedown on the container, not the images
        imageViewerContainer.addEventListener('mousedown', (e) => {
            if (e.target === imageViewerContainer) {
                isDragging = true;
                offset.x = e.clientX - imageViewerContainer.offsetLeft;
                offset.y = e.clientY - imageViewerContainer.offsetTop;
                imageViewerContainer.style.cursor = 'grabbing';
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            imageViewerContainer.style.left = e.clientX - offset.x + 'px';
            imageViewerContainer.style.top = e.clientY - offset.y + 'px';
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
            imageViewerContainer.style.cursor = 'move';
        });

        function closeImageViewer() {
            imageViewerContainer.style.display = 'none';
        }

        images.forEach((image, index) => {
            image.addEventListener('click', () => {
                images.forEach(img => img.classList.remove('selected'));
                image.classList.add('selected');
                console.log('Image index:', index + 1);
            });
        });

        function dragElement(elmnt) {
            var pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
            const PADDING = 10; // Add padding to prevent the element from touching the edges
            let rect, viewport = {};

            if (document.getElementById(elmnt.id + "header")) {
                /* if present, the header is where you move the DIV from:*/
                document.getElementById(elmnt.id + "header").onmousedown = dragMouseDown;
            } else {
                /* otherwise, move the DIV from anywhere inside the DIV:*/
                elmnt.onmousedown = dragMouseDown;
            }

            function dragMouseDown(e) {
                e = e || window.event;
                // get the mouse cursor position at startup:
                pos3 = e.clientX;
                pos4 = e.clientY;

                // store the current viewport and element dimensions when a drag starts
                rect = elmnt.getBoundingClientRect();
                viewport.bottom = window.innerHeight - PADDING;
                viewport.left = PADDING;
                viewport.right = window.innerWidth - PADDING;
                viewport.top = PADDING;

                document.onmouseup = closeDragElement;
                // call a function whenever the cursor moves:
                document.onmousemove = elementDrag;
            }

            function elementDrag(e) {
                e = e || window.event;
                // calculate the new cursor position:
                pos1 = pos3 - e.clientX;
                pos2 = pos4 - e.clientY;
                pos3 = e.clientX;
                pos4 = e.clientY;

                // check to make sure the element will be within our viewport boundary
                var newLeft = elmnt.offsetLeft - pos1;
                var newTop = elmnt.offsetTop - pos2;

                if (newLeft < viewport.left
                    || newTop < viewport.top
                    || newLeft + rect.width > viewport.right
                    || newTop + rect.height > viewport.bottom
                ) {
                    // the element will hit the boundary, do nothing...
                } else {
                    // set the element's new position:
                    elmnt.style.top = (elmnt.offsetTop - pos2) + "px";
                    elmnt.style.left = (elmnt.offsetLeft - pos1) + "px";
                }
            }

            function closeDragElement() {
                /* stop moving when mouse button is released:*/
                document.onmouseup = null;
                document.onmousemove = null;
            }
        }

        // Apply the drag behavior to the scale
        dragElement(imageViewerContainer);
    </script>
</body>
</html>
