import iro from "@jaames/iro";
import { Canvas, PencilBrush } from "fabric";
// import { EraserBrush, ClippingGroup } from '@erase2d/fabric';
import { CropButtonHandler } from "./cropButtonHandler";

interface IroColor {
  hexString: string;
}

let instance: ModalUI | null = null;

/**
 * This should NOT appear in .d.ts
 * @internal
 */
export class ModalUI {
  private brushRange: HTMLInputElement | undefined;
  private brushPath: SVGPathElement | undefined;
  private opacityRange: HTMLInputElement | undefined;
  private modalContainer: HTMLDivElement | undefined;
  private modal: HTMLDivElement | undefined;
  private openModalBtn: HTMLButtonElement | undefined;
  private closeModalBtn: HTMLButtonElement | undefined;
  private fabricCanvas: Canvas = "" as unknown as Canvas;
  private parentContainer: HTMLElement | null = null;
  private colorPickerContainer: HTMLDivElement | null = null;

  /**
   * Set the container where the modal should be appended.
   * If not set, defaults to document.body.
   */
  public setContainer(container: HTMLElement) {
    this.parentContainer = container;
  }

  private constructor() {
    this.brushRange = undefined;
    this.brushPath = undefined;
    this.opacityRange = undefined;
    // this.modalContainer = undefined;
    this.modal = undefined;
    this.openModalBtn = undefined;
    this.closeModalBtn = undefined;
    this.createUI();
    this.addEventListeners();
    this.initializeColorPicker();
  }

  public static getInstance(): ModalUI {
    if (!instance) {
      instance = new ModalUI();
    }
    return instance;
  }

  public isOpen(): boolean {
    return !!(
      this.modalContainer && this.modalContainer.style.display === "flex"
    );
  }

  public show(button: HTMLElement): void {
    this.hide(); // Ensure any existing modal is removed before showing a new one

    this.createUI(); // Reinitialize the modal UI
    this.initializeColorPicker(); // Reinitialize the color picker

    if (this.modalContainer && this.modal) {
      this.modalContainer.style.display = "flex";

      // Position the modal just above the button
      const buttonRect = button.getBoundingClientRect();
      const modalHeight = this.modal.offsetHeight;

      this.modal.style.position = "absolute";
      this.modal.style.top = `${buttonRect.top - modalHeight - 10}px`;
      this.modal.style.left = `${buttonRect.left}px`;
    }
  }

  public hide(): void {
    if (this.modalContainer) {
      this.modalContainer.style.display = "none";
      this.modalContainer.remove(); // Completely remove the modal from the DOM
      this.modalContainer = undefined; // Reset the modal container reference
      this.modal = undefined; // Reset the modal reference
    }
  }

  private createUI(): void {
    const parent = this.parentContainer || document.body;

    this.modalContainer = document.createElement("div");
    this.modalContainer.className = "annotation-modal-container";
    this.modalContainer.id = "modalContainer";
    parent.appendChild(this.modalContainer);

    this.modal = document.createElement("div");
    this.modal.className = "annotation-modal";
    this.modal.id = "modal";
    this.modalContainer.appendChild(this.modal);

    const brushContainer = document.createElement("div");
    brushContainer.className = "brush-container";
    this.modal.appendChild(brushContainer);

    const brushLabel = document.createElement("h4");
    brushLabel.textContent = "Brush Thickness:";
    brushContainer.appendChild(brushLabel);

    this.brushRange = document.createElement("input");
    this.brushRange.type = "range";
    this.brushRange.id = "brushRange";
    this.brushRange.min = "1";
    this.brushRange.max = "25";
    this.brushRange.step = "1";
    this.brushRange.value = "5";
    this.brushRange.oninput = () =>
      this.updateBrushSize(this.brushRange!.value);
    brushContainer.appendChild(this.brushRange);

    const brushPreview = document.createElementNS(
      "http://www.w3.org/2000/svg",
      "svg"
    );
    brushPreview.id = "brushPreview";
    brushPreview.setAttribute("xmlns", "http://www.w3.org/2000/svg");
    brushContainer.appendChild(brushPreview);

    this.brushPath = document.createElementNS(
      "http://www.w3.org/2000/svg",
      "path"
    );
    this.brushPath.setAttribute("d", "M 10 40 Q 50 10, 90 40 T 190 40");
    this.brushPath.setAttribute("stroke", "#4CAF50");
    this.brushPath.setAttribute("stroke-width", "5");
    this.brushPath.setAttribute("fill", "none");
    brushPreview.appendChild(this.brushPath);
    // this.brushPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    // // this.brushPath.className = 'brush-path';
    // this.brushPath.setAttribute('d', 'M 10 40 Q 50 10, 90 40 T 190 40');
    // brushPreview.appendChild(this.brushPath);

    this.colorPickerContainer = document.createElement("div");
    this.colorPickerContainer.className = "ColorPicker";
    this.colorPickerContainer.id = "sliderPicker";
    this.modal.appendChild(this.colorPickerContainer);

    const colorPickerLabel = document.createElement("h3");
    colorPickerLabel.textContent = "Select Color";
    this.colorPickerContainer.appendChild(colorPickerLabel);

    const opacityContainer = document.createElement("div");
    opacityContainer.className = "opacity-container";
    this.modal.appendChild(opacityContainer);

    const opacityLabel = document.createElement("h4");
    opacityLabel.textContent = "Choose Opacity:";
    opacityContainer.appendChild(opacityLabel);

    this.opacityRange = document.createElement("input");
    this.opacityRange.type = "range";
    this.opacityRange.id = "opacityRange";
    this.opacityRange.min = "0";
    this.opacityRange.max = "1";
    this.opacityRange.step = "0.01";
    this.opacityRange.value = "1";
    this.opacityRange.oninput = () =>
      this.updateOpacity(this.opacityRange!.value);
    opacityContainer.appendChild(this.opacityRange);

    this.closeModalBtn = document.createElement("button");
    this.closeModalBtn.className = "annotation-close-button";
    this.closeModalBtn.id = "closeModalBtn";
    this.closeModalBtn.textContent = "X"; // Change label to 'X'
    this.closeModalBtn.style.position = "absolute"; // Position the button
    this.closeModalBtn.style.top = "10px";
    this.closeModalBtn.style.right = "10px";
    this.closeModalBtn.onclick = () => this.hide();
    this.modal.appendChild(this.closeModalBtn);
  }

  private addEventListeners(): void {
    if (this.openModalBtn) {
      this.openModalBtn.addEventListener("click", () => {
        if (
          this.modalContainer &&
          this.modalContainer.style.display === "flex"
        ) {
          return; // Prevent multiple instances if already open
        }
        this.updateModalPosition();
        if (this.modalContainer) this.modalContainer.style.display = "flex";
      });
    }
    if (this.closeModalBtn) {
      this.closeModalBtn.addEventListener("click", () => {
        if (this.modalContainer) this.modalContainer.style.display = "none";
      });
    }
    window.addEventListener("click", event => {
      if (event.target === this.modalContainer) {
        if (this.modalContainer) this.modalContainer.style.display = "none";
      }
    });
    window.addEventListener("resize", () => this.updateModalPosition());
    window.addEventListener("scroll", () => this.updateModalPosition());
  }

  private updateModalPosition(): void {
    if (this.openModalBtn && this.modal) {
      const buttonRect = this.openModalBtn.getBoundingClientRect();
      const modalHeight = this.modal.offsetHeight;

      this.modal.style.position = "absolute";
      this.modal.style.top = `${buttonRect.top - modalHeight - 10 + window.scrollY}px`;
      this.modal.style.left = `${buttonRect.left + window.scrollX}px`;
    }
  }

  private updateBrushSize(value: string): void {
    const percentage =
      ((+this.brushRange!.value - +this.brushRange!.min) /
        (+this.brushRange!.max - +this.brushRange!.min)) *
      100;
    this.brushRange!.style.background = `linear-gradient(to right, #4CAF50 ${percentage}%, #ddd ${percentage}%)`;
    this.brushPath!.style.strokeWidth = value;
    this;
  }

  private updateOpacity(value: string): void {
    this.brushPath!.style.opacity = value;
    // this.fabricCanvas.freeDrawingBrush!.opacity = parseFloat(value); // Update Fabric.js brush opacity
  }

  private initializeColorPicker(): void {
    if (!this.colorPickerContainer) return;
    const sliderPicker = iro.ColorPicker(this.colorPickerContainer, {
      width: 250,
      color: "rgb(255, 0, 0)",
      borderWidth: 1,
      borderColor: "#fff",
      layout: [
        {
          component: iro.ui.Slider,
          options: {
            sliderType: "hue",
          },
        },
      ],
    });

    sliderPicker.on("color:change", (color: IroColor | any) => {
      if (this.brushPath) {
        const rgbaColor = color.rgba;
        const colorString = `rgba(${rgbaColor.r}, ${rgbaColor.g}, ${rgbaColor.b}, ${rgbaColor.a})`;
        this.brushPath.setAttribute("stroke", colorString);
        if (this.opacityRange) {
          if (this.fabricCanvas.freeDrawingBrush) {
            this.fabricCanvas.freeDrawingBrush.color = colorString;
          }
          this.opacityRange.style.background = `linear-gradient(to right, rgba(255, 0, 0, 0) 0%, ${colorString} 100%)`;
        }
      }
    });

    const canvas = CropButtonHandler.getInstance().getFabricCanvas(); // Assuming a method to get the Fabric.js canvas instance
    if (canvas) {
      this.fabricCanvas = canvas; // Store the Fabric.js canvas instance
      this.fabricCanvas.isDrawingMode = true; // Turn on freehand drawing mode

      // Ensure we're using the PencilBrush (supports free drawing)
      const pencilBrush = new PencilBrush(canvas);
      pencilBrush.limitedToCanvasSize = true; // Limit brush to canvas size // Make all annotation paths erasable
      this.fabricCanvas.freeDrawingBrush = pencilBrush; // Use PencilBrush for free drawing

      // Access and modify the drawing context to set lineJoin and lineCap
      // const context = this.fabricCanvas.freeDrawingBrush?.context; // Get the drawing context
      // if (context) {
      //     context.lineJoin = 'round'; // Set line join to round
      //     context.lineCap = 'round'; // Set line cap to round
      // }

      // Set default drawing properties
      // this.fabricCanvas.freeDrawingBrush.color = this.brushPath?.getAttribute('stroke') || 'blue'; // Default to blue if not set
      // this.fabricCanvas.freeDrawingBrush.width = parseInt(this.brushRange?.value || '5', 10); // Default to 5 if not set
      // this.fabricCanvas.freeDrawingBrush.opacity = parseFloat(this.opacityRange?.value || '1'); // Default to 1 if not set

      // Add event listeners to update brush properties dynamically
      this.brushRange?.addEventListener("input", () => {
        if (this.fabricCanvas.freeDrawingBrush) {
          this.fabricCanvas.freeDrawingBrush.width = parseInt(
            this.brushRange!.value,
            10
          );
        }
      });

      this.opacityRange?.addEventListener("input", () => {
        if (this.fabricCanvas.freeDrawingBrush) {
          this.fabricCanvas.freeDrawingBrush.color = this.opacityRange!.value;
        }
      });

      // const colorPicker = document.querySelector('#sliderPicker');
      // if (colorPicker) {
      //     this.colorPickerContainer.addEventListener('color:change', (event: any) => {
      //       console.log(event)
      //         // const color = event.detail.color;
      //         // this.fabricCanvas.freeDrawingBrush.color = color;
      //     });
      // }
    } // Assuming a method to get the Fabric.js canvas instance
    if (this.modalContainer) {
      this.modalContainer.addEventListener("mousedown", (event: MouseEvent) => {
        if (event.target === this.modalContainer) {
          this.hide();
        }
      });
    }
  }
}
