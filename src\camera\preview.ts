import type { cameraResultOptions } from "../editor/editor";

interface PreviewOptions {
  image: string;
  latitude: number;
  longitude: number;
  needCropping: boolean;
  onRetake: () => void;
  onAccept: (result: cameraResultOptions) => void;
  onCrop: (image: string) => void;
}

export class Preview {
  private modal: HTMLDivElement | null = null;

  constructor(private options: PreviewOptions) {
    this.showPreview();
  }

  private showPreview() {
    const {
      image,
      latitude,
      longitude,
      needCropping,
      onRetake,
      onAccept,
      onCrop,
    } = this.options;
    // Modal
    const modal = document.createElement("div");
    modal.style.position = "fixed";
    modal.style.top = "0";
    modal.style.left = "0";
    modal.style.width = "100vw";
    modal.style.height = "100vh";
    modal.style.background = "rgba(0,0,0,0.85)";
    modal.style.display = "flex";
    modal.style.justifyContent = "center";
    modal.style.alignItems = "center";
    modal.style.zIndex = "99999";
    this.modal = modal;

    // Container
    const container = document.createElement("div");
    container.style.position = "relative";
    container.style.background = "#222";
    container.style.borderRadius = "12px";
    container.style.padding = "1em";
    container.style.display = "flex";
    container.style.flexDirection = "column";
    container.style.alignItems = "center";
    container.style.maxWidth = "95vw";
    container.style.maxHeight = "95vh";
    container.style.boxShadow = "0 2px 16px #0008";
    modal.appendChild(container);

    // Image
    const img = document.createElement("img");
    img.src = image;
    img.style.maxWidth = "80vw";
    img.style.maxHeight = "60vh";
    img.style.borderRadius = "8px";
    img.style.marginBottom = "2em";
    container.appendChild(img);

    // Retake button (bottom left)
    const retakeBtn = document.createElement("button");
    retakeBtn.textContent = "Retake";
    retakeBtn.style.position = "absolute";
    retakeBtn.style.left = "1em";
    retakeBtn.style.bottom = "1em";
    retakeBtn.style.padding = "0.5em 1.5em";
    retakeBtn.style.fontSize = "1.1em";
    retakeBtn.style.borderRadius = "8px";
    retakeBtn.style.background = "#fff";
    retakeBtn.style.color = "#222";
    retakeBtn.style.border = "none";
    retakeBtn.style.cursor = "pointer";
    container.appendChild(retakeBtn);

    // Accept button (bottom right)
    const acceptBtn = document.createElement("button");
    acceptBtn.textContent = "Accept";
    acceptBtn.style.position = "absolute";
    acceptBtn.style.right = "1em";
    acceptBtn.style.bottom = "1em";
    acceptBtn.style.padding = "0.5em 1.5em";
    acceptBtn.style.fontSize = "1.1em";
    acceptBtn.style.borderRadius = "8px";
    acceptBtn.style.background = "#4caf50";
    acceptBtn.style.color = "#fff";
    acceptBtn.style.border = "none";
    acceptBtn.style.cursor = "pointer";
    container.appendChild(acceptBtn);

    // Append modal
    document.body.appendChild(modal);

    // Retake handler
    retakeBtn.onclick = () => {
      this.cleanup();
      onRetake();
    };

    // Accept handler
    acceptBtn.onclick = () => {
      this.cleanup();
      if (needCropping) {
        onCrop(image);
      } else {
        onAccept({ image, latitude, longitude });
      }
    };
  }

  private cleanup() {
    if (this.modal) {
      document.body.removeChild(this.modal);
      this.modal = null;
    }
  }
}
