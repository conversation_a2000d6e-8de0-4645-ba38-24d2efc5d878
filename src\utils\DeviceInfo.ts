// DeviceInfo.ts
// Singleton utility for device and environment detection

/** @internal */
export interface DeviceInfoResult {
  userAgent: string;
  isIPhone: boolean;
  isIPad: boolean;
  isIOS: boolean;
  isAndroid: boolean;
  isSafari: boolean;
  isChrome: boolean;
  isIOSWebView: boolean;
  isAndroidWebView: boolean;
  isWebView: boolean;
  hasHomeIndicator: boolean;
  safeAreaInsetTop: number;
  safeAreaInsetBottom: number;
}

/** @internal */
class DeviceInfoClass {
  private static instance: DeviceInfoClass;
  private _info: DeviceInfoResult | null = null;
  private _promise: Promise<DeviceInfoResult> | null = null;

  private constructor() {}

  public static getInstance(): DeviceInfoClass {
    if (!DeviceInfoClass.instance) {
      DeviceInfoClass.instance = new DeviceInfoClass();
    }
    return DeviceInfoClass.instance;
  }

  public async getInfo(): Promise<DeviceInfoResult> {
    if (this._info) return this._info;
    if (this._promise) return this._promise;
    this._promise = this.detect();
    this._info = await this._promise;
    return this._info;
  }

  private async detect(): Promise<DeviceInfoResult> {
    const ua = navigator.userAgent || "";
    const isAndroid = /Android/.test(ua);
    const isIPhone = /iPhone/.test(ua);
    const isIPad =
      /iPad/.test(ua) ||
      (navigator.platform === "MacIntel" && navigator.maxTouchPoints > 1);
    const isIOS = isIPhone || isIPad;
    const isSafari =
      /Safari/.test(ua) && !/Chrome|CriOS|FxiOS|OPR|EdgiOS/.test(ua);
    const isChrome = /Chrome/.test(ua) && !/Edg|OPR|Brave/.test(ua);
    const isIOSWebView = isIOS && !/Safari/.test(ua) && /AppleWebKit/.test(ua);
    const isAndroidWebView = isAndroid && /wv/.test(ua);
    const homeIndicator = isIOS && (await this.hasHomeIndicator());
    // Get safe area insets if available
    const safeAreaInsets = await this.getSafeAreaInsets();
    const safeAreaInsetTop = safeAreaInsets.top;
    const safeAreaInsetBottom = safeAreaInsets.bottom;
    return {
      userAgent: ua,
      isIPhone,
      isIPad,
      isIOS,
      isAndroid,
      isSafari,
      isChrome,
      isIOSWebView,
      isAndroidWebView,
      isWebView: isIOSWebView || isAndroidWebView,
      hasHomeIndicator: homeIndicator,
      safeAreaInsetTop,
      safeAreaInsetBottom,
    };
  }

  private async getSafeAreaInsets(): Promise<{ top: number; bottom: number }> {
    return new Promise(resolve => {
      if (
        typeof window === "undefined" ||
        typeof getComputedStyle !== "function"
      ) {
        resolve({ top: 0, bottom: 0 });
        return;
      }

      // Create test elements for both top and bottom insets
      const topTest = document.createElement("div");
      topTest.style.cssText = `
        position: absolute;
        top: 0;
        height: constant(safe-area-inset-top);
        height: env(safe-area-inset-top);
        visibility: hidden;
        pointer-events: none;
      `;

      const bottomTest = document.createElement("div");
      bottomTest.style.cssText = `
        position: absolute;
        bottom: 0;
        height: constant(safe-area-inset-bottom);
        height: env(safe-area-inset-bottom);
        visibility: hidden;
        pointer-events: none;
      `;

      document.body.appendChild(topTest);
      document.body.appendChild(bottomTest);

      // Wait for styles to apply
      setTimeout(() => {
        const topComputed = window.getComputedStyle(topTest).height;
        const bottomComputed = window.getComputedStyle(bottomTest).height;

        document.body.removeChild(topTest);
        document.body.removeChild(bottomTest);

        const top = parseInt(topComputed) || 0;
        const bottom = parseInt(bottomComputed) || 0;

        resolve({ top, bottom });
      }, 10);
    });
  }

  private async hasHomeIndicator(): Promise<boolean> {
    return new Promise(resolve => {
      const test = document.createElement("div");
      test.style.cssText = `
        position: absolute;
        bottom: 0;
        height: constant(safe-area-inset-bottom);
        height: env(safe-area-inset-bottom);
        visibility: hidden;
        pointer-events: none;
      `;
      document.body.appendChild(test);
      // Wait for style to apply
      setTimeout(() => {
        const computed = window.getComputedStyle(test).height;
        document.body.removeChild(test);
        resolve(parseInt(computed) > 0);
      }, 10);
    });
  }
}

/** @internal */
export const DeviceInfo = DeviceInfoClass.getInstance();
