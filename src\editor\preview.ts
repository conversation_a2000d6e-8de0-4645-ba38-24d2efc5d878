class ImagePreview {
  private modal: HTMLElement;
  private editButton: HTMLButtonElement;
  private okButton: HTMLButtonElement;
  private cancelButton: HTMLButtonElement;

  constructor() {
    this.modal = document.createElement("div");
    this.modal.className = "image-preview-modal";

    this.editButton = document.createElement("button");
    this.editButton.textContent = "Edit";
    this.editButton.className = "image-preview-edit";

    this.okButton = document.createElement("button");
    this.okButton.textContent = "Ok";
    this.okButton.className = "image-preview-ok";

    this.cancelButton = document.createElement("button");
    this.cancelButton.textContent = "Cancel";
    this.cancelButton.className = "image-preview-cancel";

    this.initializeModal();
  }

  private initializeModal() {
    const buttonContainer = document.createElement("div");
    buttonContainer.className = "image-preview-buttons";

    buttonContainer.appendChild(this.editButton);
    buttonContainer.appendChild(this.okButton);
    buttonContainer.appendChild(this.cancelButton);

    this.modal.appendChild(buttonContainer);
    document.body.appendChild(this.modal);

    this.addEventListeners();
  }

  private addEventListeners() {
    this.editButton.addEventListener("click", () => this.onEdit());
    this.okButton.addEventListener("click", () => this.onOk());
    this.cancelButton.addEventListener("click", () => this.onCancel());
  }

  private onEdit() {
    console.log("Edit button clicked");
    // Add logic for editing the image
  }

  private onOk() {
    console.log("Ok button clicked");
    this.hideModal();
  }

  private onCancel() {
    console.log("Cancel button clicked");
    this.hideModal();
  }

  public showModal(imageSrc: string) {
    const imageElement = document.createElement("img");
    imageElement.src = imageSrc;
    imageElement.className = "image-preview";

    // Clear previous content and add the new image
    this.modal.innerHTML = "";
    this.modal.appendChild(imageElement);

    const buttonContainer = document.createElement("div");
    buttonContainer.className = "image-preview-buttons";
    buttonContainer.appendChild(this.editButton);
    buttonContainer.appendChild(this.okButton);
    buttonContainer.appendChild(this.cancelButton);

    this.modal.appendChild(buttonContainer);
    this.modal.style.display = "block";
  }

  public hideModal() {
    this.modal.style.display = "none";
  }
}

export default ImagePreview;
