import { Editor } from "./editor/editor";

import {
  Camera,
  insightAICameraOptions,
  insightAICamCropOptions,
} from "./camera/camera";
import { InsightAICropperOptions } from "./editor/editor";
// If you need cameraOptions type, you can import it here for type safety

function handleFileInputToBlob(input: HTMLInputElement, callback: (blob: Blob, file: File) => void) {
  input.addEventListener("change", () => {
    const file = input.files?.[0];
    if (file) {
      // File is already a Blob, but you can process it here
      callback(file, file);
    }
  });
}

// Usage:
const fileInput = document.getElementById("fileInput") as HTMLInputElement | null;
if (fileInput) {
  handleFileInputToBlob(fileInput, (blob, file) => {
    console.log(file)
    console.log("Blob:", blob);
      const options: InsightAICropperOptions = {
        imageUrl: blob,
        ratingImagesScale: false,
        annotation: false,
        report: false,
        location:false
      };
      const editor = Editor.init(options);
      // editor.show();
      editor.on("success", (result: unknown) => {
        console.log("Success:", result);
        // document.getElementById('resultImg')?.src = result.image;
      });
      editor.on("error", (err: unknown) => {
        console.error("Error:", err);
      });
      editor.on("cancelled", () => {
        console.log("Editor cancelled");
      });
  });
}

//------------------open camera-----------------------------
const insightAIcameraBtn = document.getElementById("insightAIcameraBtn");
insightAIcameraBtn?.addEventListener("click", () => {
  const camera = new Camera();
  const options: insightAICameraOptions = {
    preview: true,
    location: false,
    needCropping: false,
  };
  camera
    .openCamera(options)
    .then(result => {
      // result: { image, latitude, longitude }
      console.log("result", result);
      // Handle the result here (e.g., show preview, upload, etc.)
      console.log("Captured image:", result.image);
      console.log("Latitude:", result.latitude, "Longitude:", result.longitude);
    })
    .catch(err => {
      // Handle user cancel or error
      console.error(err);
    });
});

// -------------------camera + cropper------------------------------
const CamCropperBtn = document.getElementById("insightAICamCropperBtn");
CamCropperBtn?.addEventListener("click", () => {
  const options: insightAICamCropOptions = {
    preview: true,
    location: false,
    needCropping: true,
    cropperInitOptions: {
      mode: "InsightAI",
      report: false,
      annotation: false,
      ratingImagesScale: false,
    },
  };
  const camera = new Camera();
  camera
    .openCamera(options)
    .then(result => {
      // result: { image, latitude, longitude }
      console.log("result", result);
      // Handle the result here (e.g., show preview, upload, etc.)
      console.log("Captured image:", result.image);
      console.log("Latitude:", result.latitude, "Longitude:", result.longitude);
    })
    .catch(err => {
      // Handle user cancel or error
      console.error(err);
    });
});

// --------------------open Cropper----------------

const cropperBtn = document.getElementById("insightAIcropperBtn");
cropperBtn?.addEventListener("click", () => {
  const options: InsightAICropperOptions = {
    location: false,
    imageUrl: "assets/images/sample1.jpg",
    ratingImagesScale: false,
    annotation: false,
    report: false,
  };
  const editor = Editor.init(options);
  // editor.show();
  editor.on("success", (result: unknown) => {
    console.log("Success:", result);
    // document.getElementById('resultImg')?.src = result.image;
  });
  editor.on("error", (err: unknown) => {
    console.error("Error:", err);
  });
  editor.on("cancelled", () => {
    console.log("Editor cancelled");
  });
});

// ---------------------insight AI Camera-----------------------
// const insightAICamBtn = document.getElementById("insightAICamBtn");
// insightAICamBtn?.addEventListener("click", () => {
//   const options = { imageUrl: "assets/images/sample1.jpg" };
//   const editor = Editor.init(options);
//   // editor.show();
//   editor.on("success", (result: unknown) => {
//     console.log("Success:", result);
//     // document.getElementById('resultImg')?.src = result.image;
//   });
//   editor.on("error", (err: unknown) => {
//     console.error("Error:", err);
//   });
//   editor.on("cancelled", () => {
//     console.log("Editor cancelled");
//   });
// });

// // --------------------Insight AI Camera Cropper-----------------------------

// const insightAICamCropBtn = document.getElementById("insightAICamCropBtn");
// insightAICamCropBtn?.addEventListener("click", () => {
//   const options = { imageUrl: "assets/images/sample1.jpg" };
//   const editor = Editor.init(options);
//   // editor.show();
//   editor.on("success", (result: unknown) => {
//     console.log("Success:", result);
//     // document.getElementById('resultImg')?.src = result.image;
//   });
//   editor.on("error", (err: unknown) => {
//     console.error("Error:", err);
//   });
//   editor.on("cancelled", () => {
//     console.log("Editor cancelled");
//   });
// });
