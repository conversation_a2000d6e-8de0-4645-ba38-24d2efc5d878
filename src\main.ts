import { bootstrapApplication } from '@angular/platform-browser';
import { AppComponent } from './app/app.component';
import { OPEN_CV_CONFIGURATION, OpenCVOptions } from '@lightsailed/ngx-open-cv';

const openCVConfig: OpenCVOptions = {
  scriptUrl: 'opencv.js',
  wasmBinaryFile: 'opencv_js.wasm',
  usingWasm: true
};

bootstrapApplication(AppComponent, {
  providers: [
    { provide: OPEN_CV_CONFIGURATION, useValue: openCVConfig }
  ]
}).catch((err) => console.error(err));
