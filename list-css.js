const fs = require('fs');
const path = require('path');

const nodeModulesPath = path.resolve(__dirname, 'node_modules');
const cssFiles = [];

function walk(dir) {
  let files;
  try {
    files = fs.readdirSync(dir);
  } catch (err) {
    return; // Skip unreadable directories (e.g. due to permissions)
  }

  for (const file of files) {
    const fullPath = path.join(dir, file);
    let stat;

    try {
      stat = fs.statSync(fullPath);
    } catch (err) {
      continue; // Skip files that can't be stat-ed
    }

    if (stat.isDirectory()) {
      walk(fullPath);
    } else if (file.endsWith('.css')) {
      cssFiles.push(fullPath);
    }
  }
}

console.log('Scanning node_modules for CSS files...');
walk(nodeModulesPath);

// Output results
console.log(`\nFound ${cssFiles.length} CSS file(s):\n`);
cssFiles.forEach(file => console.log(file));
