import { getImages, Category, Type } from "./ratingImages";
import { Editor } from "../editor/editor";

/** @internal */
export class ImagesScale {
  private static instance: ImagesScale;
  private container: HTMLDivElement;
  private imageScroller: HTMLDivElement;
  private dragIcon: HTMLSpanElement;
  private startX: number = 0;
  private startY: number = 0;
  private isDragging: boolean = false;

  constructor(
    private category: Category,
    private type: Type,
    private editor: Editor
  ) {
    this.container = document.createElement("div");
    this.container.className = "image-viewer-container";
    this.container.id = "myImageViewer";

    // Create image scroller
    this.imageScroller = document.createElement("div");
    this.imageScroller.className = "image-scroller";

    // Create drag icon
    this.dragIcon = document.createElement("span");
    this.dragIcon.className = "drag-icon";
    this.dragIcon.innerHTML = "&#x2195;";

    // Add images to scroller
    const images = getImages(this.category, this.type);

    images.forEach((image: string) => {
      const img = document.createElement("img");
      img.src = image;
      img.alt = image.split("/").pop() || "";
      this.imageScroller.appendChild(img);
    });

    // Add drag icon in the middle
    this.imageScroller.appendChild(this.dragIcon);

    // Create close button
    const closeButton = document.createElement("button");
    closeButton.className = "close-button";
    closeButton.textContent = "Close";

    // Append elements to container
    this.container.appendChild(this.imageScroller);
    this.container.appendChild(closeButton);

    // Add styles
    this.addStyles();

    // Add event listeners
    this.addEventListeners();
  }

  private addStyles(): void {
    const style = document.createElement("style");
    style.textContent = `
      .image-viewer-container {
        position: fixed;
        background-color: rgba(0, 0, 0, 0.8);
        border-radius: 10px;
        padding: 10px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
        cursor: move;
        width: fit-content;
        max-width: 90vw;
        z-index: 9999;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        overflow-y: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
        touch-action: none;
        user-select: none;
        left: 0;
        top: 0;
        transform: none;
      }

      .image-viewer-container.dragging {
        transition: none;
      }

      .image-viewer-container:not(.dragging) {
        transition: transform 0.3s ease;
      }

      .image-viewer-container::-webkit-scrollbar {
        width: 0;
        height: 0;
        display: none;
      }

      .image-viewer-container:hover {
        scrollbar-width: thin;
        -ms-overflow-style: auto;
      }

      .image-viewer-container:hover::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        display: block;
      }

      .image-viewer-container::-webkit-scrollbar-track {
        background: #222;
        border-radius: 10px;
      }

      .image-viewer-container::-webkit-scrollbar-thumb {
        background-color: #888;
        border-radius: 10px;
      }

      .image-viewer-container::-webkit-scrollbar-thumb:hover {
        background-color: #ddd;
      }

      .image-scroller {
        display: flex;
        overflow-x: auto;
        overflow-y: hidden;
        scroll-snap-type: x mandatory;
        width: 100%;
        max-width: 90vw;
        margin-bottom: 10px;
        position: relative;
        touch-action: pan-x;
        gap: 8px;
        padding: 4px;
      }

      .image-scroller img {
        margin-right: 5px;
        border-radius: 5px;
        height: 120px;
        width: auto;
        object-fit: contain;
        scroll-snap-align: start;
        transition: border-color 0.3s ease, transform 0.3s ease;
        position: relative;
        cursor: pointer;
        touch-action: manipulation;
      }

      .image-scroller img:last-child {
        margin-right: 0;
      }

      .image-scroller img.selected {
        border: 3px solid #007bff;
        transform: scale(0.95);
        z-index: 11;
        box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
      }

      .image-scroller img.selected::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 5px;
        pointer-events: none;
      }

      .close-button {
        background-color: rgba(255, 0, 0, 0.7);
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 12px;
        transition: background-color 0.3s ease;
        align-self: flex-end;
        touch-action: manipulation;
      }

      .close-button:hover {
        background-color: rgba(255, 0, 0, 1);
      }

      .drag-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 24px;
        color: #fff;
        opacity: 0.5;
        pointer-events: none;
        user-select: none;
        cursor: move;
        z-index: 12;
      }
    `;
    this.container.appendChild(style);
  }

  private addEventListeners(): void {
    const handleDragStart = (e: MouseEvent | TouchEvent) => {
      if (e.target === this.container) {
        this.isDragging = true;
        this.container.style.cursor = "grabbing";
        this.container.classList.add("dragging");

        if (e instanceof MouseEvent) {
          this.startX = e.clientX - this.container.offsetLeft;
          this.startY = e.clientY - this.container.offsetTop;
        } else {
          const touch = e.touches[0];
          this.startX = touch.clientX - this.container.offsetLeft;
          this.startY = touch.clientY - this.container.offsetTop;
        }
      }
    };

    const handleDrag = (e: MouseEvent | TouchEvent) => {
      if (!this.isDragging) return;

      e.preventDefault();

      let clientX: number;
      let clientY: number;

      if (e instanceof MouseEvent) {
        clientX = e.clientX;
        clientY = e.clientY;
      } else {
        const touch = e.touches[0];
        clientX = touch.clientX;
        clientY = touch.clientY;
      }

      const newLeft = clientX - this.startX;
      const newTop = clientY - this.startY;

      // Get viewport boundaries
      const rect = this.container.getBoundingClientRect();
      const maxLeft = window.innerWidth - rect.width;
      const maxTop = window.innerHeight - rect.height;

      // Apply constrained position
      this.container.style.left = `${Math.max(0, Math.min(newLeft, maxLeft))}px`;
      this.container.style.top = `${Math.max(0, Math.min(newTop, maxTop))}px`;
    };

    const handleDragEnd = () => {
      this.isDragging = false;
      this.container.style.cursor = "move";
      this.container.classList.remove("dragging");
    };

    // Mouse events
    this.container.addEventListener("mousedown", handleDragStart);
    document.addEventListener("mousemove", handleDrag);
    document.addEventListener("mouseup", handleDragEnd);

    // Touch events
    this.container.addEventListener("touchstart", handleDragStart, {
      passive: false,
    });
    document.addEventListener("touchmove", handleDrag, { passive: false });
    document.addEventListener("touchend", handleDragEnd);

    // Image selection
    const images = this.imageScroller.querySelectorAll("img");
    images.forEach((image, index) => {
      image.addEventListener("click", () => {
        images.forEach(img => img.classList.remove("selected"));
        image.classList.add("selected");
        console.log("Image index:", index + 1);
      });
    });

    // Close button
    const closeButton = this.container.querySelector(".close-button");
    if (closeButton) {
      closeButton.addEventListener("click", () => {
        this.hide();
      });
    }
  }

  public show(): void {
    const shadowRoot = this.editor.getShadowRoot();
    if (!shadowRoot.contains(this.container)) {
      shadowRoot.appendChild(this.container);
    }

    // Calculate initial position
    const rect = this.container.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Center horizontally and position at top
    const left = Math.max(0, (viewportWidth - rect.width) / 2);
    const top = Math.max(0, Math.min(10, viewportHeight - rect.height));

    // Apply position
    this.container.style.left = `${left}px`;
    this.container.style.top = `${top}px`;

    // Show the container
    this.container.style.display = "flex";
  }

  public hide(): void {
    this.container.style.display = "none";
  }

  public onClose(callback: () => void): void {
    const closeButton = this.container.querySelector(".close-button");
    if (closeButton) {
      closeButton.addEventListener("click", callback);
    }
  }

  public static getInstance(
    category: Category,
    type: Type,
    editor: Editor
  ): ImagesScale {
    if (!ImagesScale.instance) {
      ImagesScale.instance = new ImagesScale(category, type, editor);
    }
    return ImagesScale.instance;
  }
}
