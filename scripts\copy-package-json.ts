import fs from "fs";
import path from "path";

function stripPackagePrefix(filePath: string) {
  return filePath.replace(/^package\//, "");
}

export async function copyPackageJson(resolvedConfig: any) {
  // console.log("resolvedConfig", resolvedConfig.build);
  const rootPkgPath = path.resolve(__dirname, "..", "package.json");
  const distPkgPath = path.resolve(__dirname, "..", "./build/@unvired/camcrop", "package.json");

  const pkg = JSON.parse(fs.readFileSync(rootPkgPath, "utf-8"));

  delete pkg.devDependencies;
  delete pkg.dependencies;
  delete pkg.scripts;
  delete pkg.private;
  delete pkg?.volta;

  // Fix main/module/types fields
  if (pkg.main) pkg.main = stripPackagePrefix(pkg.main);
  if (pkg.module) pkg.module = stripPackagePrefix(pkg.module);
  if (pkg.types) pkg.types = stripPackagePrefix(pkg.types);

  // Fix exports field
  if (pkg.exports && typeof pkg.exports === "object") {
    for (const key of Object.keys(pkg.exports)) {
      const entry = pkg.exports[key];
      if (typeof entry === "string") {
        pkg.exports[key] = stripPackagePrefix(entry);
      } else if (typeof entry === "object" && entry !== null) {
        for (const cond of Object.keys(entry)) {
          if (typeof entry[cond] === "string") {
            entry[cond] = stripPackagePrefix(entry[cond]);
          }
        }
      }
    }
  }

  fs.mkdirSync(path.dirname(distPkgPath), { recursive: true });
  fs.writeFileSync(distPkgPath, JSON.stringify(pkg, null, 2), "utf-8");
  console.log(`✅ package.json copied to ${resolvedConfig.build.outDir}/`);
}
