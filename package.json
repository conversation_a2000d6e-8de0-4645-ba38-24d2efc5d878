{"name": "@unvired/camcrop", "private": false, "version": "0.0.17", "main": "./camcrop.umd.js", "module": "./camcrop.js", "types": "./camcrop.d.ts", "type": "module", "files": ["camcrop.js", "camcrop.d.ts"], "exports": {".": {"types": "./camcrop.d.ts", "import": "./camcrop.js", "require": "./camcrop.umd.js", "default": "./camcrop.js"}}, "scripts": {"dev": "vite --host", "build2": "rimraf build && tsc && npx vite build && npx api-extractor run --local", "build1": "rimraf build && tsc && npx vite build", "buildLatest": "rimraf build && rimraf dist && tsc && vite build && dts-bundle-generator --config ./dts-bundle-generator.config.ts && npx api-extractor run --local", "build": "rimraf build && rimraf dist && tsc && vite build && npx api-extractor run --local && node --experimental-strip-types ./scripts/clean-unused-imports.ts", "test": "vitest", "test:coverage": "vitest --coverage", "lint:scripts": "eslint . --ext .ts", "lint:styles": "stylelint ./**/*.{css,scss}", "format:scripts": "prettier . --write", "format:styles": "stylelint ./**/*.{css,scss} --fix", "format": "npm run format:scripts && npm run format:styles", "prepare": "husky && echo 'npx lint-staged' > .husky/pre-commit && git add .husky/pre-commit", "uninstall-husky": "npm uninstall husky --no-save && git config --unset core.hooksPath && npx rimraf .husky"}, "devDependencies": {"@microsoft/api-extractor": "^7.52.8", "@tailwindcss/postcss": "^4.1.10", "@types/jsdom": "^21.1.7", "@types/node": "^22.15.30", "@vitest/coverage-v8": "^3.0.7", "autoprefixer": "^10.4.21", "copyfiles": "^2.4.1", "dts-bundle-generator": "^9.5.1", "eslint": "^9.21.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "husky": "^9.1.7", "jiti": "^2.4.2", "lint-staged": "^15.4.3", "postcss": "^8.5.6", "postcss-scss": "^4.0.9", "prettier": "^3.5.2", "rimraf": "^6.0.1", "sharp": "^0.34.2", "stylelint": "^16.14.1", "stylelint-config-recommended": "^15.0.0", "stylelint-config-sass-guidelines": "^12.1.0", "stylelint-order": "^6.0.4", "stylelint-prettier": "^5.0.3", "svgo": "^3.3.2", "ts-node": "^10.9.2", "typescript": "^5.7.3", "typescript-eslint": "^8.25.0", "unplugin-dts-bundle-generator": "^3.0.0", "vite": "^6.3.5", "vite-plugin-arraybuffer": "^0.1.0", "vite-plugin-dts": "^4.5.4", "vite-plugin-image-optimizer": "^1.1.8", "vite-plugin-mkcert": "^1.17.8", "vitest": "^3.0.7"}, "dependencies": {"@erase2d/fabric": "^1.1.7", "@jaames/iro": "^5.5.2", "@tailwindcss/vite": "^4.1.10", "blueimp-load-image": "^5.16.0", "cropperjs": "^2.0.0", "daisyui": "^5.0.43", "fabric": "^6.7.0", "idb-keyval": "^6.2.2", "nanostores": "^1.0.1", "tailwindcss": "^4.1.10", "ts-morph": "^26.0.0", "vite-plugin-css-injected-by-js": "^3.5.2"}, "volta": {"node": "22.15.0"}}