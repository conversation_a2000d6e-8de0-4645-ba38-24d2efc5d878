{"name": "angular-ts-cropper-test", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.1.0", "@angular/cdk": "^19.2.19", "@angular/common": "^19.1.0", "@angular/compiler": "^19.1.0", "@angular/core": "^19.1.0", "@angular/forms": "^19.1.0", "@angular/material": "^19.2.19", "@angular/platform-browser": "^19.1.0", "@angular/platform-browser-dynamic": "^19.1.0", "@angular/router": "^19.1.0", "@lightsailed/ngx-open-cv": "^19.2.3", "@opencv.js/wasm": "^4.4.0", "@techstark/opencv-js": "^4.11.0-release.1", "@tensorflow/tfjs": "^4.22.0", "@tensorflow/tfjs-backend-wasm": "^4.22.0", "bootstrap": "^5.3.6", "ngx-opencv": "^2.0.1", "openai": "^5.9.0", "opencv-wasm": "^4.3.0-10", "rxjs": "~7.8.0", "tslib": "^2.3.0", "vite-vanilla-ts-lib-starter": "file:../vite-vanilla-ts-lib-starter/build", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.1.6", "@angular/cli": "^19.1.6", "@angular/compiler-cli": "^19.1.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.7.2"}}