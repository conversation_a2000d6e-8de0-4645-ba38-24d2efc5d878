/** @internal */
export function createAddImageUI(
  onImageUpload: (file: File) => void
): HTMLDivElement {
  // Create the upload overlay
  const overlayContent = document.createElement("div");
  overlayContent.style.backgroundColor = "white";
  overlayContent.style.padding = "20px";
  overlayContent.style.borderRadius = "8px";
  overlayContent.style.textAlign = "center";
  overlayContent.style.boxShadow = "0 4px 8px rgba(0, 0, 0, 0.2)";

  const overlayText = document.createElement("p");
  overlayText.textContent =
    "Click to upload an image or drag and drop an image here.";
  overlayText.style.marginBottom = "20px";

  const uploadButton = document.createElement("button");
  uploadButton.textContent = "Upload Image";
  uploadButton.style.padding = "10px 20px";
  uploadButton.style.fontSize = "14px";
  uploadButton.style.cursor = "pointer";

  // File input for uploading an image
  const fileInput = document.createElement("input");
  fileInput.type = "file";
  fileInput.accept = "image/*";
  fileInput.style.display = "none";

  uploadButton.addEventListener("click", () => fileInput.click());
  fileInput.addEventListener("change", event => {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      onImageUpload(file); // Call the callback function with the uploaded file
    }
  });

  overlayContent.appendChild(overlayText);
  overlayContent.appendChild(uploadButton);
  overlayContent.appendChild(fileInput);

  return overlayContent;
}
