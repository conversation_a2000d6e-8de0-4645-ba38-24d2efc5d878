// <!DOCTYPE html>
// <html lang="en">
// <head>
//   <meta charset="UTF-8" />
//   <title><PERSON> Marker and <PERSON><PERSON> with Overlay</title>
//   <style>
//     #canvasWrapper {
//       position: relative;
//       display: inline-block;
//     }
//     #mainCanvas {
//       border: 1px solid #ccc;
//       display: block;
//     }
//     .marker, #hoverMarker {
//       position: absolute;
//       width: 24px;
//       height: 24px;
//       background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 0C7.582 0 4 3.582 4 8c0 1.421.382 2.75 1.031 3.906.108.192.221.381.344.563L12 24l6.625-11.531c.102-.151.19-.311.281-.469l.063-.094C20.618 10.75 21 9.421 21 8c0-4.418-3.582-8-8-8zm0 4c2.209 0 4 1.791 4 4s-1.791 4-4 4-4-1.791-4-4 1.791-4 4-4z" fill="%23e74c3c"/><path d="M12 3c-2.761 0-5 2.239-5 5s2.239 5 5 5 5-2.239 5-5-2.239-5-5-5zm0 2c1.657 0 3 1.343 3 3s-1.343 3-3 3-3-1.343-3-3 1.343-3 3-3z" fill="%23c0392b"/></svg>') no-repeat center center;
//       background-size: contain;
//       transform: translate(-50%, -50%);
//       pointer-events: none;
//       z-index: 10;
//     }
//     .outline, #hoverOutline {
//       position: absolute;
//       width: 256px;
//       height: 256px;
//       border: 2px dotted blue;
//       transform: translate(-50%, -50%);
//       pointer-events: none;
//       z-index: 5;
//     }
//     /* The new semi-transparent overlay for the selected tile */
//     .selection-overlay {
//       position: absolute;
//       width: 256px;
//       height: 256px;
//       background-color: rgba(0, 123, 255, 0.1); /* semi-transparent blue */
//       border: 2px solid #007bff;
//       transform: translate(-50%, -50%);
//       pointer-events: none;
//       z-index: 6;
//       border-radius: 4px;
//       box-sizing: border-box;
//     }
//     #tilesContainer {
//       margin-top: 20px;
//       display: flex;
//       flex-wrap: wrap;
//       gap: 10px;
//     }
//     .tile {
//       border: 1px solid #ccc;
//     }
//   </style>
// </head>
// <body>
//   <h2>Click to Place Markers and Crop 256×256 Tiles</h2>

//   <input type="file" id="upload" accept="image/*" /><br /><br />

//   <div id="canvasWrapper">
//     <canvas id="mainCanvas"></canvas>
//     <div id="hoverMarker" style="display: none;"></div>
//     <div id="hoverOutline" style="display: none;"></div>
//   </div>

//   <div id="tilesContainer"></div>

//   <script>
//     const upload = document.getElementById('upload');
//     const canvas = document.getElementById('mainCanvas');
//     const ctx = canvas.getContext('2d');
//     const wrapper = document.getElementById('canvasWrapper');
//     const tilesContainer = document.getElementById('tilesContainer');

//     const hoverMarker = document.getElementById('hoverMarker');
//     const hoverOutline = document.getElementById('hoverOutline');

//     let image = new Image();
//     let markers = [];

//     upload.addEventListener('change', (e) => {
//       const file = e.target.files[0];
//       if (!file) return;
//       const reader = new FileReader();
//       reader.onload = (event) => {
//         image.onload = () => {
//           canvas.width = image.width;
//           canvas.height = image.height;
//           wrapper.style.width = image.width + 'px';
//           wrapper.style.height = image.height + 'px';
//           ctx.drawImage(image, 0, 0);
//         };
//         image.src = event.target.result;
//       };
//       reader.readAsDataURL(file);
//     });

//     canvas.addEventListener('mousemove', (e) => {
//       const rect = canvas.getBoundingClientRect();
//       let x = e.clientX - rect.left;
//       let y = e.clientY - rect.top;
//       const halfSize = 128;

//       // Clamp values to keep outline inside bounds
//       x = Math.max(halfSize, Math.min(canvas.width - halfSize, x));
//       y = Math.max(halfSize, Math.min(canvas.height - halfSize, y));

//       hoverMarker.style.left = `${x}px`;
//       hoverMarker.style.top = `${y}px`;
//       hoverOutline.style.left = `${x}px`;
//       hoverOutline.style.top = `${y}px`;

//       hoverMarker.style.display = 'block';
//       hoverOutline.style.display = 'block';
//     });

//     canvas.addEventListener('mouseleave', () => {
//       hoverMarker.style.display = 'none';
//       hoverOutline.style.display = 'none';
//     });

//     canvas.addEventListener('click', (e) => {
//       const rect = canvas.getBoundingClientRect();
//       let x = e.clientX - rect.left;
//       let y = e.clientY - rect.top;
//       const halfSize = 128;

//       // Clamp values to keep outline inside bounds
//       x = Math.max(halfSize, Math.min(canvas.width - halfSize, x));
//       y = Math.max(halfSize, Math.min(canvas.height - halfSize, y));

//       // Draw marker
//       const marker = document.createElement('div');
//       marker.className = 'marker';
//       marker.style.left = `${x}px`;
//       marker.style.top = `${y}px`;
//       wrapper.appendChild(marker);

//       // Draw 256x256 outline
//       const outline = document.createElement('div');
//       outline.className = 'outline';
//       outline.style.left = `${x}px`;
//       outline.style.top = `${y}px`;
//       wrapper.appendChild(outline);

//       // Draw semi-transparent overlay
//       const overlay = document.createElement('div');
//       overlay.className = 'selection-overlay';
//       overlay.style.left = `${x}px`;
//       overlay.style.top = `${y}px`;
//       wrapper.appendChild(overlay);

//       markers.push({ x, y, marker, outline, overlay });

//       cropTile(x, y);
//     });

//     function cropTile(cx, cy) {
//       const size = 256;
//       const sx = Math.max(0, cx - size / 2);
//       const sy = Math.max(0, cy - size / 2);

//       const sWidth = Math.min(size, canvas.width - sx);
//       const sHeight = Math.min(size, canvas.height - sy);

//       const tileCanvas = document.createElement('canvas');
//       tileCanvas.width = size;
//       tileCanvas.height = size;
//       const tileCtx = tileCanvas.getContext('2d');
//       tileCtx.fillStyle = 'white';
//       tileCtx.fillRect(0, 0, size, size);
//       tileCtx.drawImage(canvas, sx, sy, sWidth, sHeight, 0, 0, sWidth, sHeight);

//       tileCanvas.className = 'tile';
//       tilesContainer.appendChild(tileCanvas);
//     }
//   </script>
// </body>
// </html>
