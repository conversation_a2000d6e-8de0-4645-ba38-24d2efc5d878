// AutoFlash.ts
// Utility for auto-enabling/disabling camera torch (flash) based on ambient brightness

/** @internal */
export interface AutoFlashOptions {
  minBrightness?: number;
  maxBrightness?: number;
  onTorchChange?: (on: boolean) => void;
}

/** @internal */
export class AutoFlash {
  private video: HTMLVideoElement;
  private track: MediaStreamTrack;
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private intervalId: number | null = null;
  private torchOn = false;
  private minBrightness: number;
  private maxBrightness: number;
  private onTorchChange?: (on: boolean) => void;

  constructor(track: MediaStreamTrack, video: HTMLVideoElement, options: AutoFlashOptions = {}) {
    this.video = video;
    this.track = track;
    this.minBrightness = options.minBrightness ?? 60;
    this.maxBrightness = options.maxBrightness ?? 100;
    this.onTorchChange = options.onTorchChange;
    this.canvas = document.createElement('canvas');
    this.canvas.width = 160;
    this.canvas.height = 120;
    this.ctx = this.canvas.getContext('2d')!;
  }

  static async isSupported(track: MediaStreamTrack): Promise<boolean> {
    // Check if the track supports torch
    const capabilities = track.getCapabilities?.();
    return !!(capabilities && 'torch' in capabilities && capabilities.torch);
  }

  start() {
    if (this.intervalId) return;
    this.intervalId = window.setInterval(() => this.monitor(), 1000);
  }

  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.setTorch(false);
  }

  private monitor() {
    if (this.video.readyState < 2) return;
    this.ctx.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);
    const frame = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
    const data = frame.data;
    let brightnessSum = 0;
    const pixelCount = data.length / 4;
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      const brightness = 0.2126 * r + 0.7152 * g + 0.0722 * b;
      brightnessSum += brightness;
    }
    const avgBrightness = brightnessSum / pixelCount;
    if (avgBrightness < this.minBrightness && !this.torchOn) {
      this.setTorch(true);
    } else if (avgBrightness > this.maxBrightness && this.torchOn) {
      this.setTorch(false);
    }
  }

  public setTorch(on: boolean) {
    // @ts-ignore: 'torch' is a valid constraint in supporting browsers
    this.track.applyConstraints({ advanced: [{ torch: on }] }).then(() => {
      this.torchOn = on;
      if (this.onTorchChange) this.onTorchChange(on);
    }).catch(() => {
      // Ignore errors (e.g., not supported)
    });
  }
}