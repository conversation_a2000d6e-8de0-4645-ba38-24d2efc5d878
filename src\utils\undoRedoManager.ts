/** @internal */
export class UndoRedoManager {
  private static instance: UndoRedoManager | null = null; // Singleton instance
  private commands: { undo: () => void; redo: () => void; groupId?: string }[] =
    [];
  private index: number = -1;
  private limit: number = 0;
  private isExecuting: boolean = false;
  private callback?: () => void;

  // Private constructor to prevent direct instantiation
  private constructor() {}

  // Public method to get the singleton instance
  public static getInstance(): UndoRedoManager {
    if (!this.instance) {
      this.instance = new UndoRedoManager();
    }
    return this.instance;
  }

  private removeFromTo(array: any[], from: number, to?: number): number {
    if (to === undefined) {
      array.splice(from, 1);
    } else {
      array.splice(
        from,
        to -
          from +
          1 +
          (to < 0 !== from >= 0 ? 0 : (to < 0 ? -1 : 0) * array.length)
      );
    }
    return array.length;
  }

  private execute(
    command: { undo: () => void; redo: () => void },
    action: "undo" | "redo"
  ): this {
    if (!command || typeof command[action] !== "function") {
      return this;
    }
    this.isExecuting = true;

    command[action]();

    this.isExecuting = false;
    return this;
  }

  public add(command: {
    undo: () => void;
    redo: () => void;
    groupId?: string;
  }): this {
    if (this.isExecuting) {
      return this;
    }
    this.commands.splice(this.index + 1, this.commands.length - this.index);
    this.commands.push(command);

    if (this.limit && this.commands.length > this.limit) {
      this.removeFromTo(this.commands, 0, -(this.limit + 1));
    }

    this.index = this.commands.length - 1;
    if (this.callback) {
      this.callback();
    }
    return this;
  }

  public setCallback(callbackFunc: () => void): void {
    this.callback = callbackFunc;
  }

  public undo(): this {
    let command = this.commands[this.index];
    if (!command) {
      return this;
    }

    const groupId = command.groupId;
    while (command.groupId === groupId) {
      this.execute(command, "undo");
      this.index -= 1;
      command = this.commands[this.index];
      if (!command || !command.groupId) break;
    }

    if (this.callback) {
      this.callback();
    }
    return this;
  }

  public redo(): this {
    let command = this.commands[this.index + 1];
    if (!command) {
      return this;
    }

    const groupId = command.groupId;
    while (command.groupId === groupId) {
      this.execute(command, "redo");
      this.index += 1;
      command = this.commands[this.index + 1];
      if (!command || !command.groupId) break;
    }

    if (this.callback) {
      this.callback();
    }
    return this;
  }

  public clear(): void {
    const prevSize = this.commands.length;
    this.commands = [];
    this.index = -1;

    if (this.callback && prevSize > 0) {
      this.callback();
    }
  }

  public hasUndo(): boolean {
    return this.index !== -1;
  }

  public hasRedo(): boolean {
    return this.index < this.commands.length - 1;
  }

  public getCommands(
    groupId?: string
  ): { undo: () => void; redo: () => void; groupId?: string }[] {
    return groupId
      ? this.commands.filter(c => c.groupId === groupId)
      : this.commands;
  }

  public getIndex(): number {
    return this.index;
  }

  public setLimit(max: number): void {
    this.limit = max;
  }
}
