// Import all rating images
import amsteelBlueE1 from "../../public/assets/RatingImages/AMSTEEL-BLUE/External/<EMAIL>";
import amsteelBlueE2 from "../../public/assets/RatingImages/AMSTEEL-BLUE/External/<EMAIL>";
import amsteelBlueE3 from "../../public/assets/RatingImages/AMSTEEL-BLUE/External/<EMAIL>";
import amsteelBlueE4 from "../../public/assets/RatingImages/AMSTEEL-BLUE/External/<EMAIL>";
import amsteelBlueE5 from "../../public/assets/RatingImages/AMSTEEL-BLUE/External/<EMAIL>";
import amsteelBlueE6 from "../../public/assets/RatingImages/AMSTEEL-BLUE/External/<EMAIL>";
import amsteelBlueE7 from "../../public/assets/RatingImages/AMSTEEL-BLUE/External/<EMAIL>";

import amsteelBlueI1 from "../../public/assets/RatingImages/AMSTEEL-BLUE/Internal/<EMAIL>";
import amsteelBlueI2 from "../../public/assets/RatingImages/AMSTEEL-BLUE/Internal/<EMAIL>";
import amsteelBlueI3 from "../../public/assets/RatingImages/AMSTEEL-BLUE/Internal/<EMAIL>";
import amsteelBlueI4 from "../../public/assets/RatingImages/AMSTEEL-BLUE/Internal/<EMAIL>";
import amsteelBlueI5 from "../../public/assets/RatingImages/AMSTEEL-BLUE/Internal/<EMAIL>";
import amsteelBlueI6 from "../../public/assets/RatingImages/AMSTEEL-BLUE/Internal/<EMAIL>";
import amsteelBlueI7 from "../../public/assets/RatingImages/AMSTEEL-BLUE/Internal/<EMAIL>";

import k100E1 from "../../public/assets/RatingImages/K-100/External/<EMAIL>";
import k100E2 from "../../public/assets/RatingImages/K-100/External/<EMAIL>";
import k100E3 from "../../public/assets/RatingImages/K-100/External/<EMAIL>";
import k100E4 from "../../public/assets/RatingImages/K-100/External/<EMAIL>";
import k100E5 from "../../public/assets/RatingImages/K-100/External/<EMAIL>";
import k100E6 from "../../public/assets/RatingImages/K-100/External/<EMAIL>";
import k100E7 from "../../public/assets/RatingImages/K-100/External/<EMAIL>";

import k100I1 from "../../public/assets/RatingImages/K-100/Internal/<EMAIL>";
import k100I2 from "../../public/assets/RatingImages/K-100/Internal/<EMAIL>";
import k100I3 from "../../public/assets/RatingImages/K-100/Internal/<EMAIL>";

import saturn12E1 from "../../public/assets/RatingImages/SATURN-12/External/<EMAIL>";
import saturn12E2 from "../../public/assets/RatingImages/SATURN-12/External/<EMAIL>";
import saturn12E3 from "../../public/assets/RatingImages/SATURN-12/External/<EMAIL>";
import saturn12E4 from "../../public/assets/RatingImages/SATURN-12/External/<EMAIL>";
import saturn12E5 from "../../public/assets/RatingImages/SATURN-12/External/<EMAIL>";
import saturn12E6 from "../../public/assets/RatingImages/SATURN-12/External/<EMAIL>";
import saturn12E7 from "../../public/assets/RatingImages/SATURN-12/External/<EMAIL>";

import saturn12I1 from "../../public/assets/RatingImages/SATURN-12/Internal/<EMAIL>";
import saturn12I2 from "../../public/assets/RatingImages/SATURN-12/Internal/<EMAIL>";
import saturn12I3 from "../../public/assets/RatingImages/SATURN-12/Internal/<EMAIL>";
import saturn12I4 from "../../public/assets/RatingImages/SATURN-12/Internal/<EMAIL>";
import saturn12I5 from "../../public/assets/RatingImages/SATURN-12/Internal/<EMAIL>";
import saturn12I6 from "../../public/assets/RatingImages/SATURN-12/Internal/<EMAIL>";
import saturn12I7 from "../../public/assets/RatingImages/SATURN-12/Internal/<EMAIL>";

import tenexE1 from "../../public/assets/RatingImages/TENEX/External/<EMAIL>";
import tenexE2 from "../../public/assets/RatingImages/TENEX/External/<EMAIL>";
import tenexE3 from "../../public/assets/RatingImages/TENEX/External/<EMAIL>";
import tenexE4 from "../../public/assets/RatingImages/TENEX/External/<EMAIL>";

// Define types for better type safety
export type Category = "AMSTEEL-BLUE" | "K-100" | "SATURN-12" | "TENEX";
export type Type = "External" | "Internal";

type RatingImageType = {
  files: string[];
};

type RatingCategoryType = {
  External: RatingImageType;
  Internal?: RatingImageType;
};

type RatingImagesType = {
  [key in Category]: RatingCategoryType;
};

// Create the images object with all imported images
export const ratingImages: RatingImagesType = {
  "AMSTEEL-BLUE": {
    External: {
      files: [
        amsteelBlueE1,
        amsteelBlueE2,
        amsteelBlueE3,
        amsteelBlueE4,
        amsteelBlueE5,
        amsteelBlueE6,
        amsteelBlueE7,
      ],
    },
    Internal: {
      files: [
        amsteelBlueI1,
        amsteelBlueI2,
        amsteelBlueI3,
        amsteelBlueI4,
        amsteelBlueI5,
        amsteelBlueI6,
        amsteelBlueI7,
      ],
    },
  },
  "K-100": {
    External: {
      files: [k100E1, k100E2, k100E3, k100E4, k100E5, k100E6, k100E7],
    },
    Internal: {
      files: [k100I1, k100I2, k100I3],
    },
  },
  "SATURN-12": {
    External: {
      files: [
        saturn12E1,
        saturn12E2,
        saturn12E3,
        saturn12E4,
        saturn12E5,
        saturn12E6,
        saturn12E7,
      ],
    },
    Internal: {
      files: [
        saturn12I1,
        saturn12I2,
        saturn12I3,
        saturn12I4,
        saturn12I5,
        saturn12I6,
        saturn12I7,
      ],
    },
  },
  TENEX: {
    External: {
      files: [tenexE1, tenexE2, tenexE3, tenexE4],
    },
  },
};

// Helper function to get images for a specific category and type
export function getImages(category: Category, type: Type): string[] {
  const categoryImages = ratingImages[category];
  if (!categoryImages) {
    console.error(`Category ${category} not found in rating images`);
    return [];
  }

  return type === "Internal"
    ? categoryImages.Internal?.files || []
    : categoryImages.External.files;
}
