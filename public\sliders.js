const sliderHeight = targetHeight - 40;

const contrastSlider = document.createElement('input');
contrastSlider.type = 'range';
contrastSlider.min = '50';
contrastSlider.max = '200';
contrastSlider.value = '100';
contrastSlider.step = '1';
contrastSlider.style.writingMode = 'bt-lr';
contrastSlider.style.WebkitAppearance = 'slider-vertical';
contrastSlider.style.width = '30px';
contrastSlider.style.height = sliderHeight + 'px';
contrastSlider.style.transform = 'rotate(-90deg)';
contrastSlider.style.background = 'transparent';

const brightnessSlider = document.createElement('input');
brightnessSlider.type = 'range';
brightnessSlider.min = '50';
brightnessSlider.max = '200';
brightnessSlider.value = '100';
brightnessSlider.step = '1';
brightnessSlider.style.writingMode = 'bt-lr';
brightnessSlider.style.WebkitAppearance = 'slider-vertical';
brightnessSlider.style.width = '30px';
brightnessSlider.style.height = sliderHeight + 'px';
brightnessSlider.style.transform = 'rotate(-90deg)';
brightnessSlider.style.background = 'transparent';

// Insert sliders and canvas into previewFlex
// previewFlex.appendChild(contrastContainer);
// previewFlex.appendChild(canvas);
// previewFlex.appendChild(brightnessContainer);

// Replace the old preview area with previewFlex
if (rowDiv2 && rowDiv2.parentNode) {
  rowDiv2.parentNode.replaceChild(previewFlex, rowDiv2);
}

// --- Add filter logic ---
let currentBrightness = 100;
let currentContrast = 100;
function updateCanvasFilter() {
  canvas.style.filter = `brightness(${currentBrightness}%) contrast(${currentContrast}%)`;
}
contrastSlider.addEventListener('input', function () {
  currentContrast = this.value;
  updateCanvasFilter();
});
brightnessSlider.addEventListener('input', function () {
  currentBrightness = this.value;
  updateCanvasFilter();
});
updateCanvasFilter();
// ...existing code after preview area...
