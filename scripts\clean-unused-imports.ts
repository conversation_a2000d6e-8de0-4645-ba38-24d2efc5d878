import { Project, SyntaxKind, ImportDeclaration } from 'ts-morph';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

// Get absolute path to this script (for ESM environments)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Resolve project root by walking up from script's location
const projectRoot = path.resolve(__dirname, '..'); // Go one level up from /scripts

const dtsPath = path.resolve(projectRoot, 'build/@unvired/camcrop', 'camcrop.d.ts');
if (!fs.existsSync(dtsPath)) {
  console.error(`❌ .d.ts file not found at: ${dtsPath}`);
  process.exit(1);
}

// const dtsPath = path.resolve('build', 'vite-vanilla-ts-lib-starter.d.ts'); // Adjust path to your rollup .d.ts file
const project = new Project({
  compilerOptions: { allowJs: false },
  useInMemoryFileSystem: false,
});

const sourceFile = project.addSourceFileAtPath(dtsPath);

// Step 1: Collect all used identifiers in the file
const usedIdentifiers = new Set<string>();

sourceFile.forEachDescendant((node) => {
  if (
    node.getKind() === SyntaxKind.Identifier &&
    node.getParent()?.getKind() !== SyntaxKind.ImportSpecifier &&
    node.getParent()?.getKind() !== SyntaxKind.ImportClause
  ) {
    usedIdentifiers.add(node.getText());
  }
});

// Step 2: Clean up import declarations
sourceFile.getImportDeclarations().forEach((importDecl: ImportDeclaration) => {
  const namedImports = importDecl.getNamedImports();
  const defaultImport = importDecl.getDefaultImport();
  const namespaceImport = importDecl.getNamespaceImport();

  // Check if this import is still used
  const keepNamed = namedImports.filter((spec) =>
    usedIdentifiers.has(spec.getName())
  );
  const keepDefault =
    defaultImport && usedIdentifiers.has(defaultImport.getText());
  const keepNamespace =
    namespaceImport && usedIdentifiers.has(namespaceImport.getText());

  if (keepNamed.length === 0 && !keepDefault && !keepNamespace) {
    importDecl.remove(); // Remove whole import
  } else {
    // Remove unused named imports
    namedImports.forEach((spec) => {
      if (!usedIdentifiers.has(spec.getName())) {
        spec.remove();
      }
    });
    if (defaultImport && !keepDefault) {
      importDecl.removeDefaultImport();
    }
    if (namespaceImport && !keepNamespace) {
      importDecl.removeNamespaceImport();
    }
  }
});

// Step 3: Save the result
sourceFile.saveSync();

console.log('🧹 Removed unused imports from rollup .d.ts!');
