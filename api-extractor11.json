{"$schema": "https://developer.microsoft.com/json-schemas/api-extractor/v7/api-extractor.schema.json", "mainEntryPointFilePath": "./build/dist/vite-vanilla-ts-lib-starter.d.ts", "dtsRollup": {"enabled": true, "untrimmedFilePath": "./build/dist/vite-vanilla-ts-lib-starter.untrimmed.d.ts", "publicTrimmedFilePath": "./build/dist/vite-vanilla-ts-lib-starter.d.ts"}, "compiler": {"tsconfigFilePath": "./tsconfig.json"}, "projectFolder": ".", "apiReport": {"enabled": false}, "docModel": {"enabled": false}, "messages": {"compilerMessageReporting": {"default": {"logLevel": "warning"}}, "extractorMessageReporting": {"default": {"logLevel": "warning"}}}, "newlineKind": "lf"}