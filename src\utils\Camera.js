// const overlayImg = new Image();
// overlayImg.src = ropePatternOverlayImage;
// let overlayLoaded = false;
// overlayImg.onload = function() { overlayLoaded = true; };
// overlayImg.onerror = function() { overlayLoaded = false; };

// function drawVideoOnCanvas() {
//   // ...existing code...
//   ctx.globalAlpha = 0.4;  // Set opacity (0 = fully transparent, 1 = fully opaque)
//   if (overlayLoaded) {
//     ctx.drawImage(overlayImg, rectX, rectY, rectWidth, rectHeight);
//   }
//   ctx.globalAlpha = 1;
//   // ...existing code...
// }

const ropePatternOverlayImage = ''


export class camera {
  constructor(params)
}
function capture(success, failure, params) {
  let overlayTheme = 'light'
  const overlayImg = new Image();
  overlayImg.src = ropePatternOverlayImage;
  let overlayLoaded = false;
  overlayImg.onload = function() { 
    console.log("loaded overlay image");
    overlayLoaded = true; 
    };
  overlayImg.onerror = function() { 
    console.error("Error loading overlay image");
    overlayLoaded = false;
    };
  let rectObj;
  var HIGHEST_POSSIBLE_Z_INDEX = 99999;
  let preferredWidth = params[0].width;
  let preferredHeight = params[0].height;
  var targetWidth = screen.width / 2;
  var targetHeight = screen.height / 2;
  let widthRatio = targetWidth / preferredWidth;
  let rectRatio = 0;
  console.log("widthRatio:", widthRatio);
  let heightRatio = targetHeight / preferredHeight;
  console.log("heightRatio:", heightRatio);
  // var x = window.matchMedia("(max-width: 500px)");
  currentCamera = 'environment';

  var imgWidth = targetWidth / 3;
  var imgHeight = targetHeight
  if (imgWidth < preferredWidth) {
    imgWidth = targetWidth / 2
  }
  if (imgWidth < preferredWidth) {
    imgWidth = preferredWidth
  }

  var localMediaStream;
  console.log("Width : ", window.innerWidth);
  console.log("Height : ", window.innerHeight);

  console.log("image Height : ", imgHeight);
  console.log("image Width : ", imgWidth);

  var animationFrameId = null;

  const canvasModal = document.createElement('div');
  canvasModal.style.position = 'fixed';
  canvasModal.style.top = '0'
  canvasModal.style.left = '0';
  canvasModal.style.height = '100%'
  canvasModal.style.width = '100%';
  canvasModal.style.backgroundColor = 'rgb(143, 143, 143)';
  canvasModal.style.Zindex = '9999';
  canvasModal.setAttribute('id', 'cameraModal')

  // Create the flex container
  const flexContainer = document.createElement('div');
  canvasModal.appendChild(flexContainer);
  flexContainer.id = 'flex-container';
  flexContainer.style.display = 'flex';
  flexContainer.style.backgroundColor = 'black';
  flexContainer.style.flexDirection = 'column';
  flexContainer.style.position = 'absolute';
  flexContainer.style.left = '50%';
  flexContainer.style.top = '50%';
  flexContainer.style.borderRadius = '10px';
  flexContainer.style.transform = 'translate(-50%, -50%)';

  const rowDiv1 = document.createElement('div');
  rowDiv1.style.height = '50px';
  rowDiv1.style.display = 'flex';
  rowDiv1.style.justifyContent = 'flex-end'; // Align buttons to the end
  const rowDiv2 = document.createElement('div');
  rowDiv2.style.display = 'flex';
  rowDiv2.style.alignItems = 'center';

  var video = document.createElement("video");
  var canvas = document.createElement("canvas");
  // rowDiv2.appendChild(canvas);
  // video.width = targetWidth;
  canvas.width = targetWidth;
  canvas.height = targetHeight;
  canvas.objectFit = 'cover';


  var captureButton = document.createElement("button")
  captureButton.style.cursor = 'pointer';
  captureButton.style.height = '60px';
  captureButton.style.width = '60px';
  captureButton.style.borderRadius = '30px';
  captureButton.style.marginLeft = '5em';
  captureButton.style.marginBottom = '5px';
  captureButton.style.backgroundColor = '#ffffff';
  captureButton.style.border = '5px solid grey';
  captureButton.style.position = 'relative';

  var cancelButton = document.createElement("button");
  cancelButton.style.height = '25px';
  cancelButton.style.margin = '10px';
  cancelButton.innerHTML = '&#10005';
  cancelButton.style.color = '#ffffff';
  cancelButton.style.cursor = 'pointer';
  cancelButton.style.height = '25px';
  cancelButton.style.width = '25px';
  cancelButton.style.fontSize = '19px';
  cancelButton.style.backgroundColor = 'transparent';
  cancelButton.style.background = 'transparent';
  cancelButton.style.borderStyle = 'none';
  // cancelButton.style.color = 'white';

  var increaseRectBtn = document.createElement("button");
  increaseRectBtn.id = 'iRectBtn';
  increaseRectBtn.style.height = '25px';
  increaseRectBtn.style.margin = '10px';
  increaseRectBtn.innerHTML = '&plus;';
  increaseRectBtn.style.color = '#ffffff';
  increaseRectBtn.style.cursor = 'pointer';
  increaseRectBtn.style.height = '25px';
  increaseRectBtn.style.width = '25px';
  increaseRectBtn.style.fontSize = '19px';
  increaseRectBtn.style.backgroundColor = 'transparent';
  increaseRectBtn.style.background = 'transparent';
  // increaseRectBtn.style.borderStyle = 'none';
  increaseRectBtn.style.border = 'solid 1px white';
  increaseRectBtn.style.borderRadius = '5px';
  increaseRectBtn.title = 'Increase overlay size';


  var decreaseRectBtn = document.createElement("button");
  decreaseRectBtn.id = 'dRectBtn';
  decreaseRectBtn.style.height = '25px';
  decreaseRectBtn.style.margin = '10px';
  decreaseRectBtn.innerHTML = '&minus;';
  decreaseRectBtn.style.color = '#ffffff';
  decreaseRectBtn.style.cursor = 'pointer';
  decreaseRectBtn.style.height = '25px';
  decreaseRectBtn.style.width = '25px';
  decreaseRectBtn.style.fontSize = '19px';
  decreaseRectBtn.style.backgroundColor = 'transparent';
  decreaseRectBtn.style.background = 'transparent';
  decreaseRectBtn.style.border = 'solid 1px white';
  decreaseRectBtn.style.borderRadius = '5px';
  decreaseRectBtn.title = 'Decrease overlay size';

  var switchCamButton = document.createElement("button")
  switchCamButton.style.cursor = 'pointer';
  switchCamButton.style.height = '40px';
  switchCamButton.style.width = '40px';
  switchCamButton.style.borderRadius = '30px';
  switchCamButton.style.marginRight = '5px';
  switchCamButton.innerHTML = `<svg class="svg-icon" viewBox="0 0 20 20">
          <path d="M12.319,5.792L8.836,2.328C8.589,2.08,8.269,2.295,8.269,2.573v1.534C8.115,4.091,7.937,4.084,7.783,4.084c-2.592,0-4.7,2.097-4.7,4.676c0,1.749,0.968,3.337,2.528,4.146c0.352,0.194,0.651-0.257,0.424-0.529c-0.415-0.492-0.643-1.118-0.643-1.762c0-1.514,1.261-2.747,2.787-2.747c0.029,0,0.06,0,0.09,0.002v1.632c0,0.335,0.378,0.435,0.568,0.245l3.483-3.464C12.455,6.147,12.455,5.928,12.319,5.792 M8.938,8.67V7.554c0-0.411-0.528-0.377-0.781-0.377c-1.906,0-3.457,1.542-3.457,3.438c0,0.271,0.033,0.542,0.097,0.805C4.149,10.7,3.775,9.762,3.775,8.76c0-2.197,1.798-3.985,4.008-3.985c0.251,0,0.501,0.023,0.744,0.069c0.212,0.039,0.412-0.124,0.412-0.34v-1.1l2.646,2.633L8.938,8.67z M14.389,7.107c-0.34-0.18-0.662,0.244-0.424,0.529c0.416,0.493,0.644,1.118,0.644,1.762c0,1.515-1.272,2.747-2.798,2.747c-0.029,0-0.061,0-0.089-0.002v-1.631c0-0.354-0.382-0.419-0.558-0.246l-3.482,3.465c-0.136,0.136-0.136,0.355,0,0.49l3.482,3.465c0.189,0.186,0.568,0.096,0.568-0.245v-1.533c0.153,0.016,0.331,0.022,0.484,0.022c2.592,0,4.7-2.098,4.7-4.677C16.917,9.506,15.948,7.917,14.389,7.107 M12.217,15.238c-0.251,0-0.501-0.022-0.743-0.069c-0.212-0.039-0.411,0.125-0.411,0.341v1.101l-2.646-2.634l2.646-2.633v1.116c0,0.174,0.126,0.318,0.295,0.343c0.158,0.024,0.318,0.034,0.486,0.034c1.905,0,3.456-1.542,3.456-3.438c0-0.271-0.032-0.541-0.097-0.804c0.648,0.719,1.022,1.659,1.022,2.66C16.226,13.451,14.428,15.238,12.217,15.238"></path>
      </svg>`;
  switchCamButton.style.fontSize = '20px';
  switchCamButton.style.lineHeight = '0';
  switchCamButton.style.alignSelf = 'center';
  switchCamButton.style.backgroundColor = '#ffffff';

  // Create note (first row)
  let note = document.createElement('p');
  note.style.fontSize = '20px';
  // note.style.color = 'yellow'
  note.innerHTML = "Orient Rope to Pattern Overlay";
  note.style.color = 'white';
  note.style.textAlign = 'center';
  note.style.gridColumn = "1 / span 2"; // Centered across both columns
  note.style.marginTop = '5px'

  // Create the third row div
  const rowDiv3 = document.createElement('div');
  rowDiv3.style.display = 'grid';
  rowDiv3.style.gridTemplateRows = '40px 70px';
  rowDiv3.style.gridTemplateColumns = '90% 10%'; // Capture button (90%), Switch button (10%)
  // rowDiv3.style.gridTemplateRows = '1fr 1fr'; // Two rows

  // Style capture button (center it)
  captureButton.style.justifySelf = "center";

  // Style switch button (align it to the right)
  switchCamButton.style.justifySelf = "end";

  // Append elements
  rowDiv3.appendChild(note); // First row (spans both columns)
  rowDiv3.appendChild(captureButton); // Second row, left (centered)
  rowDiv3.appendChild(switchCamButton); // Second row, right (aligned right)
  // Create a new style element
  var styleElement = document.createElement('style');

  // Set the inner text of the style element to your CSS code
  styleElement.innerText = `
      .svg-icon {
      width: 1em;
      height: 1em;
      }
  
      .svg-icon path,
      .svg-icon polygon,
      .svg-icon rect {
      fill: #000000;
      }
  
      .svg-icon circle {
      stroke: #000000;
      stroke-width: 1;
      }`;

  // Append the style element to the head of the document
  document.head.appendChild(styleElement);

  rowDiv1.appendChild(decreaseRectBtn);// Append cancelButton to rowDiv1
  rowDiv1.appendChild(increaseRectBtn);
  rowDiv1.appendChild(cancelButton);

  // Create the <select> element
const themeSelect = document.createElement('select');
themeSelect.id = 'themeSelector';
themeSelect.style.padding = '6px 12px';
themeSelect.style.border = '1px solid #ccc';
themeSelect.style.borderRadius = '4px';
themeSelect.style.fontSize = '14px';
themeSelect.style.margin = '5px';

themeSelect.addEventListener('change', (e) => {
  const selectedTheme = e.target.value;
  console.log('Theme changed to:', selectedTheme);
  
  // You can handle theme change logic here
  if (selectedTheme === 'dark') {
    overlayTheme = 'dark';
  } else {
    overlayTheme = 'light';
  }
});


// Create "Light" option
const lightOption = document.createElement('option');
lightOption.value = 'light';
lightOption.textContent = 'Light';
lightOption.selected = true;

// Create "Dark" option
const darkOption = document.createElement('option');
darkOption.value = 'dark';
darkOption.textContent = 'Dark';

// Append options to the select element
themeSelect.appendChild(lightOption);
themeSelect.appendChild(darkOption);
rowDiv1.appendChild(themeSelect);

  rowDiv3.style.justifyContent = 'center';
  rowDiv3.style.justifyItems = 'center';

  flexContainer.appendChild(rowDiv1);
  flexContainer.appendChild(rowDiv2);
  flexContainer.appendChild(rowDiv3);

  // Variables to store current filter values
let currentBrightness = 100;
let currentContrast = 100;

// --- Slider Icon SVGs ---
const contrastIcon = document.createElement('div');
contrastIcon.innerHTML = `<svg fill="yellow" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
	 width="30px" height="30px" viewBox="0 0 100 100" enable-background="new 0 0 100 100" xml:space="preserve">
<g>
	<path d="M50,12.5c-20.712,0-37.5,16.793-37.5,37.502C12.5,70.712,29.288,87.5,50,87.5c20.712,0,37.5-16.788,37.5-37.498
		C87.5,29.293,70.712,12.5,50,12.5z M50.124,22.443C65.265,22.51,77.56,34.848,77.56,50.002c0,15.155-12.295,27.488-27.436,27.555
		V22.443z"/>
</g>
</svg>`;
contrastIcon.style.display = 'flex';
contrastIcon.style.justifyContent = 'center';
contrastIcon.style.alignItems = 'center';

const brightnessIcon = document.createElement('div');
brightnessIcon.innerHTML = `<svg width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="5" fill="yellow"/><g stroke="yellow" stroke-width="2"><line x1="12" y1="2" x2="12" y2="5"/><line x1="12" y1="19" x2="12" y2="22"/><line x1="2" y1="12" x2="5" y2="12"/><line x1="19" y1="12" x2="22" y2="12"/><line x1="4.22" y1="4.22" x2="6.34" y2="6.34"/><line x1="17.66" y1="17.66" x2="19.78" y2="19.78"/><line x1="4.22" y1="19.78" x2="6.34" y2="17.66"/><line x1="17.66" y1="6.34" x2="19.78" y2="4.22"/></g></svg>`;
brightnessIcon.style.display = 'flex';
brightnessIcon.style.justifyContent = 'center';
brightnessIcon.style.alignItems = 'center';

const contrastSlider = document.createElement('input');
contrastSlider.type = 'range';
contrastSlider.min = '50';
contrastSlider.max = '200';
contrastSlider.value = '100';
contrastSlider.step = '1';
// contrastSlider.style.writingMode = 'bt-lr';
contrastSlider.style.WebkitAppearance = 'slider-vertical';
contrastSlider.style.width = '30px';
contrastSlider.style.position= 'absolute';
contrastSlider.style.left = '0';
contrastSlider.style.cursor = 'pointer';
// contrastSlider.style.height = (targetHeight - 40) + 'px';
contrastSlider.style.margin = '0 0 0 0';
contrastSlider.style.marginTop = '30px';
contrastSlider.style.zIndex = '1';
contrastSlider.style.background = 'transparent';

const brightnessSlider = document.createElement('input');
brightnessSlider.type = 'range';
brightnessSlider.min = '50';
brightnessSlider.max = '200';
brightnessSlider.value = '100';
brightnessSlider.step = '1';
brightnessSlider.style.position= 'absolute';
brightnessSlider.style.right = '0';
brightnessSlider.style.WebkitAppearance = 'slider-vertical';
brightnessSlider.style.width = '30px';
brightnessSlider.style.zIndex = '1';
brightnessSlider.style.cursor = 'pointer';
// brightnessSlider.style.height = (targetHeight - 40) + 'px';
brightnessSlider.style.margin = '0 0 0 0';
brightnessSlider.style.marginTop = '30px';
// brightnessSlider.style.transform = 'rotate(-90deg)';
brightnessSlider.style.background = 'transparent';

function setSliderHeights() {
  const halfHeight = canvas.height / 2;
  // contrastSlider.style.height = halfHeight + 'px';
  // brightnessSlider.style.height = halfHeight + 'px';
}
// setSliderHeights();
window.addEventListener('resize', setSliderHeights);

// Insert contrast slider, canvas, brightness slider in rowDiv2
const contrastWrapper = document.createElement('div');
contrastWrapper.style.display = 'flex';
contrastWrapper.id = 'contrastWrapper';
contrastWrapper.style.flexDirection = 'column';
contrastWrapper.style.alignItems = 'center';
contrastWrapper.style.position = 'absolute';
contrastWrapper.style.left = '0';
contrastWrapper.style.zIndex = '1';
// contrastWrapper.style.top = '50%';
contrastWrapper.style.transform = 'translateY(-50%)';

const brightnessWrapper = document.createElement('div');
brightnessWrapper.style.display = 'flex';
brightnessWrapper.id = 'brightnessWrapper';
brightnessWrapper.style.flexDirection = 'column';
brightnessWrapper.style.alignItems = 'center';
brightnessWrapper.style.position = 'absolute';
brightnessWrapper.style.right = '0';
brightnessWrapper.style.zIndex = '1';
// brightnessWrapper.style.top = '50%';
brightnessWrapper.style.transform = 'translateY(-50%)';

// Add margin between icon and slider
contrastIcon.style.marginBottom = '12px';
brightnessIcon.style.marginBottom = '12px';

contrastWrapper.appendChild(contrastIcon);
contrastWrapper.appendChild(contrastSlider);
brightnessWrapper.appendChild(brightnessIcon);
brightnessWrapper.appendChild(brightnessSlider);
rowDiv2.appendChild(contrastWrapper);
rowDiv2.appendChild(canvas);
rowDiv2.appendChild(brightnessWrapper);

// Update canvas filter on slider change
function updateCanvasFilter() {
  canvas.style.filter = `brightness(${currentBrightness}%) contrast(${currentContrast}%)`;
}

brightnessSlider.addEventListener('input', function() {
  currentBrightness = this.value;
  updateCanvasFilter();
});
contrastSlider.addEventListener('input', function() {
  currentContrast = this.value;
  updateCanvasFilter();
});
  // Stop drawing updates
  function stopDrawing() {
    cancelAnimationFrame(animationFrameId);
  }

  function drawVideoOnCanvas() {
    // canvas.height= video.videoHeight;
    // canvas.width= video.videoWidth;
    var ctx = canvas.getContext("2d");
    ctx.clearRect(0, 0, canvas.width, canvas.height); // Clear the canvas
    ctx.fillStyle = 'white';
    // 1. Draw the full unblurred video onto the canvas
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // 2. Calculate rectangle position
    const rectX = ((targetWidth - imgWidth) / 2) - (rectRatio / 4);
    const rectY = (targetHeight - imgHeight) / 2;
    const rectWidth = imgWidth + (rectRatio / 2);
    const rectHeight = imgHeight;

    // 3. Create a blurred copy of the canvas
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = canvas.width;
    tempCanvas.height = canvas.height;
    const tempCtx = tempCanvas.getContext('2d');
    tempCtx.drawImage(canvas, 0, 0); // Copy the current canvas state (full video)

    ctx.globalAlpha = 0.9;
    ctx.filter = 'blur(15px)';
    ctx.filter = 'none';
    ctx.globalAlpha = 1;

    tempCtx.globalAlpha = 0.9;
    tempCtx.filter = 'blur(1px)';
    tempCtx.fillRect(0, 0, canvas.width, canvas.height); // Apply blur to the entire temp canvas
    tempCtx.filter = 'none';
    tempCtx.globalAlpha = 1;

    // 4. Clear the rectangle on the blurred copy
    tempCtx.clearRect(rectX, rectY, rectWidth, rectHeight);

    // 5. Draw the blurred copy back onto the original canvas
    ctx.drawImage(tempCanvas, 0, 0);

    // 6. Draw the rectangle border
    ctx.strokeStyle = 'yellow';
    ctx.lineWidth = 4;
    ctx.strokeRect(rectX, rectY, rectWidth, rectHeight);

    // img.onload = function () {
    // ctx.drawImage(overlayImg, rectX, rectY, rectWidth, rectHeight); // Place inside the rectangle
    // ctx.strokeRect(rectObj.x, rectObj.y, rectObj.height, rectObj.width);
    // };

    ctx.globalAlpha = 0.4;  // Set opacity (0 = fully transparent, 1 = fully opaque)
    if (overlayLoaded) {
      if(overlayTheme === 'dark') {
        // Create an offscreen canvas the size of the overlay
        const overlayCanvas = document.createElement('canvas');
        overlayCanvas.width = rectWidth;
        overlayCanvas.height = rectHeight;
        const overlayCtx = overlayCanvas.getContext('2d');

        // Step 1: Draw the overlay image on the offscreen canvas
        overlayCtx.drawImage(overlayImg, 0, 0, rectWidth, rectHeight);

        // Step 2: Use 'source-in' to tint only non-transparent pixels
        overlayCtx.globalCompositeOperation = 'source-in';
        overlayCtx.fillStyle = 'rgba(0, 0, 0, 0.4)'; // Dark color with desired opacity
        overlayCtx.fillRect(0, 0, rectWidth, rectHeight);

        // Step 3: Draw the tinted result onto the main canvas
        ctx.drawImage(overlayCanvas, rectX, rectY);
    } else {
      ctx.drawImage(overlayImg, rectX, rectY, rectWidth, rectHeight);
    }
  }
    ctx.globalAlpha = 1;

    // Define rectangle object
    rectObj = {
      x: ((targetWidth - rectRatio / 2) - imgWidth) / 2,
      y: (targetHeight - imgHeight) / 2,
      height: imgWidth + (rectRatio / 2),
      width: imgHeight
    }
    // console.log("rectObj.x + rectObj.width",rectObj.x+','+ rectObj.width)
    // console.log("canvas.width",canvas.width)
    if (rectObj.height <= canvas.width) {
        ctx.strokeRect(rectObj.x, rectObj.y, rectObj.height, rectObj.width);
    }
    // ctx.strokeRect(rectObj.x, rectObj.y, rectObj.height, rectObj.width);

  }

  switchCamButton.onclick = function () {
    if (currentCamera === 'user') {
      currentCamera = 'environment'; // Switch to back camera
    } else {
      currentCamera = 'user'; // Switch to front camera
    }

    // Stop the current stream (if any)
    localMediaStream.getTracks().forEach(function (track) {
      track.stop();
    });

    previewcamera();
  }

  // Call the draw function repeatedly to update the canvas
  function animate() {
    drawVideoOnCanvas();
    animationFrameId = requestAnimationFrame(animate);
  }
  animate();

  //   old working login start
  captureButton.onclick = function () {
    // Get the actual video and canvas dimensions
    const videoWidth = video.videoWidth;
    const videoHeight = video.videoHeight;
    const canvasWidth = canvas.width;
    const canvasHeight = canvas.height;

    // Calculate aspect ratios and scaling factors
    const scaleX = videoWidth / canvasWidth;
    const scaleY = videoHeight / canvasHeight;

    console.log("Video Dimensions:", videoWidth, videoHeight);
    console.log("Canvas Dimensions:", canvasWidth, canvasHeight);
    console.log("Scaling Factors:", scaleX, scaleY);

    // Convert rectObj to match video resolution
    const videoRect = {
      x: rectObj.x * scaleX,
      y: rectObj.y * scaleY,
      width: rectObj.height * scaleX,
      height: rectObj.width * scaleY
    };

    console.log("Corrected Video Crop Area:", videoRect);

    // Create a cropped canvas that matches the video resolution
    let croppedCanvas = document.createElement("canvas");
    croppedCanvas.width = videoWidth;  // Match video width
    croppedCanvas.height = videoHeight; // Match video height
    let croppedCtx = croppedCanvas.getContext("2d");

    console.log("Upscaled Cropped Canvas Dimensions W x H:", croppedCanvas.width, croppedCanvas.height);

    // Fill croppedCanvas with the full video frame (to match video resolution)
    croppedCtx.drawImage(video, 0, 0, videoWidth, videoHeight);

    // Create another canvas for final cropping
    let finalCanvas = document.createElement("canvas");
    finalCanvas.width = videoRect.width;
    finalCanvas.height = videoRect.height;
    let finalCtx = finalCanvas.getContext("2d");

    // Crop the correct portion from the upscaled canvas
    finalCtx.drawImage(
      croppedCanvas,
      videoRect.x, videoRect.y, videoRect.width, videoRect.height, // Source (video)
      0, 0, videoRect.width, videoRect.height // Destination (cropped output)
    );

    console.log("Final Cropped Image Dimensions W x H:", finalCanvas.width, finalCanvas.height);

    // Convert to image data
    let imageData = finalCanvas.toDataURL("image/png");
    imageData = imageData.replace("data:image/png;base64,", "");

    // Hide modal and show the cropped image
    if (imageData) {
      canvasModal.style.visibility = 'hidden';
      showPreviewModal(imageData, success, failure, params, capture);
    }
  };

  cancelButton.onclick = function () {
    // styleElement.remove();
    // document.head.appendChild(styleElement);
    // canvasModal.style.display = 'none';
    // canvasModal.remove()
    stopDrawing();
    console.clear();
    cleanup();
  };

  increaseRectBtn.onclick = function () {
    if (rectObj.height < canvas.width || rectRatio == 0) {
      rectRatio += Math.min(10, canvas.width - rectObj.height);
    }
  }

  decreaseRectBtn.onclick = function () {
    if((rectObj.height-10) > preferredWidth) {
      rectRatio -= 10;
    }
  }

  function cleanup() {
    let camModal = document.getElementById('cameraModal');
    video.srcObject = null;
    video.remove()
    camModal.remove();
    styleElement.remove();
  }


  function showPreviewModal(imageData, success, failure, params, capture) {
    let modal = document.createElement("div");
    modal.id = "previewModal";
    modal.style.position = "fixed";
    modal.style.top = "0";
    modal.style.left = "0";
    modal.style.width = "100vw";
    modal.style.height = "100vh";
    modal.style.backgroundColor = 'rgb(143, 143, 143)';
    modal.style.display = "flex";
    modal.style.flexDirection = "column";
    modal.style.justifyContent = "center";
    modal.style.alignItems = "center";

    let container = document.createElement("div");
    container.style.display = "flex";
    container.style.flexDirection = "column";
    container.style.alignItems = "center";
    container.style.background = "transparent";
    container.style.padding = "10px";
    container.style.borderRadius = "10px";

    let img = document.createElement("img");
    img.src = "data:image/jpeg;base64," + imageData;
    img.style.maxWidth = "80vw";
    img.style.maxHeight = "70vh";
    img.style.borderRadius = "10px";

    let buttonBar = document.createElement("div");
    buttonBar.style.display = "flex";
    buttonBar.style.justifyContent = "space-between";
    buttonBar.style.width = "100%";
    buttonBar.style.marginTop = "10px";

    let retakeBtn = document.createElement("button");
    retakeBtn.innerText = "Retake";
    retakeBtn.style.borderRadius = "10px";
    retakeBtn.style.padding = "10px 20px";
    retakeBtn.style.marginRight = "10px";
    retakeBtn.style.cursor = "pointer";
    retakeBtn.style.backgroundColor = "black";
    retakeBtn.onclick = function () {
      document.body.removeChild(modal);
      canvasModal.style.visibility = 'visible'
      // animate()
      // capture(success, failure, params);
    };
    let acceptBtn = document.createElement("button");
    acceptBtn.innerText = "Use Photo";
    acceptBtn.style.borderRadius = "10px";
    acceptBtn.style.padding = "10px 20px";
    acceptBtn.style.cursor = "pointer";
    acceptBtn.style.backgroundColor = "black";
    acceptBtn.onclick = function () {
      document.body.removeChild(modal);
      let res = {
        "ImageData": img.src,
        "GuidelineBoxWidth": img.width,
        "GuidelineBoxHeight": img.height
      };
      if (localMediaStream.stop) {
        localMediaStream.stop();
      } else {
        localMediaStream.getTracks().forEach(track => track.stop());
      }

      // localMediaStream.getTracks().forEach(function (track) {
      //   track.stop();
      // });
      stopDrawing();
      cleanup();
      success(res);
    };

    buttonBar.appendChild(retakeBtn);
    buttonBar.appendChild(acceptBtn);
    container.appendChild(img);
    container.appendChild(buttonBar);
    modal.appendChild(container);
    document.body.appendChild(modal);
  }


  navigator.getUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia || navigator.msGetUserMedia || navigator.mediaDevices.getUserMedia;

  var successCallback = function (stream) {
    localMediaStream = stream;
    video.onloadedmetadata = () => {
      console.log("Camera Resolution:", video.videoWidth, "x", video.videoHeight);
    };
    if ("srcObject" in video) {
      video.srcObject = localMediaStream;
    } else {
      video.src = window.URL.createObjectURL(localMediaStream);
    }
    video.width = '100%';
    video.height = 'auto';
    video.play();
    canvasModal.style.zIndex = HIGHEST_POSSIBLE_Z_INDEX;
    document.body.appendChild(canvasModal);
  };

  var errorCallback = (error) => {
    console.log("camera inialise error:",error);
    failure(error)
  }


  function previewcamera() {
    if (navigator.getUserMedia) {

      navigator.getUserMedia({
        video: {
          facingMode: currentCamera,
          width: { min: 640, ideal: 1280, max: 1920 },
          height: { min: 480, ideal: 720, max: 1080 },
          aspectRatio: 16 / 9
        }
      },
        successCallback,
        errorCallback
      );
    } else {
      return error("camera devices not found");
    }
  }
  previewcamera();
}