import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import {MatButtonModule} from '@angular/material/button';
// import { cv, cvTranslateError } from 'opencv-wasm';
// import * as cv from '@opencv.js/wasm';
// import { NgOpenCVModule } from 'ngx-open-cv';
import { NgOpenCVService } from '@lightsailed/ngx-open-cv';

import { setWasmPaths } from '@tensorflow/tfjs-backend-wasm';
import * as tf from '@tensorflow/tfjs';
import '@tensorflow/tfjs-backend-wasm';

declare global {
  interface Window { cv: any; }
}
declare var window: Window;

declare var cv : any;
@Component({
  selector: "app-root",
  imports: [MatButtonModule],
  templateUrl: "./app.component.html",
  styleUrl: "./app.component.css",
  providers:[NgOpenCVService]
})
export class AppComponent implements OnInit {
  title = "angular-tsCropper-test";
  result: unknown;
  cameraOptions : any;
  private cv: any;
  openCVLoadResult: any;
  openCVBuildInfo: string;

  constructor(private openCVService: NgOpenCVService, private cdr: ChangeDetectorRef) {
    this.openCVBuildInfo = ''
  }

  async ngOnInit() {
    setWasmPaths('/tfjs-wasm/');
    try {
      await tf.setBackend('wasm').then(() => {
        console.log('WASM backend set successfully');
      }).catch((error) => {
        console.error('Error setting WASM backend:', error);
      });
      await tf.ready();
    } catch (e) {
      console.warn('WASM backend failed, falling back to WebGL');
      await tf.setBackend('webgl');
      await tf.ready();
    }


    const tensor = tf.tensor([1, 2, 3, 4]);
    const squared = tensor.square();
    this.result = squared.toString();
    console.log('Current backend:', tf.getBackend());

    this.openCVService.isReady$.subscribe({
      next: (result) => {
        if (result.ready) {
          // OpenCV is loaded, ready for user to upload image
          const cv = window.cv;
          this.openCVBuildInfo = cv.getBuildInformation();
          this.cdr.detectChanges();
          console.log('OpenCV Build Information:', this.openCVBuildInfo);
          console.log('OpenCV is ready');
          
        } else if (result.error) {
          console.error('Failed to load OpenCV');
        }
      }
    });
  }
  originalImageSizeMB: number | null = null;
  claheTimeSeconds: number | null = null;

  onImageUpload(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const file = input.files[0];
      this.originalImageSizeMB = +(file.size / (1024 * 1024)).toFixed(2);
      const reader = new FileReader();
      reader.onload = (e: any) => {
        const img = new Image();
        img.onload = () => {
          const t0 = performance.now();
          this.showCLAHE(img);
          const t1 = performance.now();
          this.claheTimeSeconds = +((t1 - t0) / 1000).toFixed(3);
          // Show the original image in the UI
          const inputImgElem = document.getElementById('inputImage') as HTMLImageElement;
          if (inputImgElem) {
            inputImgElem.src = img.src;
            inputImgElem.style.display = 'inline';
          }
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  }

  showCLAHE(imgElement: HTMLImageElement) {
    const cv = window.cv;
    this.openCVBuildInfo = cv.getBuildInformation();
    // Create Mat from image
    let src = cv.imread(imgElement);
    let gray = new cv.Mat();
    cv.cvtColor(src, gray, cv.COLOR_RGBA2GRAY, 0);
    // Apply CLAHE
    let clahe = new cv.CLAHE(2.0, new cv.Size(8, 8));
    let dst = new cv.Mat();
    clahe.apply(gray, dst);
    // Show result in canvas at original image size
    let canvas = document.getElementById('claheCanvas') as HTMLCanvasElement;
    if (!canvas) {
      src.delete();
      gray.delete();
      dst.delete();
      clahe.delete();
      return;
    }
    canvas.width = imgElement.naturalWidth;
    canvas.height = imgElement.naturalHeight;
    cv.imshow(canvas, dst);
    // Clean up
    src.delete();
    gray.delete();
    dst.delete();
    clahe.delete();
  }

  openEditor() {
    // const options: CropOptions = { imageUrl: "sample1.jpg" };
    // const editor = Editor.init(options);
    // // editor.show();
    // editor.on("success", (result?: unknown) => {
    //   const res = result as resultOptions;
    //   console.log("Success:", res);
    //   // document.getElementById('resultImg')?.src = res.image;
    // });
    // editor.on("error", (err?: unknown) => {
    //   console.error("Error:", err);
    // });
    // editor.on("cancelled", () => {
    //   console.log("Editor cancelled");
    // });
  }
  
  open(mode: string) {
    switch(mode) {
    case 'insCamera':
      // this.cameraOptions = 
      // this.CropOptions = 
      // this.resultOptions = 
      break;
     case 'insGallery':

      break;
      case 'extCamera':
      break;
      case 'extGallery':

      break;
      case 'intCamera':

      break;
      case 'intGallery':

      break;
      case 'otherCamera':

      break;
      case 'otherGallery':

      break;
      default:
      console.error("Unknown mode:", mode);
      break;
    }
    
  }
}
