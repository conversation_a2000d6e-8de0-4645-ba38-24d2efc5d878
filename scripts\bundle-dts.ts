import * as fs from "fs";
import * as path from "path";

// Read the generated declaration files
const editorDts = fs.readFileSync("package/src/editor/editor.d.ts", "utf8");
const editorModeStoreDts = fs.readFileSync(
  "package/src/editor/editorModeStore.d.ts",
  "utf8"
);

// Extract only the types and interfaces we need
function extractExportedTypes(content: string): string {
  const lines = content.split("\n");
  const exportedLines: string[] = [];
  let inExportedBlock = false;
  let braceCount = 0;

  for (const line of lines) {
    if (
      line.includes("export interface") ||
      line.includes("export type") ||
      line.includes("export enum") ||
      line.includes("export declare")
    ) {
      inExportedBlock = true;
      exportedLines.push(line);
      braceCount += (line.match(/\{/g) || []).length;
      braceCount -= (line.match(/\}/g) || []).length;
    } else if (inExportedBlock) {
      exportedLines.push(line);
      braceCount += (line.match(/\{/g) || []).length;
      braceCount -= (line.match(/\}/g) || []).length;

      if (braceCount <= 0 && (line.includes("}") || line.includes(";"))) {
        inExportedBlock = false;
        braceCount = 0;
      }
    }
  }

  return exportedLines.join("\n");
}

// Extract types from editor.d.ts
const editorTypes = extractExportedTypes(editorDts);

// Extract types from editorModeStore.d.ts
const editorModeStoreTypes = extractExportedTypes(editorModeStoreDts);

// Create the bundled declaration file content
const bundledContent = `// Generated declaration file for Editor only
${editorTypes}

${editorModeStoreTypes}

export { Editor } from './editor/editor';
export type { CropOptions, resultOptions } from './editor/editor';
export type { EditorMode } from './editor/editorModeStore';
export { editorModeType } from './editor/editorModeStore';
`
  .replace(/export declare/g, "declare")
  .replace(/export interface/g, "interface")
  .replace(/export type/g, "type")
  .replace(/export enum/g, "enum");

// Write the bundled declaration file
const outputPath = path.join("package", "image-editor.es.d.ts");
fs.writeFileSync(outputPath, bundledContent);

// Clean up the src directory
fs.rmSync("package/src", { recursive: true, force: true });

console.log("✅ Bundled declaration file created at:", outputPath);
