<button class="bottom-bar-button">InsightAI camera</button>
<button class="bottom-bar-button">InsightAI Gallery</button>
<button class="bottom-bar-button">External camera</button>
<button class="bottom-bar-button">External Gallery</button>
<button class="bottom-bar-button">Internal camera</button>
<button class="bottom-bar-button">Internal Gallery</button>
<button class="bottom-bar-button">others camera</button>
<button class="bottom-bar-button">Others Gallery</button>
<!-- <p id="app">{{result}}</p> -->
<!-- <img src="/sample1.jpg" height='400' width='300' alt="Sample Image"> -->
<input type="file" accept="image/*" (change)="onImageUpload($event)" />
<br />
<div>
    @if(originalImageSizeMB !== null) {
          <ng-container>
            <strong>Original Image Size:</strong> {{ originalImageSizeMB }} MB
        </ng-container>
    }

  <br />
  @if(claheTimeSeconds !== null) {
    <ng-container>
    <strong>CLAHE Conversion Time:</strong> {{ claheTimeSeconds }} seconds
  </ng-container>
  }
</div>
<div style="display: flex; flex-wrap: wrap; gap: 16px; max-width: 100vw; align-items: flex-start;">
  <div style="flex: 1 1 0; min-width: 0; max-width: 48%;">
    <img id="inputImage" [src]="''" style="max-width: 100%; height: auto; display:none; border:1px solid #ccc;" alt="Original Image" />
    <div style="text-align:center;">Original Image</div>
  </div>
  <div style="flex: 1 1 0; min-width: 0; max-width: 48%;">
    <canvas id="claheCanvas" style="max-width: 100%; height: auto; border:1px solid #ccc;"></canvas>
    <div style="text-align:center;">CLAHE Image</div>
  </div>
</div>
<p>TensorFlow.js WASM Backend Test: {{ result }}</p>
<p>OpenCV Load Result: {{ openCVBuildInfo }}</p>
