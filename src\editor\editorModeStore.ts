import { atom } from "nanostores";
import { set, get, del } from "idb-keyval";

/**
 * @internal
 */
export type EditorMode = "home" | "crop" | "annotate";
/** @internal */
export type imageType = string | Blob | ArrayBuffer;
/** @internal */
export type shapeType = "Rectangle" | "Oval";

/** @internal */
export const editorMode = atom<EditorMode>("home");
/** @internal */
export const image = atom<imageType>("");
/** @internal */
export const shapeStore = atom<shapeType>("Rectangle");

/**
 * @internal
 */
export enum editorModeType {
  home = "home",
  crop = "crop",
  annotate = "annotate",
}

// export const editorModeType = {
//     home: 'home',
//     crop: 'crop',
//     annotate: 'annotate'
// }
/**
 * @internal
 */
export function setCropMode() {
  editorMode.set("crop");
}

/** @internal */
export function setAnnotateMode() {
  editorMode.set("annotate");
}

/**
 * @internal
 */
export function setHomeMode() {
  editorMode.set("home");
}

/**
 * @internal
 */
export function getEditorMode() {
  editorMode.get();
}

/** @internal */
export const originalImageKey = atom<string | null>(null);
/** @internal */
export const modifiedImageKey = atom<string | null>(null);

/** @internal */
export async function setOriginalImage(image: imageType): Promise<void> {
  const key = "original-image";
  await set(key, image);
  originalImageKey.set(key);
}

/** @internal */
export async function setModifiedImage(image: imageType): Promise<void> {
  const key = "modified-image";
  await set(key, image);
  modifiedImageKey.set(key);
}

/** @internal */
export async function loadImageByKey(
  key: string
): Promise<imageType | undefined> {
  return await get(key);
}

/** @internal */
export async function clearImages(): Promise<void> {
  await del("original-image");
  await del("modified-image");
  originalImageKey.set(null);
  modifiedImageKey.set(null);
}
