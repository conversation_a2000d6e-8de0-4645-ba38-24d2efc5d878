<!DOCTYPE html>
<html>
<head>
  <title>OpenCV.js Standalone Test</title>
</head>
<body>
  <script>
    // Preload the WASM file and set up Module before loading OpenCV.js
    fetch('./opencv.wasm')
      .then(response => {
        console.log('WASM fetch response status:', response.status);
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.arrayBuffer();
      })
      .then(bytes => {
        console.log('WASM file loaded, size:', bytes.byteLength);
        
        // Set up Module with the WASM binary
        window.Module = {
          wasmBinary: bytes,
          onRuntimeInitialized() {
            console.log('OpenCV runtime initialized!');
            
            // Wait a bit for cv to be available
            setTimeout(() => {
              console.log('Checking for cv object...');
              console.log('window.cv:', window.cv);
              console.log('typeof window.cv:', typeof window.cv);
              
              // Try to get cv from different possible locations
              let cv = window.cv || this.cv || Module.cv;
              
              if (cv) {
                console.log('cv object found!');
                
                // Print build information
                console.log('=== OpenCV Build Information ===');
                if (cv.getBuildInformation) {
                  console.log('Build Information:', cv.getBuildInformation());
                } else {
                  console.log('cv.getBuildInformation not available');
                }
                
                // Print version information
                if (cv.version) {
                  console.log('OpenCV Version:', cv.version);
                } else {
                  console.log('cv.version not available');
                }
                
                // Print available properties on cv object
                console.log('Available cv properties:', Object.keys(cv));
                
                // OpenCV is ready!
                let mat = new cv.Mat(100, 100, cv.CV_8UC4);
                console.log('Mat created:', mat);
                mat.delete();
                document.body.innerHTML += '<p>OpenCV.js loaded and Mat created!</p>';
                
                // Display build info on page
                if (cv.getBuildInformation) {
                  document.body.innerHTML += '<h2>Build Information:</h2><pre>' + cv.getBuildInformation() + '</pre>';
                }
              } else {
                console.error('cv object not found!');
                console.log('Available global objects:', Object.keys(window));
                document.body.innerHTML += '<p>Error: cv object not found after OpenCV initialization</p>';
              }
            }, 100);
          }
        };
        
        console.log('Module configured with wasmBinary, loading OpenCV.js...');
        
        // Load OpenCV.js after setting up the Module
        const script = document.createElement('script');
        script.src = 'opencv.js';
        script.onload = () => {
          console.log('OpenCV.js script loaded');
          console.log('window.cv after script load:', window.cv);
        };
        script.onerror = (error) => console.error('Failed to load OpenCV.js:', error);
        document.body.appendChild(script);
      })
      .catch(error => {
        console.error('Failed to load WASM file:', error);
        document.body.innerHTML += '<p>Error: Failed to load OpenCV WASM file</p>';
      });
  </script>
</body>
</html>