const OpenAI = require("openai");
 
const client = new OpenAI({
    apiKey: "sk-C2JOQnhgUFBPSbGWBIZYiJu6sdVUCoKskFeQgJ48cUS6M1Ch",    
    baseURL: "https://api.moonshot.ai/v1",
});
 
async function main() {
    const completion = await client.chat.completions.create({
        model: "kimi-k2-0711-preview",         
        messages: [ 
            {role: "system", content: "You are <PERSON><PERSON>, an AI assistant provided by Moonshot AI. You are proficient in Chinese and English conversations. You provide users with safe, helpful, and accurate answers. You will reject any questions involving terrorism, racism, or explicit content. Moonshot AI is a proper noun and should not be translated."},
            {role: "user", content: "Hello, my name is <PERSON>. What is 1+1?"}
        ],
        temperature: 0.6
    });
    console.log(completion.choices[0].message.content);
}
 
main();