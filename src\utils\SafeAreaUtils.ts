import type { DeviceInfoResult } from './DeviceInfo';

/**
 * Utility class for handling safe area insets consistently across camera and editor components
 * @internal
 */
export class SafeAreaUtils {
  /**
   * Calculate top inset for iOS webview environments
   * @param device DeviceInfoResult from DeviceInfo
   * @returns number of pixels to add as top padding/margin
   */
  static calculateTopInset(device: DeviceInfoResult): number {
    // Only apply adjustments for iOS webview
    if (!device.isIOSWebView) {
      return 0;
    }

    // Use safe area inset if available
    if (device.safeAreaInsetTop > 0) {
      return device.safeAreaInsetTop;
    }

    // Fallback values for iOS webview when CSS env() is not available
    // These are common status bar heights for different iOS devices
    if (device.isIPhone) {
      // iPhone X and newer have taller status bars
      if (window.screen.height >= 812) {
        return 44; // iPhone X, XS, XR, 11, 12, 13, 14 series
      }
      return 20; // iPhone 8 and older
    }

    if (device.isIPad) {
      return 24; // iPad status bar height
    }

    return 20; // Default fallback
  }

  /**
   * Calculate bottom inset for iOS webview environments
   * @param device DeviceInfoResult from DeviceInfo
   * @returns number of pixels to add as bottom padding/margin
   */
  static calculateBottomInset(device: DeviceInfoResult): number {
    // Only apply adjustments for iOS webview
    if (!device.isIOSWebView) {
      return 0;
    }

    // If device has home indicator, we need bottom spacing
    if (device.hasHomeIndicator) {
      // Use safe area inset if available
      if (device.safeAreaInsetBottom > 0) {
        return device.safeAreaInsetBottom;
      }

      // Fallback for home indicator devices when CSS env() is not available
      return 34; // Standard home indicator height
    }

    return 0; // No bottom inset needed for devices without home indicator
  }

  /**
   * Get complete safe area insets for iOS webview
   * @param device DeviceInfoResult from DeviceInfo
   * @returns object with top and bottom insets
   */
  static getSafeAreaInsets(device: DeviceInfoResult): { top: number; bottom: number } {
    return {
      top: this.calculateTopInset(device),
      bottom: this.calculateBottomInset(device)
    };
  }

  /**
   * Check if safe area adjustments should be applied
   * @param device DeviceInfoResult from DeviceInfo
   * @returns boolean indicating if adjustments are needed
   */
  static shouldApplySafeAreaAdjustments(device: DeviceInfoResult): boolean {
    return device.isIOSWebView;
  }
}
