// import "../style.css";
import styleText from "../style.css?inline";
import { DeviceInfo } from "../utils/DeviceInfo";
import { SafeAreaUtils } from "../utils/SafeAreaUtils";
import "@cropper/element-image";
import Cropper from "cropperjs";
import { TopToolbar } from "./topToolBar";
import { BottomToolbar } from "./bottomToolBar";
// import type CropperHandle from '@cropper/element-handle';
import { editorMode, setHomeMode, clearImages } from "./editorModeStore";
import uploadIconPath from "../../public/assets/add-image.svg?inline";

/** @public */
export interface CropperInitOptions {
  imageUrl?: string | Blob | ArrayBuffer;
  location: boolean;
  fileName?: string;
  mode?: "Observation"; //! "internal"
  abrasionMode?: "External" | "Insternal" | "Others";
  maxRating?: number | string; //7 or 4
  currentRating?: number | string; //current rating set in rating slider in App
  productName?: string; //"AMSTEEL-BLUE"
  isDemo?: string | boolean;
  report?: boolean; //only for observations not insight AT
  annotation?: boolean;
  ratingImagesScale?: boolean;
}

/** @public */
export interface InsightAICropperOptions {
  imageUrl?: string | Blob | ArrayBuffer;
  location: boolean;
  fileName?: string;
  mode?: "InsightAI"; //! "internal"
  abrasionMode?: undefined;
  maxRating?: undefined; //7 or 4
  currentRating?: undefined; //current rating set in rating slider in App
  productName?: undefined; //"AMSTEEL-BLUE"
  isDemo?: false;
  report?: false; //only for observations not insight AT
  annotation?: false;
  ratingImagesScale?: false;
}

/** @public */
export type cropOptions = CropperInitOptions | InsightAICropperOptions;

/** @public */
export interface cameraResultOptions {
  image: string | Blob | ArrayBuffer;
  latitude: string | number;
  longitude: string | number;
}

/** @public */
export interface cropperResultOptions {
  image: string | Blob | ArrayBuffer;
  report?: boolean;
  rating?: number | string;
  texts: string[];
  latitude: string | number;
  longitude: string | number;
}

/** @public */
export type resultOptions = cameraResultOptions | cropperResultOptions;

/** @public */
// export interface resultOptions {
//   image: string | Blob | ArrayBuffer;
//   report?: boolean;
//   rating?: number | string;
//   texts: string[];
//   latitude: string | number;
//   longitude: string | number;
// }

/**
 * Represents a useful class.
 * @public
 */
export class Editor {
  // DeviceInfo is now used for safe area insets and environment detection
  /** @internal */
  private listeners: {
    [event in "success" | "error" | "cancelled"]?: ((data?: unknown) => void)[];
  } = {};

  /** @internal */
  private static instance: Editor | null = null;
  /** @internal */
  private options?: cropOptions;
  /** @internal */
  private _isDestroyed = false;

  /** @internal */
  private lattitude: number = 0;

  /** @internal */
  private longitude: number = 0;

  /**
   * Subscribe to an event: 'success', 'error', 'cancelled'.
   * Usage: editor.on('success', fn)
   * @public
   */
  public on(
    event: "success" | "error" | "cancelled",
    callback: (data?: unknown) => void
  ) {
    if (!this.listeners[event]) this.listeners[event] = [];
    this.listeners[event].push(callback);
  }

  // /**
  //  * Unsubscribe from an event.
  //  * @public
  //  */
  // public off(
  //   event: "success" | "error" | "cancelled",
  //   callback: (data?: unknown) => void
  // ) {
  //   if (!this.listeners[event]) return;
  //   this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
  // }

  /** @internal */
  private emit(event: "success" | "error" | "cancelled", data?: unknown) {
    if (this._isDestroyed) return;
    // Always include latitude/longitude in result if possible
    if (event === "success" && typeof data === "object" && data) {
      if ("latitude" in data && "longitude" in data) {
        (data as { latitude: number; longitude: number }).latitude =
          this.lattitude;
        (data as { latitude: number; longitude: number }).longitude =
          this.longitude;
      }
    }
    (this.listeners[event] || []).forEach(cb => cb(data));
    if (event === "success" || event === "error" || event === "cancelled") {
      this._isDestroyed = true;
      clearImages().then(() => {
        if (this.overlay && document.body.contains(this.overlay)) {
          // Only remove from DOM, do not emit any other event
          document.body.removeChild(this.overlay);
        }
        Editor.instance = null;
      });
    }
  }

  /**
   * Destroy the editor instance, remove from DOM, and cleanup. Only for external/manual use.
   * @internal
   */
  public destroy(): void {
    if (this._isDestroyed) return;
    this._isDestroyed = true;
    if (this.overlay && document.body.contains(this.overlay)) {
      document.body.removeChild(this.overlay);
    }
    Editor.instance = null;
    clearImages();
  }

  private container: HTMLDivElement;
  private topToolbar: HTMLDivElement;
  private editorContainer: HTMLDivElement;
  private bottomToolbar: HTMLDivElement;
  private overlay: HTMLDivElement;
  /** @internal */
  private cropper: Cropper | null = null;
  private shadowRoot: ShadowRoot;
  private imageContainer: HTMLDivElement | null = null;
  private imageElement: HTMLImageElement | null = null;

  // SAMSON configurations
  // private report : boolean = true;
  // private currentRating? : number | string;
  // private productName? : string;
  // private report : boolean = false;

  /** @internal */
  private constructor(
    cropOptions?: cropOptions,
    resultOptions?: resultOptions
  ) {
    console.log(cropOptions);
    console.log(resultOptions);
    this.options = cropOptions;
    document.addEventListener(
      "dblclick",
      function (e) {
        e.preventDefault();
      },
      { passive: false }
    );
    // Create fullscreen overlay
    this.overlay = document.createElement("div");
    this.overlay.className = "editor-fullscreen-overlay";
    Object.assign(this.overlay.style, {
      position: "fixed",
      top: "0",
      left: "0",
      width: "100%",
      height: "100%",
      zIndex: "99999",
      background: "rgba(0,0,0,0.85)", // semi-transparent dark overlay
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
    });
    document.body.appendChild(this.overlay);

    // Attach shadow root to the overlay
    this.shadowRoot = this.overlay.attachShadow({ mode: "open" });

    // const shadowRoot = this.attachShadow({ mode: 'open' });
    // this.shadowRoot.innerHTML = `
    //   <style>${styleText}</style>
    //   <div>...</div>
    // `;
    // Inject the CSS into the shadow root
    const style = document.createElement("style");
    style.textContent = styleText;
    this.shadowRoot.appendChild(style);

    // Initialize the editor
    this.container = document.createElement("div");
    this.container.className = "editor-global-container";
    this.topToolbar = TopToolbar.getInstance().element;
    this.editorContainer = document.createElement("div");

    // Wire up toolbar events
    this.topToolbar.addEventListener("editor:back", () => {
      const mode = editorMode.get();

      const imageContainer = this.getImageContainer();
      if (!imageContainer) return;
      if (mode === "crop") {
        // Remove cropper and restore image
        const cropperEl =
          this.editorContainer.querySelector(".cropper-container");
        if (cropperEl) this.editorContainer.removeChild(cropperEl);
        this.editorContainer.innerHTML = "";
        // Ensure all images are visible
        const imgs = imageContainer.querySelectorAll("img");
        imgs.forEach(img => {
          img.style.display = "";
        });
        this.editorContainer.appendChild(imageContainer);
      } else if (mode === "annotate") {
        // Remove annotation UI and restore image
        this.editorContainer.innerHTML = "";
        this.editorContainer.appendChild(imageContainer);
      }
      setHomeMode();
    });
    this.topToolbar.addEventListener("editor:save", () => {
      if (editorMode.get() === "crop") {
        this.finalizeCrop();
        setHomeMode();
      } else if (editorMode.get() === "annotate") {
        // TODO: Implement annotation save logic
        setHomeMode();
      } else if (editorMode.get() === "home") {
        // Emit 'success' event with resultOptions
        const imageContainer = this.getImageContainer();
        let imageBase64 = "";
        if (imageContainer) {
          const img = imageContainer.querySelector("img");
          if (img && img.src) {
            imageBase64 = img.src;
          }
        }
        this.emit("success", {
          image: imageBase64,
          report: this.options?.report,
          rating: this.options?.currentRating,
        });
        // Destroy NanoStore data and the editor instance
        clearImages().then(() => this.destroy());
      }
    });
    this.topToolbar.addEventListener("editor:done", () => {
      // When 'done' is clicked in crop or annotation mode, update this.imageContainer
      const mode = editorMode.get();
      if (mode === "crop") {
        // If in crop mode, finalize crop and update imageContainer
        if (this.cropper) {
          const cropperSelection = this.cropper.getCropperSelection();
          if (cropperSelection) {
            const editorContainer = this.getEditorContainer();
            const editorContainerRect = editorContainer.getBoundingClientRect();
            const canvasWidth = editorContainerRect.width;
            const canvasHeight = editorContainerRect.height;
            cropperSelection
              .$toCanvas({ width: canvasWidth, height: canvasHeight })
              .then((canvasElement: HTMLCanvasElement) => {
                const croppedImage = canvasElement.toDataURL("image/png");
                // Create new image with cropped result
                const img = document.createElement("img");
                img.src = croppedImage;
                img.alt = "Cropped Image";
                img.style.maxWidth = "100%";
                img.style.maxHeight = "100%";
                img.style.objectFit = "contain";
                // Create a new image container
                const imageContainer = document.createElement("div");
                imageContainer.className = "image-container";
                imageContainer.appendChild(img);
                // Add image to container
                editorContainer.innerHTML = "";
                editorContainer.appendChild(imageContainer);
                // Update reference
                this.imageContainer = imageContainer;
                // Reset cropper
                this.cropper = null;
                setHomeMode();
              });
            return;
          }
        }
      } else if (mode === "annotate") {
        // TODO: Implement annotation save logic and update this.imageContainer
        // Example: after annotation is finalized, create a new imageContainer, assign to this.imageContainer, and show it
        // For now, just set home mode
        setHomeMode();
      }
    });
    this.topToolbar.addEventListener("editor:cancel", () => {
      const editor =
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (window as any).Editor?.instance || (window as any).editor || undefined;
      // If Editor singleton is accessible, call destroy
      try {
        // Try to get the singleton via the class if possible
        import("./editor").then(() => {
          if (Editor && Editor["instance"]) {
            Editor["instance"]?.destroy();
          }
        });
      } catch {
        // Fallback: try window.editor
        if (editor && typeof editor.destroy === "function") {
          editor.destroy();
        }
      }
      this.emit("cancelled", {});
    });
    this.editorContainer.className = "editor-container";
    this.bottomToolbar = new BottomToolbar(this).getToolbar();

    this.setupStyles();

    if (this.options?.imageUrl) {
      this.loadImageReference(this.options.imageUrl);
    } else {
      this.showUploadUI();
    }

    // Append the container to the shadow root
    this.shadowRoot.appendChild(this.container);

    // Add a resize event listener to adjust the height dynamically
    window.addEventListener("resize", () => this.adjustEditorContainerHeight());
  }

  /**
   * Get the singleton instance of the Editor
   * @internal
   */
  public static getInstance(options?: cropOptions): Editor {
    if (!Editor.instance) {
      if (options?.imageUrl) Editor.instance = new Editor(options);
      else Editor.instance = new Editor();
      if (options?.location && navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          position => {
            if (Editor.instance) {
              Editor.instance.lattitude = position.coords.latitude;
              Editor.instance.longitude = position.coords.longitude;
            }
          },
          () => {}
        );
      } else {
        console.log("Geolocation is not supported by this browser.");
      }
    }
    return Editor.instance;
  }

  /**
   * Public API: initialize and show the editor
   * @public
   */
  public static init(options?: cropOptions): Editor {
    const instance = Editor.getInstance(options);
    instance.show();
    return instance;
  }

  /**
   * Show the editor (attach to DOM, etc)
   * @internal
   */
  public show(): void {
    // If already appended, do nothing
    if (!document.body.contains(this.overlay)) {
      document.body.appendChild(this.overlay);
    }
    // Optionally, could set overlay/style/display here if needed
  }

  /** @internal */
  public getCropContainer(): HTMLDivElement {
    return this.container;
  }

  /** @internal */
  public getEditorContainer(): HTMLDivElement {
    return this.editorContainer;
  }

  /** @internal */
  // Add a public getter for the shadowRoot
  public getShadowRoot(): ShadowRoot {
    return this.shadowRoot;
  }

  /** @internal */
  private setupStyles(): void {
    // Remove margin and padding from the body and html elements
    // document.body.style.margin = "0";
    // document.body.style.padding = "0";
    // document.body.style.height = "100vh";
    // document.body.style.overflow = "hidden"; // Prevent scrolling
    // if (import.meta.env.DEV) {
    //   const link = document.createElement("link");
    //   link.setAttribute("rel", "stylesheet");
    //   link.setAttribute("href", "src/style.css");
    //   this.shadowRoot.appendChild(link);
    // } else {
    //   // const linkElem = document.createElement('link');
    //   // linkElem.setAttribute('rel', 'stylesheet');
    //   // linkElem.setAttribute('href', './typescript-image-editor.css');
    //   // this.shadowRoot.appendChild(linkElem);
    //   // ---------------------------------------------
    //   const linkElem = document.createElement("link");
    //   linkElem.setAttribute("rel", "stylesheet");
    //   linkElem.setAttribute("href", "./typescript-image-editor.css");
    //   this.shadowRoot.appendChild(linkElem);
    // }
  }

  /** @internal */
  private adjustEditorContainerHeight(): void {
    // Use a timeout to ensure elements are rendered before measuring
    // setTimeout(() => {
    //     // Set fixed heights for topToolbar and bottomToolbar
    //     const topToolbarHeight = 25; // Fixed height in pixels
    //     const bottomToolbarHeight = 25; // Fixed height in pixels
    //     const editorContainerHeight = window.innerHeight - topToolbarHeight - bottomToolbarHeight; // Remaining height
    //     this.topToolbar.style.height = `5vh`;
    //     this.editorContainer.style.height = `90vh`;
    //     this.bottomToolbar.style.height = `5vh`;
    // }, 0);
  }

  /** @internal */
  private showUploadUI(): void {
    // const div1 = document.createElement("div");
    // div1.className = "h-screen flex items-end justify-center bg-gray-100";
    // const dropdownContainer = document.createElement("div");
    // dropdownContainer.className = "relative";

    // const dropdownButton = document.createElement("button");
    // dropdownButton.id = "dropdownButton";
    // dropdownButton.className =
    //   "w-40 bg-blue-600 text-white px-4 py-2 rounded shadow hover:bg-blue-700 flex items-center justify-between";
    // dropdownButton.innerHTML = `
    //         <span id="selectedText">Rectangle</span>
    //         <svg class="w-4 h-4 ml-2 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
    //             <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"/>
    //         </svg>
    //     `;

    // const dropdownMenu = document.createElement("div");
    // dropdownMenu.id = "dropdownMenu";
    // dropdownMenu.className =
    //   "absolute hidden bg-white border border-gray-300 rounded shadow w-40";
    // dropdownMenu.innerHTML = `
    //         <ul>
    //             <li><button data-value="Rectangle" class="dropdown-option block w-full text-left px-4 py-2 hover:bg-gray-100">Rectangle</button></li>
    //             <li><button data-value="Oval" class="dropdown-option block w-full text-left px-4 py-2 hover:bg-gray-100">Oval</button></li>
    //         </ul>
    //     `;

    // dropdownContainer.appendChild(dropdownButton);
    // dropdownContainer.appendChild(dropdownMenu);

    // Use references from created elements, not document
    // const selectedText = dropdownButton.firstElementChild as HTMLSpanElement;
    // const options = (dropdownMenu.firstElementChild?.children ??
    //   []) as unknown as NodeListOf<HTMLButtonElement>;

    // dropdownButton.addEventListener("click", () => {
    //   const rect = dropdownButton.getBoundingClientRect();
    //   const spaceBelow = window.innerHeight - rect.bottom;
    //   const spaceAbove = rect.top;

    //   // Remove existing positioning classes
    //   dropdownMenu.classList.remove("bottom-full", "mb-2", "top-full", "mt-2");

    //   // Add appropriate positioning classes based on available space
    //   if (spaceBelow < 100 && spaceAbove >= 100) {
    //     dropdownMenu.classList.add("bottom-full", "mb-2");
    //   } else {
    //     dropdownMenu.classList.add("top-full", "mt-2");
    //   }

    //   dropdownMenu.classList.toggle("hidden");
    // });

    // Array.from(options).forEach(option => {
    //   option.addEventListener("click", () => {
    //     const selectedValue = option.getAttribute("data-value");
    //     if (selectedText) {
    //       selectedText.textContent = selectedValue;
    //     }
    //     dropdownMenu.classList.add("hidden");
    //     // Trigger any necessary callbacks or events
    //     // this.onShapeChange?.(selectedValue || 'Rectangle');
    //   });
    // });

    // Close dropdown when clicking outside
    // window.addEventListener("click", e => {
    //   if (
    //     !dropdownButton.contains(e.target as Node) &&
    //     !dropdownMenu.contains(e.target as Node)
    //   ) {
    //     dropdownMenu.classList.add("hidden");
    //   }
    // });

    // div1.appendChild(dropdownContainer);
    // document.body.appendChild(div1);
    // Clear the container
    this.container.innerHTML = "";
    // Create a wrapper for centering the upload UI
    const uploadWrapper = document.createElement("div");
    uploadWrapper.style.display = "flex";
    uploadWrapper.style.flexDirection = "column";
    uploadWrapper.style.justifyContent = "center";
    uploadWrapper.style.alignItems = "center";
    uploadWrapper.style.height = "100%";
    uploadWrapper.style.position = "absolute";
    uploadWrapper.style.top = "50%";
    uploadWrapper.style.left = "50%";
    uploadWrapper.style.transform = "translate(-50%, -50%)";

    // Create the upload button using the add-image SVG
    const uploadButton = document.createElement("button");
    uploadButton.style.border = "none";
    uploadButton.style.background = "none";
    uploadButton.style.cursor = "pointer";

    const uploadIcon = document.createElement("img");
    uploadIcon.src = uploadIconPath;
    uploadIcon.alt = "Add Image";
    uploadIcon.style.width = "100px";
    uploadIcon.style.height = "100px";

    uploadButton.appendChild(uploadIcon);

    // Add click event to the button to open file browser
    uploadButton.addEventListener("click", () => {
      const fileInput = document.createElement("input");
      fileInput.type = "file";
      fileInput.accept = "image/*";
      fileInput.style.display = "none";

      fileInput.addEventListener("change", event => {
        const target = event.target as HTMLInputElement;
        if (target.files && target.files[0]) {
          const reader = new FileReader();
          reader.onload = () => {
            const result = reader.result;
            if (typeof result === "string") {
              this.loadImage(result);
            } else {
              console.error("Unexpected result type from FileReader");
            }
          };
          reader.readAsDataURL(target.files[0]);
        }
      });

      // Trigger the file input click
      fileInput.click();
    });

    // Create the image scale UI
    const imageScaleUI = document.createElement("div");
    imageScaleUI.style.marginTop = "20px";
    imageScaleUI.textContent = "Image Scale: 100%"; // Placeholder for actual scale UI

    // Append the button and image scale UI to the wrapper
    uploadWrapper.appendChild(uploadButton);
    uploadWrapper.appendChild(imageScaleUI);

    // Append the wrapper to the overlay
    this.overlay.innerHTML = ""; // Clear any existing overlay content
    this.overlay.appendChild(uploadWrapper);

    // Append the overlay to the container
    this.container.appendChild(this.overlay);

    // Adjust the editor container height
    this.adjustEditorContainerHeight();
  }

  /** @internal */
  private loadImage(imageSrc: string): void {
    // Clear the container and overlay
    this.container.innerHTML = "";
    this.overlay.innerHTML = "";

    // Calculate heights considering mobile viewport, iOS status bar, and home indicator using DeviceInfo
    (async () => {
      const device = await DeviceInfo.getInfo();
      // Use SafeAreaUtils for consistent safe area calculations
      const safeInsets = SafeAreaUtils.getSafeAreaInsets(device);
      const topInset = safeInsets.top;
      const bottomInset = safeInsets.bottom;
      const topToolbarHeight = 25;
      const bottomToolbarHeight = 45;
      const viewportHeight = window.innerHeight;
      const editorContainerHeight = viewportHeight - (topToolbarHeight + topInset) - (bottomToolbarHeight + bottomInset);

      // Set up container structure
      this.container.className = "editor-global-container";
      this.container.style.height = `${viewportHeight}px`;

      this.topToolbar.className = "editor-top-toolbar";
      this.topToolbar.style.height = `${topToolbarHeight + topInset}px`;
      this.topToolbar.style.paddingTop = `${topInset}px`;

      this.editorContainer.className = "editor-container";
      this.editorContainer.style.height = `${editorContainerHeight}px`;

      this.bottomToolbar.style.height = `${bottomToolbarHeight + bottomInset}px`;
      this.bottomToolbar.style.paddingBottom = `${bottomInset}px`;
    })();

    // Create image container
    this.imageContainer = document.createElement("div");
    this.imageContainer.className = "image-container";

    this.imageElement = document.createElement("img");
    this.imageElement.src = imageSrc;
    this.imageElement.alt = "Image for editing";
    this.imageElement.style.maxWidth = "100%";
    this.imageElement.style.maxHeight = "100%";
    this.imageElement.style.objectFit = "contain";

    this.imageElement.onload = () => {
      if (this.imageContainer) {
        this.imageContainer.innerHTML = "";
        this.imageContainer.appendChild(this.imageElement!);
      }
      this.container.appendChild(this.topToolbar);
      this.editorContainer.innerHTML = "";
      this.editorContainer.appendChild(this.imageContainer!);
      this.container.appendChild(this.editorContainer);
      this.container.appendChild(this.bottomToolbar);
    };

    // Add resize observer to handle viewport and safe area changes
    const resizeObserver = new ResizeObserver(async () => {
      const device = await DeviceInfo.getInfo();
      // Use SafeAreaUtils for consistent safe area calculations
      const safeInsets = SafeAreaUtils.getSafeAreaInsets(device);
      const topInset = safeInsets.top;
      const bottomInset = safeInsets.bottom;
      const topToolbarHeight = 25;
      const bottomToolbarHeight = 45;
      const newViewportHeight = window.innerHeight;
      const newEditorHeight = newViewportHeight - (topToolbarHeight + topInset) - (bottomToolbarHeight + bottomInset);
      this.container.style.height = `${newViewportHeight}px`;
      this.topToolbar.style.height = `${topToolbarHeight + topInset}px`;
      this.topToolbar.style.paddingTop = `${topInset}px`;
      this.topToolbar.style.alignItems = "end";
      this.editorContainer.style.height = `${newEditorHeight}px`;
      this.bottomToolbar.style.height = `${bottomToolbarHeight + bottomInset}px`;
      this.bottomToolbar.style.paddingBottom = `${bottomInset}px`;
    });
    resizeObserver.observe(this.container);
  }

  /** @internal */
  private base64ToBlob(base64: string, type = "image/jpeg") {
    const byteString = atob(base64.split(",")[1]);
    const arrayBuffer = new Uint8Array(byteString.length);
    for (let i = 0; i < byteString.length; i++) {
      arrayBuffer[i] = byteString.charCodeAt(i);
    }
    return new Blob([arrayBuffer], { type });
  }

  /** @internal */
  private async loadImageReference(
    imageReference: string | Blob | ArrayBuffer
  ): Promise<void> {
    let imageSrc: string;

    if (typeof imageReference === "string") {
      if (
        imageReference.startsWith("http://") ||
        imageReference.startsWith("https://")
      ) {
        // Remote URL
        imageSrc = imageReference;
      } else if (imageReference.startsWith("data:image")) {
        // Base64 string → convert to Blob URL
        const blob = this.base64ToBlob(imageReference);
        imageSrc = URL.createObjectURL(blob);
      } else {
        // Assume it's a relative/local path → fetch it and convert to Blob URL
        try {
          const response = await fetch(imageReference);
          const blob = await response.blob();
          imageSrc = URL.createObjectURL(blob);
        } catch (error) {
          console.error("Failed to load local image:", error);
          imageSrc = "";
        }
      }
    } else if (imageReference instanceof Blob) {
      imageSrc = URL.createObjectURL(imageReference);
    } else if (imageReference instanceof ArrayBuffer) {
      const blob = new Blob([imageReference]);
      imageSrc = URL.createObjectURL(blob);
    } else {
      console.error("Unsupported image reference type");
      return;
    }

    this.loadImage(imageSrc);
  }

  /**
   * Get the cropper instance
   * @internal
   */
  public getCropper(): Cropper | null {
    return this.cropper;
  }

  /** @internal */
  public setCropper(cropper: Cropper | null): void {
    this.cropper = cropper;
  }

  /** @internal */
  public switchCropperSelection(): void {
    const cropperCanvas = this.cropper?.getCropperCanvas();
    const oldSelection = this.cropper?.getCropperSelection();

    if (oldSelection) {
      cropperCanvas?.removeChild(oldSelection);
    }

    // Create a new oval selection element
    const ovalSelection = document.createElement("cropper-selection", {
      is: "oval-cropper-selection",
    }) as HTMLElement;

    // Optional: Set movable/resizable
    ovalSelection.setAttribute("movable", "");
    ovalSelection.setAttribute("resizable", "");

    // Append to the canvas
    cropperCanvas?.appendChild(ovalSelection);
  }

  /** @internal */
  public initializeCropper(): void {
    // Try to locate image element inside the editor container
    const imageContainer =
      this.editorContainer.querySelector(".image-container");
    const img = imageContainer?.querySelector("img") as HTMLImageElement | null;

    if (!img) return;

    this.imageElement = img;

    // Prepare cropper container
    const cropperContainer = document.createElement("div");
    cropperContainer.className = "cropper-container";
    this.editorContainer.innerHTML = "";
    this.editorContainer.appendChild(cropperContainer);

    // Initialize the cropper
    this.cropper = new Cropper(this.imageElement, {
      container: cropperContainer,
      template: `
      <cropper-canvas background class"canBack">
      <cropper-image src='${this.imageElement}' initial-center-size="contain" alt="Picture" style="width: 100%;" rotatable scalable translatable></cropper-image>
      <cropper-shade hidden></cropper-shade>
      <cropper-handle action="select" plain></cropper-handle>
      <cropper-selection class="marching-ants" slottable movable resizable theme-color="#ee2727ff" aspect-ratio="16/9" style="border: 3px dashed #ffff00ff;">
        <!-- <svg class="marching-ants-border" viewBox="0 0 100 100" preserveAspectRatio="none">
          <rect x="0" y="0" width="100" height="100" vector-effect="non-scaling-stroke"/>
        </svg> -->
      <cropper-grid role="grid" bordered covered theme-color="#f6ff00ff"></cropper-grid>
        <cropper-crosshair centered theme-color="#ffff03ff"></cropper-crosshair>
        <cropper-handle action="move" plain theme-color="transparent"></cropper-handle>
        <cropper-handle action="n-resize" theme-color="#f6ff00ff"></cropper-handle>
        <cropper-handle action="e-resize" theme-color="#f6ff00ff"></cropper-handle>
        <cropper-handle action="s-resize" theme-color="#f6ff00ff"></cropper-handle>
        <cropper-handle action="w-resize" theme-color="#f6ff00ff"></cropper-handle>
        <cropper-handle action="ne-resize" theme-color="#f6ff00ff" style="background-color:yellow"></cropper-handle>
        <cropper-handle action="nw-resize" theme-color="#f6ff00ff" style="background-color:yellow"></cropper-handle>
        <cropper-handle action="se-resize" theme-color="#f6ff00ff" style="background-color:yellow"></cropper-handle>
        <cropper-handle action="sw-resize" theme-color="#f6ff00ff" style="background-color:yellow"></cropper-handle>
      </cropper-selection>
    </cropper-canvas>
    `,
    });

    // const cropperImage12 = new CropperImage();
    // cropperImage12.$ready((image) => {
    //   console.log(image.naturalWidth, image.naturalHeight);
    // });
    // cropperImage12.src = '/cropperjs/picture.jpg';

    const cropperImage = this.cropper.getCropperImage();
    const cropperSelection = this.cropper.getCropperSelection();


    if (cropperImage) {
      cropperImage.scalable = true;
      // const imageBox = cropperImage.getBoundingClientRect();
      // if (imageBox && cropperSelection) {
      //     cropperSelection.$addStyles(`:host { }`);
      //     cropperSelection.$change(imageBox.x, imageBox.y, imageBox.width, imageBox.height);
      //     cropperSelection.$render();
      // }

      // cropperImage.$addStyles(`:host { width: 100%; height: 100%; }`)
      cropperImage.$ready(() => {
      const readyImageBox = cropperImage.getBoundingClientRect();
      if (readyImageBox && cropperSelection) {
        // cropperSelection.style.border = '2px solid rgb(255, 251, 0)';
        // cropperSelection.$addStyles(`:host { 
        //   border: 2px dashed rgb(255, 255, 0);
        //   }`);
        setTimeout(() => {
          cropperSelection.$change(
          readyImageBox.x,
          readyImageBox.y,
          readyImageBox.width,
          readyImageBox.height,
          16 / 9,
          true
        );
        cropperSelection.$render();
        }, 2000);
        
      }
      });
    }

    // Setup selection change listener
    if (cropperSelection) {
      cropperSelection.zoomable = true;
      cropperSelection.addEventListener("change", () => {
        // this.showDoneButton(); // Uncomment if needed
      });
    }

    // Apply canvas styling
    const cropperCanvas = this.cropper.getCropperCanvas();
    if (cropperCanvas) {
      cropperCanvas.style.width = "100%";
      cropperCanvas.style.height = "100%";
      cropperCanvas.$addStyles(`:host { background: white !important; }`);
    }

    // Enforce cropper restrictions
    this.restrictCropperSelection();
  }

  /** @internal */
  private finalizeCrop(): void {
    if (!this.cropper) {
      this.emit("error", { message: "No cropper instance." });
      return;
    }
    const cropperSelection = this.cropper.getCropperSelection();
    if (!cropperSelection) {
      this.emit("error", { message: "No cropper selection." });
      return;
    }

    const editorContainer = this.getEditorContainer();
    const editorContainerRect = editorContainer.getBoundingClientRect();
    const canvasWidth = editorContainerRect.width;
    const canvasHeight = editorContainerRect.height;

    cropperSelection
      .$toCanvas({ width: canvasWidth, height: canvasHeight })
      .then((canvasElement: HTMLCanvasElement) => {
        // Convert canvas to image
        const croppedImage = canvasElement.toDataURL("image/png");

        // Clear the editor container
        this.editorContainer.innerHTML = "";

        // Create new image with cropped result
        const img = document.createElement("img");
        img.src = croppedImage;
        img.alt = "Cropped Image";
        img.style.maxWidth = "100%";
        img.style.maxHeight = "100%";
        img.style.objectFit = "contain";

        // Add image to container
        this.editorContainer.appendChild(img);

        // Reset cropper
        this.cropper = null;
      });
  }

  /** @internal */
  private restrictCropperSelection(): void {
    const cropperSelection = this.cropper?.getCropperSelection();
    const cropperImage = this.cropper?.getCropperImage();

    if (!cropperSelection || !cropperImage) {
      console.error("Cropper selection or image not found");
      return;
    }

    cropperSelection.addEventListener("change", (event: Event) => {
      const customEvent = event as CustomEvent<{
        x: number;
        y: number;
        width: number;
        height: number;
      }>;

      const { x, y, width, height } = customEvent.detail;

      // Get the bounding rectangle of the image
      const imageRect = cropperImage.getBoundingClientRect();
      const cropperContainer =
        this.editorContainer.querySelector(".cropper-container");
      const containerRect = cropperContainer?.getBoundingClientRect();

      if (!containerRect) return;

      // Calculate the selection's boundaries relative to the container
      const selectionLeft = x;
      const selectionTop = y;
      const selectionRight = x + width;
      const selectionBottom = y + height;

      // Calculate image boundaries relative to the container
      const imageLeft = imageRect.left - containerRect.left;
      const imageTop = imageRect.top - containerRect.top;
      const imageRight = imageLeft + imageRect.width;
      const imageBottom = imageTop + imageRect.height;

      // Check if the selection exceeds the image's boundaries
      const exceedsBounds =
        selectionLeft < imageLeft ||
        selectionTop < imageTop ||
        selectionRight > imageRight ||
        selectionBottom > imageBottom;

      if (exceedsBounds) {
        event.preventDefault();
      }
    });
  }

  /**
   * Get the image container element
   * @internal
   */
  public getImageContainer(): HTMLDivElement | null {
    return this.imageContainer;
  }

  /**
   * Get the image element
   * @internal
   */
  public getImageElement(): HTMLImageElement | null {
    return this.imageElement;
  }

  /**
   * Helper to fetch geolocation if needed
   */
  // private fetchLocationIfNeeded(
  //   locationRequired: boolean,
  //   callback: () => void
  // ) {
  //   if (!locationRequired) {
  //     this.lattitude = 0;
  //     this.longitude = 0;
  //     callback();
  //     return;
  //   }
  //   if (navigator.geolocation) {
  //     navigator.geolocation.getCurrentPosition(
  //       position => {
  //         this.lattitude = position.coords.latitude;
  //         this.longitude = position.coords.longitude;
  //         callback();
  //       },
  //       () => {
  //         this.lattitude = 0;
  //         this.longitude = 0;
  //         callback();
  //       }
  //     );
  //   } else {
  //     this.lattitude = 0;
  //     this.longitude = 0;
  //     callback();
  //   }
  // }

  /**
   * Camera/cropper entry point
   * @param cropperOptions cropper options (if cropper only)
   * @param resultOptions result options (if opening on existing image)
   */
  public static open(
    cropperOptions?: cropOptions,
    resultOptions?: resultOptions
  ): Editor {
    // Cropper only
    if (cropperOptions) {
      const editor = new Editor(cropperOptions, undefined);
      // If location is provided in cropperOptions, use it; else set to 0
      const lat: unknown = (cropperOptions as { latitude?: unknown }).latitude;
      const lng: unknown = (cropperOptions as { longitude?: unknown })
        .longitude;
      editor.lattitude =
        typeof lat === "number"
          ? lat
          : typeof lat === "string"
            ? parseFloat(lat) || 0
            : 0;
      editor.longitude =
        typeof lng === "number"
          ? lng
          : typeof lng === "string"
            ? parseFloat(lng) || 0
            : 0;
      editor.show();
      return editor;
    }
    // Open on existing image with resultOptions (e.g., recrop)
    if (resultOptions) {
      const editor = new Editor(undefined, undefined);
      const lat: unknown = resultOptions.latitude;
      const lng: unknown = resultOptions.longitude;
      editor.lattitude =
        typeof lat === "number"
          ? lat
          : typeof lat === "string"
            ? parseFloat(lat) || 0
            : 0;
      editor.longitude =
        typeof lng === "number"
          ? lng
          : typeof lng === "string"
            ? parseFloat(lng) || 0
            : 0;
      editor.show();
      return editor;
    }
    // Default fallback
    return new Editor();
  }
}
