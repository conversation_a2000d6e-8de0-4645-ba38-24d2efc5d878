/// <reference types="vitest" />
import path from "path";
import { defineConfig } from "vite";
import packageJson from "./package.json";
import tailwindcss from "@tailwindcss/vite";
import { copyPackageJson } from "./scripts/copy-package-json";
import cssInjectedByJsPlugin from "vite-plugin-css-injected-by-js";

import dtsBundleGenerator from 'unplugin-dts-bundle-generator/vite';
import { EntryPointConfig, LibrariesOptions } from "dts-bundle-generator";
import mkcert from 'vite-plugin-mkcert'

// import dts, { PluginOptions } from "vite-plugin-dts";
// const getPackageName = () => {
//   return packageJson.name;
// };

// const fileName = {
//   es: `${getPackageName()}.js`,
//   // iife: `${getPackageName()}.iife.js`,
//   // umd: `${getPackageName()}.umd.js`,
// };

const getFileSafeName = () => packageJson.name.replace(/^@.*\//, '');
const libFileName = {
  es: `${getFileSafeName()}.js`,
  // iife: `${getFileSafeName()}.iife.js`,
  umd: `${getFileSafeName()}.umd.js`,
  
};

const formats = Object.keys(libFileName) as Array<keyof typeof libFileName>;
// const dtsOptions: PluginOptions = {
//   include: ["src/index.ts"],
//   outDir: "build",
//   insertTypesEntry: true, // optionally generates `index.d.ts` with exports
//   rollupTypes: true,
// };

let resolvedConfig;
let entryConfig: LibrariesOptions = {
    importedLibraries: []
}
export default defineConfig({
  publicDir: false,
  base: "./",
  plugins: [
    tailwindcss(),
    {
      name: "copy-clean-package-json",
      configResolved(config) {
        resolvedConfig = config;
      },
      async closeBundle() {
        await copyPackageJson(resolvedConfig);
      },
    },
    cssInjectedByJsPlugin({
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      injectCodeFunction: function injectToShadowDOM(cssCode, options) {
        return function () {
          try {
            console.log(
              "vite-plugin-css-injected-by-js shadow DOM injection",
              "color:green;"
            );
            // if (typeof this !== "undefined" && this.shadowRoot) {
            //   const elementStyle = document.createElement("style");

            //   // Add attributes if specified
            //   for (const attribute in options.attributes) {
            //     elementStyle.setAttribute(
            //       attribute,
            //       options.attributes[attribute]
            //     );
            //   }

            //   elementStyle.textContent = cssCode;
            //   this.shadowRoot.appendChild(elementStyle);
            //   console.log(
            //     "vite-plugin-css-injected-by-js shadow DOM injection",
            //     "color:green;"
            //   );
            // }
          } catch (e) {
            console.error(
              "vite-plugin-css-injected-by-js shadow DOM injection error:",
              e
            );
          }
        };
      },
    }),
    dtsBundleGenerator({
        fileName: "./camcrop.d.ts",
        output: {
          noCheck: true,
          outFile: "./camcrop.d.ts",
        } as EntryPointConfig["output"],
        libraries: entryConfig as LibrariesOptions,
      })
  ],
  build: {
    minify: true,
    outDir: path.resolve(__dirname, 'build/@unvired/camcrop'),
    lib: {
      entry: path.resolve(__dirname, "src/index.ts"),
      name: "@unvired/camcrop",
      formats,
      fileName: format => libFileName[format],
    },
    sourcemap: false,
    rollupOptions: {
      // Externalize dependencies that should not be bundled
      external: [],
      output: {
        // Provide global variables to use in UMD build for externalized deps
        // globals: {
        //   cropperjs: "Cropper",
        //   fabric: "fabric",
        // },
      },
    },
  },
  server: {
    allowedHosts: ["e79b61a93d31.ngrok-free.app"],
  },
  test: {
    watch: false,
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
      "@@": path.resolve(__dirname),
    },
  },
});
