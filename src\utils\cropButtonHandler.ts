import { Editor } from "../editor/editor";
import delIcon from "../../assets/delete.svg";
import { Canvas, Control, Textbox, FabricImage, FabricObject } from "fabric";
import { UndoRedoManager } from "./undoRedoManager";
import * as fabric from "fabric";
import { EraserBrush } from "@erase2d/fabric";
// import { BottomToolbarView } from '../editor/bottomToolBarView';

/** @internal */
// Extend Fabric.js object type to include the `erasable` property
declare module "fabric" {
  interface Object {
    erasable?: boolean;
  }
}

/** @internal */
export class CropButtonHandler {
  private static instance: CropButtonHandler | null = null;
  private editor: Editor | null = null;
  // private toolbar: HTMLElement | null = null;
  private fabricCanvas: Canvas | null = null;
  private undoRedoManager: UndoRedoManager = UndoRedoManager.getInstance();

  private constructor() {
    // Private constructor to prevent direct instantiation
  }

  public static getInstance(): CropButtonHandler {
    if (!this.instance) {
      this.instance = new CropButtonHandler();
    }
    return this.instance;
  }

  public initialize(editor: Editor): void {
    this.editor = editor;
  }

  /**  @internal */
  public getFabricCanvas(): Canvas | null {
    return this.fabricCanvas;
  }

  /** @internal */
  public setFabricCanvas(canvas: Canvas): void {
    this.fabricCanvas = canvas;
  }

  // public saveCanvasState(): void {
  //     if (this.fabricCanvas) {
  //         const before = this.fabricCanvas.toJSON();
  //         // Perform the change (e.g., add image, text, etc.)
  //         // After the change:
  //         const after = this.fabricCanvas.toJSON();
  //         this.undoRedoManager.add({
  //             undo: () => {
  //                 console.log("fabric JSON", this.fabricCanvas!.toJSON());
  //                 // this.fabricCanvas!.loadFromJSON(JSON.stringify(before), () => {
  //                 //     this.fabricCanvas!.renderAll();
  //                 // });
  //             },
  //             redo: () => {
  //                 console.log("fabric JSON", this.fabricCanvas!.toJSON());
  //                 // this.fabricCanvas!.loadFromJSON(JSON.stringify(after), () => {
  //                 //     this.fabricCanvas!.renderAll();
  //                 // });
  //             }
  //         });
  //     }
  // }

  public undo(): void {
    this.undoRedoManager.undo();
  }

  public redo(): void {
    this.undoRedoManager.redo();
  }

  public updateUndoRedoButtons(
    undoButton: HTMLElement,
    redoButton: HTMLElement
  ): void {
    const hasUndo = this.undoRedoManager.hasUndo();
    const hasRedo = this.undoRedoManager.hasRedo();

    (undoButton as HTMLButtonElement).disabled = !hasUndo;
    (redoButton as HTMLButtonElement).disabled = !hasRedo;
  }

  public attachUndoRedoHandlers(
    undoButton: HTMLElement,
    redoButton: HTMLElement
  ): void {
    undoButton.addEventListener("click", () => {
      this.undo();
      this.updateUndoRedoButtons(undoButton, redoButton);
    });
    redoButton.addEventListener("click", () => {
      this.redo();
      this.updateUndoRedoButtons(undoButton, redoButton);
    });

    // Initial state update
    this.updateUndoRedoButtons(undoButton, redoButton);
  }

  public getUndoRedoManager(): UndoRedoManager {
    return this.undoRedoManager;
  }

  public handleAddTextButtonClick(): void {
    const deleteIcon = new Image();
    deleteIcon.src = delIcon;
    if (this.fabricCanvas?.isDrawingMode) {
      this.fabricCanvas.isDrawingMode = false;
    }
    const text: Textbox = new Textbox("Enter Text", {
      left: 100,
      top: 100,
      fontSize: 24,
      objectCaching: false,
      fill: "black",
      editable: true,
      borderColor: "rgb(252, 207, 3)",
      cornerColor: "rgb(252, 207, 3)",
      cornerStrokeColor: "rgb(252, 207, 3)",
      transparentCorners: false,
      hasControls: true,
    });
    // const fillColor = 'orange';
    // const renderFilledControl = (ctx: CanvasRenderingContext2D, left: number, top: number) => {
    //     const size = 10;
    //     ctx.save();
    //     ctx.fillStyle = fillColor;
    //     ctx.beginPath();
    //     ctx.arc(left, top, size / 2, 0, 2 * Math.PI);
    //     ctx.fill();
    //     ctx.restore();
    // };
    ["tl", "tr", "bl", "br", "mt", "mb", "ml", "mr", "mtr"].forEach(
      position => {
        console.log(position);
        // text.controls[position].render = renderFilledControl;
      }
    );
    let target: FabricObject | any;
    const deleteControl = new Control({
      x: 0.5,
      y: -0.5,
      offsetY: -16,
      offsetX: 16,
      cursorStyle: "pointer",
      mouseUpHandler: (_, transform) => {
        target = transform.target;
        this.undoRedoManager.add({
          undo: () => {
            this.fabricCanvas!.add(text);
            this.fabricCanvas!.requestRenderAll();
          },
          redo: () => {
            this.fabricCanvas!.remove(target);
            this.fabricCanvas!.requestRenderAll();
          },
        });
        this.fabricCanvas!.remove(target);
        this.fabricCanvas!.requestRenderAll();
      },
      render: (ctx, left, top) => {
        if (!deleteIcon.complete) {
          deleteIcon.onload = () => this.fabricCanvas!.requestRenderAll();
          return;
        }
        ctx.drawImage(deleteIcon, left - 15, top - 12, 15, 15);
      },
    });
    const colorPickerIcon = new Image();
    colorPickerIcon.src =
      'data:image/svg+xml;utf8,<svg width="15" height="15" xmlns="http://www.w3.org/2000/svg"><circle cx="7.5" cy="7.5" r="7" fill="orange" stroke="black" stroke-width="1"/></svg>';
    const colorControl = new Control({
      x: 0.5,
      y: -0.5,
      offsetX: 30,
      offsetY: -10,
      cursorStyle: "pointer",
      mouseUpHandler: (_, transform) => {
        console.log(transform);
        const colorInput = document.createElement("input");
        colorInput.type = "color";
        colorInput.value = (text.fill as string) || "#000000";
        colorInput.style.position = "fixed";
        colorInput.style.left = "-9999px";
        document.body.appendChild(colorInput);
        colorInput.click();
        colorInput.oninput = () => {
          text.set("fill", colorInput.value);
          this.fabricCanvas!.requestRenderAll();
          document.body.removeChild(colorInput);
        };
        colorInput.onblur = () => {
          document.body.removeChild(colorInput);
        };
      },
      render: (ctx, left, top) => {
        if (!colorPickerIcon.complete) {
          colorPickerIcon.onload = () => this.fabricCanvas!.requestRenderAll();
          return;
        }
        ctx.drawImage(colorPickerIcon, left - 15, top + 8, 15, 15);
      },
    });
    this.undoRedoManager.add({
      undo: () => {
        this.fabricCanvas!.remove(text);
        this.fabricCanvas!.requestRenderAll();
      },
      redo: () => {
        this.fabricCanvas!.add(text);
        this.fabricCanvas!.setActiveObject(text);
        this.fabricCanvas!.requestRenderAll();
      },
    });
    text.controls.delete = deleteControl;
    text.controls.color = colorControl;
    this.fabricCanvas!.add(text);
    this.fabricCanvas!.setActiveObject(text);
    this.fabricCanvas!.requestRenderAll();
  }

  public handleCropButtonClick(
    canvasElement: HTMLCanvasElement,
    selectedShape?: string
  ): void {
    console.log(selectedShape);
    const editorContainer = this.editor!.getEditorContainer();
    editorContainer.innerHTML = "";
    // Remove DOM queries for addTextBtn and annotation buttons
    // All button logic is now handled via BottomToolbarView and controller
    const newCanvas = document.createElement("canvas") as HTMLCanvasElement;
    editorContainer.appendChild(newCanvas);
    // Initialize Fabric.js canvas
    this.fabricCanvas = new Canvas(newCanvas, {
      height: canvasElement.height,
      width: canvasElement.width,
    });
    const image = new Image();
    image.src = canvasElement.toDataURL("image/png");
    image.onload = () => {
      const loadImage = async () => {
        try {
          const fabricImage = new FabricImage(image);
          fabricImage.set({
            left: 0,
            top: 0,
            scaleX: this.fabricCanvas!.width / image.width,
            scaleY: this.fabricCanvas!.height / image.height,
            selectable: false,
          });
          this.fabricCanvas!.add(fabricImage);
          this.fabricCanvas!.renderAll();
        } catch (error) {
          console.error("Error loading cropped image:", error);
        }
      };
      loadImage();
    };
    // Drawing/annotation logic is now handled via BottomToolbarView event handlers
    this.fabricCanvas!.on("selection:created", e => {
      const selection = (e as any).target;

      const objects =
        selection?.type === "activeSelection"
          ? selection._objects
          : [selection];

      const isFullyErased = (obj: fabric.Object | any): boolean => {
        if (!obj.clipPath || !obj.annotation) return false;

        const objBounds = obj.getBoundingRect(true, true);
        const clipBounds = obj.clipPath.getBoundingRect(true, true);

        const covered =
          clipBounds.left <= objBounds.left &&
          clipBounds.top <= objBounds.top &&
          clipBounds.left + clipBounds.width >=
            objBounds.left + objBounds.width &&
          clipBounds.top + clipBounds.height >=
            objBounds.top + objBounds.height;

        return covered;
      };

      const visibleObjects = objects.filter((obj: fabric.Object | any) => {
        if (obj.annotation) {
          return !isFullyErased(obj);
        }
        // Keep non-annotation objects always selectable
        return true;
      });

      if (visibleObjects.length === 0) {
        this.fabricCanvas!.discardActiveObject();
        this.fabricCanvas!.requestRenderAll();
        console.warn(
          "Selection discarded: All selected annotations are fully erased."
        );
        return;
      }

      // Re-assign filtered selection if needed
      // if (visibleObjects.length < objects.length) {
      //   const group = new fabric.ActiveSelection(visibleObjects, {
      //     canvas: this.fabricCanvas
      //   });
      //   this.fabricCanvas!.setActiveObject(group);
      //   this.fabricCanvas!.requestRenderAll();
      // }
    });

    this.fabricCanvas!.on("path:created", e => {
      const path: FabricObject | any = e.path;
      console.log("New annotation path added:", path);
      e.path.set({
        erasable: true,
        annotation: true,
        selectable: true,
      });
      this.undoRedoManager.add({
        undo: () => {
          this.fabricCanvas!.remove(path);
          this.fabricCanvas!.requestRenderAll();
        },
        redo: () => {
          this.fabricCanvas!.add(path);
          this.fabricCanvas!.requestRenderAll();
        },
      });
    });
  }

  public enableEraserMode(): void {
    if (this.fabricCanvas) {
      const circle = new fabric.Rect({ radius: 50, erasable: true });
      this.fabricCanvas.add(circle);

      const eraser = new EraserBrush(this.fabricCanvas);
      eraser.width = 30;
      this.fabricCanvas.freeDrawingBrush = eraser;
      this.fabricCanvas.isDrawingMode = true;

      eraser.on("start", e => {
        console.log(e);
        // inform something
      });

      eraser.on("end", async (e: CustomEvent) => {
        console.log(e);
        // const pathPerObjectMap = await eraser.commit(e.detail);
        // for (const [obj, path] of pathPerObjectMap) {
        //     // Optionally remove the object if it's fully erased
        //     if (obj.clipPath && obj.clipPath?.isEmpty()) {
        //         this.fabricCanvas?.remove(obj);
        //     }
        // }

        this.fabricCanvas?.discardActiveObject();
        this.fabricCanvas?.requestRenderAll();
      });
    } else {
      console.error("Fabric canvas is not initialized");
    }
  }
}
