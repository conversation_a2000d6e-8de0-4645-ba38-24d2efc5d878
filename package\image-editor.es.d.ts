import { Canvas } from 'fabric';
import { default as default_2 } from 'cropperjs';
import { PreinitializedWritableAtom } from 'nanostores';

export declare class BottomToolbar {
    private controller;
    constructor(editor: Editor);
    getToolbar(): HTMLDivElement;
}

export declare class BottomToolbarController {
    private static instance;
    private view;
    private editor;
    private zoomLevel;
    private scaleDisplayed;
    editorContainer: any;
    private constructor();
    static getInstance(editor: Editor): BottomToolbarController;
    private wireEvents;
    getToolbar(): HTMLDivElement;
}

export declare class BottomToolbarView {
    private static instance;
    toolbar: HTMLDivElement;
    eraserButton: HTMLButtonElement;
    cropButton: HTMLButtonElement;
    annotateButton: HTMLButtonElement;
    imageScaleButton: HTMLButtonElement;
    rotateLeftButton: HTMLButtonElement;
    rotateRightButton: HTMLButtonElement;
    undoButton: HTMLButtonElement;
    redoButton: HTMLButtonElement;
    zoomOutButton: HTMLButtonElement;
    zoomInButton: HTMLButtonElement;
    zoomLabel: HTMLSpanElement;
    addTextButton: HTMLButtonElement;
    reportbutton: HTMLDivElement;
    resetButton: HTMLButtonElement;
    addAnnotationButton: HTMLButtonElement;
    onShapeChange?: (shape: string) => void;
    undoRedoContainer: HTMLDivElement;
    zoomContainer: HTMLDivElement;
    rotateContainer: HTMLDivElement;
    shapeDropdownContainer: HTMLElement;
    constructor(icons: {
        eraser: string;
        crop: any;
        annotate: string;
        imageScale: string;
        rotateLeft: string;
        rotateRight: string;
        undo: string;
        redo: string;
        zoomOut: string;
        zoomIn: string;
        addText: string;
        resetIcon: string;
        addAnnotation: string;
    });
    private createButton;
    getToolbar(): HTMLDivElement;
    private showToolbarState;
    static getInstance(options: any): BottomToolbarView;
}

export declare function clearImages(): Promise<void>;

export declare function createAddImageUI(onImageUpload: (file: File) => void): HTMLDivElement;

export declare function createToolBars(): {
    topBar: HTMLDivElement;
    bottomBar: HTMLDivElement;
};

export declare class CropButtonHandler {
    private static instance;
    private editor;
    private fabricCanvas;
    private undoRedoManager;
    private constructor();
    static getInstance(): CropButtonHandler;
    initialize(editor: Editor): void;
    getFabricCanvas(): Canvas | null;
    setFabricCanvas(canvas: Canvas): void;
    undo(): void;
    redo(): void;
    updateUndoRedoButtons(undoButton: HTMLElement, redoButton: HTMLElement): void;
    attachUndoRedoHandlers(undoButton: HTMLElement, redoButton: HTMLElement): void;
    getUndoRedoManager(): UndoRedoManager;
    handleAddTextButtonClick(): void;
    handleCropButtonClick(canvasElement: HTMLCanvasElement, selectedShape?: string): void;
    enableEraserMode(): void;
}

export declare interface CropOptions {
    imageUrl?: string | Blob | ArrayBuffer;
    fileName?: string;
    mode?: string;
    maxRating?: number | string;
    currentRating?: number | string;
    productName?: string;
    isDemo?: string | boolean;
    report?: boolean;
}

export declare class Editor {
    private listeners;
    private static instance;
    private options?;
    private _isDestroyed;
    /**
     * Subscribe to an event: 'success', 'error', 'cancelled'.
     * Usage: editor.on('success', fn)
     */
    on(event: 'success' | 'error' | 'cancelled', callback: Function): void;
    /**
     * Unsubscribe from an event.
     */
    off(event: 'success' | 'error' | 'cancelled', callback: Function): void;
    /**
     * Internal: emit an event to all listeners and cleanup on success, error, or cancelled.
     * Only emits the requested event, and only does cleanup once.
     */
    private emit;
    /**
     * Destroy the editor instance, remove from DOM, and cleanup. Only for external/manual use.
     */
    destroy(): void;
    private container;
    private topToolbar;
    private editorContainer;
    private bottomToolbar;
    private overlay;
    private cropper;
    private shadowRoot;
    private imageContainer;
    private imageElement;
    private constructor();
    static getInstance(options?: CropOptions): Editor;
    /**
     * Public API: initialize and show the editor
     */
    static init(options?: CropOptions): Editor;
    /**
     * Show the editor (attach to DOM, etc)
     */
    show(): void;
    getCropContainer(): HTMLDivElement;
    getEditorContainer(): HTMLDivElement;
    getShadowRoot(): ShadowRoot;
    private setupStyles;
    private adjustEditorContainerHeight;
    private showUploadUI;
    private loadImage;
    private base64ToBlob;
    private loadImageReference;
    getCropper(): default_2 | null;
    setCropper(cropper: default_2 | null): void;
    switchCropperSelection(): void;
    initializeCropper(): void;
    private finalizeCrop;
    private restrictCropperSelection;
    getImageContainer(): HTMLDivElement | null;
    getImageElement(): HTMLImageElement | null;
}

export declare type EditorMode = 'home' | 'crop' | 'annotate';

export declare const editorMode: PreinitializedWritableAtom<EditorMode> & object;

export declare enum editorModeType {
    home = "home",
    crop = "crop",
    annotate = "annotate"
}

export declare function getEditorMode(): void;

/**
 * Fetches all image file paths from a given folder and its subfolders.
 * @param folderPath - The path to the folder containing images.
 * @returns A promise that resolves to an array of image file paths.
 */
export declare function getImagesFromFolder(folderPath: string): Promise<string[]>;

export declare const image: PreinitializedWritableAtom<imageType> & object;

export declare class ImagesScale {
    private category;
    private type;
    private static instance;
    private container;
    constructor(category: string, type: string, editor?: Editor);
    private generateImageViewerHTML;
    private addEventListeners;
    private ensureImageScaleInView;
    show(): void;
    hide(): void;
    onClose(callback: () => void): void;
    static getInstance(name: string, type: string, editor: any): ImagesScale;
}

export declare type imageType = string | Blob | ArrayBuffer;

export declare function loadImageByKey(key: string): Promise<imageType | undefined>;

export declare class ModalUI {
    private brushRange;
    private brushPath;
    private opacityRange;
    private modalContainer;
    private modal;
    private openModalBtn;
    private closeModalBtn;
    private fabricCanvas;
    private constructor();
    static getInstance(): ModalUI;
    show(button: HTMLElement): void;
    hide(): void;
    private createUI;
    private addEventListeners;
    private updateModalPosition;
    private updateBrushSize;
    private updateOpacity;
    private initializeColorPicker;
}

export declare const modifiedImageKey: PreinitializedWritableAtom<string | null> & object;

export declare const originalImageKey: PreinitializedWritableAtom<string | null> & object;

export declare interface resultOptions {
    image: string | Blob | ArrayBuffer;
    report?: boolean;
    rating?: number | string;
}

export declare function setAnnotateMode(): void;

export declare function setCropMode(): void;

export declare function setHomeMode(): void;

export declare function setModifiedImage(image: imageType): Promise<void>;

export declare function setOriginalImage(image: imageType): Promise<void>;

export declare const shapeStore: PreinitializedWritableAtom<shapeType> & object;

export declare type shapeType = 'Rectangle' | 'Oval';

export declare function showCroppedImageModal(croppedImage: string, actions: {
    onEdit: () => void;
    onContinue: () => void;
    onCancel: () => void;
}, shadowRoot: ShadowRoot): void;

export declare function switchToOvalSelection(cropper: any): void;

export declare class TopToolbar {
    static instance: TopToolbar | null;
    readonly element: HTMLDivElement;
    private backButton;
    private saveButton;
    private cancelButton;
    private doneButton;
    private toolContainer;
    private constructor();
    static getInstance(): TopToolbar;
    private bindEvents;
    private subscribeToStore;
    addTool(toolElement: HTMLElement): void;
}

export declare class UndoRedoManager {
    private static instance;
    private commands;
    private index;
    private limit;
    private isExecuting;
    private callback?;
    private constructor();
    static getInstance(): UndoRedoManager;
    private removeFromTo;
    private execute;
    add(command: {
        undo: () => void;
        redo: () => void;
        groupId?: string;
    }): this;
    setCallback(callbackFunc: () => void): void;
    undo(): this;
    redo(): this;
    clear(): void;
    hasUndo(): boolean;
    hasRedo(): boolean;
    getCommands(groupId?: string): {
        undo: () => void;
        redo: () => void;
        groupId?: string;
    }[];
    getIndex(): number;
    setLimit(max: number): void;
}

export { }


declare module 'fabric' {
    interface Object {
        erasable?: boolean;
    }
}
