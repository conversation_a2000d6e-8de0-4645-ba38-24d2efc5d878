<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Test</title>
    <script src='./build/vite-vanilla-ts-lib-starter.js'></script>
  </head>
  <body>
    <script>
      function openEditor() {
        // let options = { data: "hello world" }
        // // const editor = new Editor(); // 'MyEditor' comes from `name` in vite.config.ts
        // Editor.init(options);
        // // const editor = init(options);
        const options= { imageUrl: "sample1.jpg" };
        const editor = Editor.init(options);
        // editor.show();
        editor.on("success", (result) => {
          const res = result;
          console.log("Success:", res);
          // document.getElementById('resultImg')?.src = res.image;
        });
        editor.on("error", (err) => {
          console.error("Error:", err);
        });
        editor.on("cancelled", () => {
          console.log("Editor cancelled");
        });
      };
  </script>
  <h1 style="color:black">Test</h1>
  <button style="color:black" onclick="openEditor()">Open</button>
  </body>
</html>