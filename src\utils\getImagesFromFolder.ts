import fs from "fs";
import path from "path";

/**
 * Fetches all image file paths from a given folder and its subfolders.
 * @param folderPath - The path to the folder containing images.
 * @returns A promise that resolves to an array of image file paths.
 */
export async function getImagesFromFolder(
  folderPath: string
): Promise<string[]> {
  return new Promise((resolve, reject) => {
    fs.readdir(folderPath, { withFileTypes: true }, (err, files) => {
      if (err) {
        reject(err);
        return;
      }

      const imagePaths: string[] = [];

      files.forEach(file => {
        const fullPath = path.join(folderPath, file.name);
        if (file.isDirectory()) {
          getImagesFromFolder(fullPath)
            .then(subDirImages => {
              imagePaths.push(...subDirImages);
              if (imagePaths.length === files.length) {
                resolve(imagePaths);
              }
            })
            .catch(reject);
        } else if (file.isFile() && isImageFile(file.name)) {
          imagePaths.push(fullPath);
        }
      });

      resolve(imagePaths);
    });
  });
}

/**
 * Checks if a file is an image based on its extension.
 * @param fileName - The name of the file.
 * @returns True if the file is an image, false otherwise.
 */
function isImageFile(fileName: string): boolean {
  const imageExtensions = [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".svg"];
  return imageExtensions.includes(path.extname(fileName).toLowerCase());
}
