import { describe, it, expect, beforeEach } from 'vitest';
import { SafeAreaUtils } from '../SafeAreaUtils';
import type { DeviceInfoResult } from '../DeviceInfo';

describe('SafeAreaUtils', () => {
  let mockDevice: DeviceInfoResult;

  beforeEach(() => {
    // Reset mock device to default values
    mockDevice = {
      userAgent: '',
      isIPhone: false,
      isIPad: false,
      isIOS: false,
      isAndroid: false,
      isSafari: false,
      isChrome: false,
      isIOSWebView: false,
      isAndroidWebView: false,
      isWebView: false,
      hasHomeIndicator: false,
      safeAreaInsetTop: 0,
      safeAreaInsetBottom: 0,
    };

    // Mock window.screen for device height checks
    Object.defineProperty(global, 'window', {
      value: {
        screen: {
          height: 800,
        },
      },
      writable: true,
    });
  });

  describe('calculateTopInset', () => {
    it('should return 0 for non-iOS webview', () => {
      mockDevice.isIOSWebView = false;
      mockDevice.isAndroid = true;
      
      const topInset = SafeAreaUtils.calculateTopInset(mockDevice);
      
      expect(topInset).toBe(0);
    });

    it('should use safeAreaInsetTop when available in iOS webview', () => {
      mockDevice.isIOSWebView = true;
      mockDevice.isIOS = true;
      mockDevice.safeAreaInsetTop = 44;
      
      const topInset = SafeAreaUtils.calculateTopInset(mockDevice);
      
      expect(topInset).toBe(44);
    });

    it('should return 44px for iPhone X+ in iOS webview when CSS env not available', () => {
      mockDevice.isIOSWebView = true;
      mockDevice.isIOS = true;
      mockDevice.isIPhone = true;
      mockDevice.safeAreaInsetTop = 0;
      
      // Mock iPhone X+ screen height
      Object.defineProperty(global.window, 'screen', {
        value: { height: 812 },
        writable: true,
      });
      
      const topInset = SafeAreaUtils.calculateTopInset(mockDevice);
      
      expect(topInset).toBe(44);
    });

    it('should return 20px for older iPhone in iOS webview when CSS env not available', () => {
      mockDevice.isIOSWebView = true;
      mockDevice.isIOS = true;
      mockDevice.isIPhone = true;
      mockDevice.safeAreaInsetTop = 0;
      
      // Mock older iPhone screen height
      Object.defineProperty(global.window, 'screen', {
        value: { height: 667 },
        writable: true,
      });
      
      const topInset = SafeAreaUtils.calculateTopInset(mockDevice);
      
      expect(topInset).toBe(20);
    });

    it('should return 24px for iPad in iOS webview when CSS env not available', () => {
      mockDevice.isIOSWebView = true;
      mockDevice.isIOS = true;
      mockDevice.isIPad = true;
      mockDevice.safeAreaInsetTop = 0;
      
      const topInset = SafeAreaUtils.calculateTopInset(mockDevice);
      
      expect(topInset).toBe(24);
    });

    it('should return 20px as default fallback for iOS webview', () => {
      mockDevice.isIOSWebView = true;
      mockDevice.isIOS = true;
      mockDevice.safeAreaInsetTop = 0;
      
      const topInset = SafeAreaUtils.calculateTopInset(mockDevice);
      
      expect(topInset).toBe(20);
    });
  });

  describe('calculateBottomInset', () => {
    it('should return 0 for non-iOS webview', () => {
      mockDevice.isIOSWebView = false;
      mockDevice.isAndroid = true;
      
      const bottomInset = SafeAreaUtils.calculateBottomInset(mockDevice);
      
      expect(bottomInset).toBe(0);
    });

    it('should return 0 for iOS webview without home indicator', () => {
      mockDevice.isIOSWebView = true;
      mockDevice.isIOS = true;
      mockDevice.hasHomeIndicator = false;
      
      const bottomInset = SafeAreaUtils.calculateBottomInset(mockDevice);
      
      expect(bottomInset).toBe(0);
    });

    it('should use safeAreaInsetBottom when available for iOS webview with home indicator', () => {
      mockDevice.isIOSWebView = true;
      mockDevice.isIOS = true;
      mockDevice.hasHomeIndicator = true;
      mockDevice.safeAreaInsetBottom = 34;
      
      const bottomInset = SafeAreaUtils.calculateBottomInset(mockDevice);
      
      expect(bottomInset).toBe(34);
    });

    it('should return 34px fallback for iOS webview with home indicator when CSS env not available', () => {
      mockDevice.isIOSWebView = true;
      mockDevice.isIOS = true;
      mockDevice.hasHomeIndicator = true;
      mockDevice.safeAreaInsetBottom = 0;
      
      const bottomInset = SafeAreaUtils.calculateBottomInset(mockDevice);
      
      expect(bottomInset).toBe(34);
    });
  });

  describe('getSafeAreaInsets', () => {
    it('should return both top and bottom insets', () => {
      mockDevice.isIOSWebView = true;
      mockDevice.isIOS = true;
      mockDevice.isIPhone = true;
      mockDevice.hasHomeIndicator = true;
      mockDevice.safeAreaInsetTop = 44;
      mockDevice.safeAreaInsetBottom = 34;
      
      const insets = SafeAreaUtils.getSafeAreaInsets(mockDevice);
      
      expect(insets.top).toBe(44);
      expect(insets.bottom).toBe(34);
    });

    it('should return zero insets for non-iOS webview', () => {
      mockDevice.isIOSWebView = false;
      mockDevice.isAndroid = true;
      
      const insets = SafeAreaUtils.getSafeAreaInsets(mockDevice);
      
      expect(insets.top).toBe(0);
      expect(insets.bottom).toBe(0);
    });
  });

  describe('shouldApplySafeAreaAdjustments', () => {
    it('should return true for iOS webview', () => {
      mockDevice.isIOSWebView = true;
      
      const shouldApply = SafeAreaUtils.shouldApplySafeAreaAdjustments(mockDevice);
      
      expect(shouldApply).toBe(true);
    });

    it('should return false for non-iOS webview', () => {
      mockDevice.isIOSWebView = false;
      mockDevice.isAndroid = true;
      
      const shouldApply = SafeAreaUtils.shouldApplySafeAreaAdjustments(mockDevice);
      
      expect(shouldApply).toBe(false);
    });

    it('should return false for iOS Safari (not webview)', () => {
      mockDevice.isIOSWebView = false;
      mockDevice.isIOS = true;
      mockDevice.isSafari = true;
      
      const shouldApply = SafeAreaUtils.shouldApplySafeAreaAdjustments(mockDevice);
      
      expect(shouldApply).toBe(false);
    });
  });

  describe('Integration scenarios', () => {
    it('should handle iPhone X+ in Cordova app correctly', () => {
      mockDevice.isIOSWebView = true;
      mockDevice.isIOS = true;
      mockDevice.isIPhone = true;
      mockDevice.hasHomeIndicator = true;
      mockDevice.safeAreaInsetTop = 44;
      mockDevice.safeAreaInsetBottom = 34;
      
      const insets = SafeAreaUtils.getSafeAreaInsets(mockDevice);
      const shouldApply = SafeAreaUtils.shouldApplySafeAreaAdjustments(mockDevice);
      
      expect(shouldApply).toBe(true);
      expect(insets.top).toBe(44);
      expect(insets.bottom).toBe(34);
    });

    it('should handle older iPhone in Cordova app correctly', () => {
      mockDevice.isIOSWebView = true;
      mockDevice.isIOS = true;
      mockDevice.isIPhone = true;
      mockDevice.hasHomeIndicator = false;
      mockDevice.safeAreaInsetTop = 0;
      mockDevice.safeAreaInsetBottom = 0;
      
      // Mock older iPhone screen height
      Object.defineProperty(global.window, 'screen', {
        value: { height: 667 },
        writable: true,
      });
      
      const insets = SafeAreaUtils.getSafeAreaInsets(mockDevice);
      const shouldApply = SafeAreaUtils.shouldApplySafeAreaAdjustments(mockDevice);
      
      expect(shouldApply).toBe(true);
      expect(insets.top).toBe(20);
      expect(insets.bottom).toBe(0);
    });

    it('should handle Android webview correctly', () => {
      mockDevice.isIOSWebView = false;
      mockDevice.isAndroidWebView = true;
      mockDevice.isAndroid = true;
      
      const insets = SafeAreaUtils.getSafeAreaInsets(mockDevice);
      const shouldApply = SafeAreaUtils.shouldApplySafeAreaAdjustments(mockDevice);
      
      expect(shouldApply).toBe(false);
      expect(insets.top).toBe(0);
      expect(insets.bottom).toBe(0);
    });
  });
});
