import Cropper from 'cropperjs';

/** @public */
export declare interface CropOptions {
    	imageUrl?: string | Blob | ArrayBuffer;
    	fileName?: string;
    	mode?: string;
    	maxRating?: number | string;
    	currentRating?: number | string;
    	productName?: string;
    	isDemo?: string | boolean;
    	report?: boolean;
}

/**
 * Represents a useful class.
 * @public
 */
declare class Editor {
    	
    	
    	
    	
    	/**
     	 * Subscribe to an event: 'success', 'error', 'cancelled'.
     	 * Usage: editor.on('success', fn)
     	 * @public
     	 */
    	on(event: "success" | "error" | "cancelled", callback: (data?: unknown) => void): void;
    	
    	
    	private container;
    	private topToolbar;
    	private editorContainer;
    	private bottomToolbar;
    	private overlay;
    	private cropper;
    	private shadowRoot;
    	private imageContainer;
    	private imageElement;
    	
    	
    	/**
     	 * Public API: initialize and show the editor
     	 * @public
     	 */
    	static init(options?: CropOptions): Editor;
    	
    	
    	
    	
    	
    	
    	
    	
    	
    	
    	
    	
    	
    	
    	
    	
    	
    	
}
export default Editor;

/** @public */
export declare interface resultOptions {
    	image: string | Blob | ArrayBuffer;
    	report?: boolean;
    	rating?: number | string;
    	texts: string[];
    	latitude: string | number;
    	longitude: string | number;
}

export { }
