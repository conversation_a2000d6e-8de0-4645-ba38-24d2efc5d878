// import { <PERSON>rop<PERSON>uttonHandler } from "../utils/cropButtonHandler";
import {
  EditorMode,
  editorMode,
  setCropMode
} from "./editorModeStore";
import { setHomeMode } from "./editorModeStore"; // View logic for BottomToolbar
// import { Editor } from "./editor";
// import { shapeStore } from "./editorModeStore";

/**
 * @internal
 */
export class BottomToolbarView {
  private static instance: BottomToolbarView;
  public toolbar: HTMLDivElement;
  public eraserButton: HTMLButtonElement;
  public cropButton: HTMLButtonElement;
  public annotateButton: HTMLButtonElement;
  public imageScaleButton: HTMLButtonElement;
  public rotateLeftButton: HTMLButtonElement;
  public rotateRightButton: HTMLButtonElement;
  public undoButton: HTMLButtonElement;
  public redoButton: HTMLButtonElement;
  public zoomOutButton: HTMLButtonElement;
  public zoomInButton: HTMLButtonElement;
  public zoomLabel: HTMLSpanElement;
  public addTextButton: HTMLButtonElement;
  public reportbutton: HTMLDivElement;
  public resetButton: HTMLButtonElement;
  public addAnnotationButton: HTMLButtonElement;
  public onShapeChange?: (shape: string) => void;
  undoRedoContainer: HTMLDivElement;
  zoomContainer: HTMLDivElement;
  rotateContainer: HTMLDivElement;
  // shapeDropdownContainer: HTMLElement;

  constructor(icons: {
    eraser: string;
    crop: string;
    annotate: string;
    imageScale: string;
    rotateLeft: string;
    rotateRight: string;
    undo: string;
    redo: string;
    zoomOut: string;
    zoomIn: string;
    addText: string;
    resetIcon: string;
    addAnnotation: string;
  }) {
    this.toolbar = document.createElement("div");
    this.toolbar.className = "bottom-toolbar-container";
    // Crop

    // const sampleBTN = document.createElement("button");
    // sampleBTN.className = "btn btn-accent btn-outline rounded-full";
    // sampleBTN.innerHTML = "sample";
    // this.toolbar.appendChild(sampleBTN);

    this.cropButton = this.createButton(icons.crop, "Crop", "crop-button");
    this.toolbar.appendChild(this.cropButton);
    // Create shape dropdown
    // const dropdownContainer = document.createElement("div");
    // dropdownContainer.className = "relative";
    // this.shapeDropdownContainer = dropdownContainer;
    // const dropdownButton = document.createElement("button");
    // dropdownButton.className =
    //   "w-40 bg-slate-950 text-white px-4 py-2 rounded-[15px] shadow hover:bg-slate-800 flex items-center justify-between";
    // const selectedText = document.createElement("span");
    // selectedText.textContent = "Rectangle";
    // selectedText.className = "selected-text";
    // dropdownButton.appendChild(selectedText);
    // const arrowSvg = document.createElementNS(
    //   "http://www.w3.org/2000/svg",
    //   "svg"
    // );
    // arrowSvg.setAttribute("class", "w-4 h-4 ml-2 fill-current");
    // arrowSvg.setAttribute("viewBox", "0 0 20 20");
    // arrowSvg.innerHTML =
    //   '<path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"/>';
    // dropdownButton.appendChild(arrowSvg);
    // const dropdownMenu = document.createElement("div");
    // dropdownMenu.className =
    //   "shape-dropdown-portal text-slate-950 hidden bg-white border border-gray-300 rounded shadow w-40";
    // const ul = document.createElement("ul");
    // const shapes = ["Rectangle", "Oval"];
    // const options: HTMLButtonElement[] = [];
    // shapes.forEach(shape => {
    //   const li = document.createElement("li");
    //   const btn = document.createElement("button");
    //   btn.setAttribute("data-value", shape);
    //   btn.className =
    //     "dropdown-option block w-full text-left px-4 py-2 hover:bg-gray-100";
    //   btn.textContent = shape;
    //   li.appendChild(btn);
    //   ul.appendChild(li);
    //   options.push(btn);
    // });
    // dropdownMenu.appendChild(ul);
    // dropdownContainer.appendChild(dropdownButton);

    // let dropdownOpen = false;
    // function closeDropdown() {
    //   dropdownMenu.classList.add("hidden");
    //   dropdownOpen = false;
    //   document.removeEventListener("mousedown", outsideClickListener);
    //   window.removeEventListener("resize", closeDropdown);
    //   window.removeEventListener("scroll", closeDropdown, true);
    // }
    // function positionDropdownAboveButton() {
    //   const rect = dropdownButton.getBoundingClientRect();
    //   dropdownMenu.style.left = `${rect.left + window.scrollX}px`;
    //   dropdownMenu.style.top = `${rect.top + window.scrollY - dropdownMenu.offsetHeight}px`;
    // }
    // function outsideClickListener(e: MouseEvent) {
    //   if (
    //     !dropdownButton.contains(e.target as Node) &&
    //     !dropdownMenu.contains(e.target as Node)
    //   ) {
    //     closeDropdown();
    //   }
    // }
    // dropdownButton.addEventListener("click", () => {
    //   if (!dropdownOpen) {
    //     const root = dropdownButton.getRootNode();
    //     if (root instanceof ShadowRoot) {
    //       root.appendChild(dropdownMenu);
    //     } else {
    //       document.body.appendChild(dropdownMenu);
    //     }
    //     dropdownMenu.classList.remove("hidden");
    //     setTimeout(() => {
    //       positionDropdownAboveButton();
    //     }, 0);
    //     dropdownOpen = true;
    //     document.addEventListener("mousedown", outsideClickListener);
    //     window.addEventListener("resize", closeDropdown);
    //     window.addEventListener("scroll", closeDropdown, true);
    //   } else {
    //     closeDropdown();
    //   }
    // });
    // options.forEach(option => {
    //   option.addEventListener("mousedown", e => {
    //     e.stopPropagation(); // Prevent outsideClickListener from firing
    //   });
    //   option.addEventListener("click", () => {
    //     const selectedValue: shapeType =
    //       (option.getAttribute("data-value") as shapeType) || "Rectangle";
    //     console.log("Dropdown clicked:", selectedValue);
    //     if (selectedText.textContent !== selectedValue) {
    //       shapeStore.set(selectedValue); // Update store
    //     }
    //     selectedText.textContent = selectedValue;
    //     closeDropdown();
    //     this.onShapeChange?.(selectedValue || "Rectangle");
    //   });
    // });

    // this.toolbar.appendChild(dropdownContainer);

    this.annotateButton = this.createButton(
      icons.annotate,
      "Annotate",
      "annotate-button"
    );
    this.toolbar.appendChild(this.annotateButton);

    this.addAnnotationButton = this.createButton(
      icons.addAnnotation,
      "add Annotation",
      "add-annotate-button"
    );
    this.toolbar.appendChild(this.addAnnotationButton);

    this.eraserButton = this.createButton(
      icons.eraser,
      "Eraser",
      "eraser-button"
    );
    this.toolbar.appendChild(this.eraserButton);

    this.addTextButton = this.createButton(
      icons?.addText ? icons?.addText : "",
      "Add text",
      "add-text-button"
    );
    this.addTextButton.id = "addTextBtn";
    this.toolbar.appendChild(this.addTextButton);
    // Attach handler directly if CropButtonHandler is available

    this.imageScaleButton = this.createButton(
      icons.imageScale,
      "Image Scale",
      "image-scale-button"
    );
    this.toolbar.appendChild(this.imageScaleButton);

    const rotateContainer = document.createElement("div");
    rotateContainer.className = "buttons-group-container";
    this.rotateContainer = rotateContainer;
    this.rotateLeftButton = this.createButton(
      icons.rotateLeft,
      "Rotate Left",
      "rotate-left-button"
    );
    rotateContainer.appendChild(this.rotateLeftButton);
    this.rotateRightButton = this.createButton(
      icons.rotateRight,
      "Rotate Right",
      "rotate-right-button"
    );
    rotateContainer.appendChild(this.rotateRightButton);
    this.toolbar.appendChild(rotateContainer);
    const undoRedoContainer = document.createElement("div");
    undoRedoContainer.className = "buttons-group-container";
    this.undoRedoContainer = undoRedoContainer;
    this.undoButton = this.createButton(
      icons.undo,
      "Undo",
      "undo-button bottom-bar-button"
    );
    undoRedoContainer.appendChild(this.undoButton);
    this.redoButton = this.createButton(icons.redo, "Redo", "redo-button");
    undoRedoContainer.appendChild(this.redoButton);
    this.toolbar.appendChild(undoRedoContainer);
    const zoomContainer = document.createElement("div");
    zoomContainer.className = "buttons-group-container";
    this.zoomContainer = zoomContainer;
    this.zoomOutButton = this.createButton(
      icons.zoomOut,
      "Zoom Out",
      "zoom-out-button"
    );
    zoomContainer.appendChild(this.zoomOutButton);
    this.zoomLabel = document.createElement("span");
    this.zoomLabel.className = "zoom-label";
    this.zoomLabel.textContent = "10%";
    zoomContainer.appendChild(this.zoomLabel);
    this.zoomInButton = this.createButton(
      icons.zoomIn,
      "Zoom In",
      "zoom-in-button"
    );
    zoomContainer.appendChild(this.zoomInButton);
    this.toolbar.appendChild(zoomContainer);

    this.reportbutton = document.createElement("div");
    this.reportbutton.innerHTML = `
      <div class="bottom-bar-button crop-button">
        <div class="flex items-center space-x-2">
          <span class="text-white font-medium text-sm select-none">Report</span>
          <label class="switch">
            <input id="myToggle" type="checkbox">
            <span class="slider"></span>
          </label>
    </div>
  </div>
`;

    this.toolbar.appendChild(this.reportbutton);
    this.resetButton = this.createButton(
      icons?.resetIcon ? icons?.resetIcon : "",
      "Reset",
      "resetBtn"
    );

    const toolbar = document.createElement("div");
    toolbar.className = "bottom-toolbar";
    toolbar.appendChild(this.toolbar);
    this.toolbar = toolbar;
    // Add toggle logic for toolbar visibility
    this.cropButton.addEventListener("click", () => {
      // this.showToolbarState('crop');
      setCropMode();
    });
    // this.annotateButton.addEventListener('click', () => {
    //     // this.showToolbarState('annotate');
    //     setAnnotateMode();
    // });
    // Default state
    // this.showToolbarState('home');
    setHomeMode();

    editorMode.subscribe((mode: EditorMode) => {
      this.showToolbarState(mode);
    });
  }

  private createButton(
    icon: string,
    alt: string,
    className: string
  ): HTMLButtonElement {
    const button = document.createElement("button");
    button.className = className + " bottom-bar-button";
    button.innerHTML = `<img src="${icon}" alt="${alt}" class="button-icon">`;
    return button;
  }

  public getToolbar(): HTMLDivElement {
    return this.toolbar;
  }

  // Add toggle logic for toolbar visibility
  private showToolbarState(mode: "crop" | "annotate" | "home") {
    if (mode === "crop") {
      this.cropButton.style.display = "";
      // this.shapeDropdownContainer.style.display = "";
      this.zoomContainer.style.display = "";
      this.rotateContainer.style.display = "";
      this.undoRedoContainer.style.display = "none";
      this.annotateButton.style.display = "none";
      this.addTextButton.style.display = "none";
      this.eraserButton.style.display = "none";
      this.reportbutton.style.display = "none";
      this.imageScaleButton.style.display = "none";
      this.addAnnotationButton.style.display = "none";
    } else if (mode === "annotate") {
      this.annotateButton.style.display = "";
      this.rotateContainer.style.display = "";
      this.addTextButton.style.display = "";
      this.eraserButton.style.display = "";
      this.undoRedoContainer.style.display = "";
      this.redoButton.style.display = "";
      this.undoButton.style.display = "";
      this.addAnnotationButton.style.display = "";
      this.annotateButton.style.display = "none";
      this.cropButton.style.display = "none";
      // this.shapeDropdownContainer.style.display = "none";
      this.zoomContainer.style.display = "none";
      this.reportbutton.style.display = "none";
      this.imageScaleButton.style.display = "none";
    } else {
      // home
      this.cropButton.style.display = "";
      this.annotateButton.style.display = "";
      this.imageScaleButton.style.display = "";
      this.reportbutton.style.display = "";
      // this.shapeDropdownContainer.style.display = "none";
      this.zoomContainer.style.display = "none";
      this.rotateContainer.style.display = "none";
      this.undoRedoContainer.style.display = "none";
      this.addTextButton.style.display = "none";
      this.eraserButton.style.display = "none";
      this.addAnnotationButton.style.display = "none";
    }
  }
  public static getInstance(options: any): BottomToolbarView {
    if (!BottomToolbarView.instance) {
      BottomToolbarView.instance = new BottomToolbarView(options);
    }
    return BottomToolbarView.instance;
  }
}
