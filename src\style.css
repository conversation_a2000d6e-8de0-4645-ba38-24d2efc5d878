@import "tailwindcss";
@plugin "daisyui";

:host {
  overflow: visible;
}

.toggle-icon::after {
  content: "✖"; /* U+2716 */
  transition: all 0.3s ease;
  border-radius: 9999px;
  position: absolute;
  color: black;
  height: 2.5rem; /* 40px = h-10 */
  width: 2.5rem;
  background-color: #f9fafb; /* Tailwind gray-50 */
  top: 0.25rem; /* top-1 */
  left: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: rotate(-180deg);
}

input:checked + .toggle-icon::after {
  content: "✔"; /* U+2714 */ 
  transform: rotate(0deg);
}


/* Removed global :root, html, body styles. Library-specific resets and variables moved to .editor-fullscreen-overlay and .editor-global-container */
 .editor-global-container {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #ffffff;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  --toolbar-height: 25px;
  --bottom-toolbar-height: 35px;
  --editor-bg: #ffffff;
  --toolbar-bg: #ffffff;
  --button-hover-bg: rgba(0, 0, 0, 0.05);
  --border-color: #e5e7eb;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  min-height: 100%;
  min-width: 100%;
  position: relative;
}

/* If annotation modal needs to fill viewport, ensure it uses fixed and 100vw/100vh */
.annotation-modal-container {
  position: fixed;
  display: none;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  justify-content: center;
  align-items: center;
  z-index: 999999;
  background: rgba(0,0,0,0.20);
}

.annotation-modal-container.show {
  display: flex !important;
}

/* Add box-sizing for all elements inside overlay for consistency */
.editor-fullscreen-overlay *, .editor-global-container * {
  box-sizing: border-box;
}

.bottom-bar-button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 8px 12px;
  font-size: 0.9em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 35px;
}

.bottom-bar-button:hover {
  border-color: #646cff;
}

.bottom-bar-button .flex {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
} */

.annotation-open-modal-button {
  padding: 10px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

/* Ensure the modal background is fully opaque and positioned correctly */
.annotation-modal-container {
  position: fixed;
  display: none;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  justify-content: center;
  align-items: center;
  z-index: 999999; /* Ensure annotation modal is above all UI layers */
}
.annotation-modal-container.show {
  display: flex !important;
}


.annotation-modal {
  background: #f8f9fa;
  color: #222;
  padding: 22px 18px 18px 18px;
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
  width: 280px;
  z-index: 2001;
  margin-bottom: 20px;
  border: 1.5px solid #e0e0e0;
  font-family: 'Segoe UI', Arial, sans-serif;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.annotation-modal h4, .annotation-modal h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 6px 0;
  color: #222;
}

.annotation-modal .brush-container,
.annotation-modal .ColorPicker,
.annotation-modal .opacity-container {
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.annotation-modal .brush-container {
  gap: 8px;
}

.annotation-modal #brushRange,
.annotation-modal #sliderPicker,
.annotation-modal #opacityRange {
  width: 92%;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 6px;
  box-sizing: border-box;
}

.annotation-modal #brushPreview {
  width: 92%;
  height: 48px;
  margin-left: auto;
  margin-right: auto;
  margin-top: 2px;
  margin-bottom: 8px;
  display: block;
}

.annotation-modal .ColorPicker {
  gap: 4px;
}

.annotation-modal #sliderPicker {
  width: 100%;
  margin-top: 2px;
  margin-bottom: 2px;
}

.annotation-modal .opacity-container {
  gap: 4px;
}

.annotation-modal #opacityRange {
  width: 100%;
  margin-top: 2px;
  margin-bottom: 2px;
}

.annotation-close-button {
  background: #e74c3c;
  color: #fff;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 1.1rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10);
  transition: background 0.18s;
}
.annotation-close-button:hover {
  background: #c0392b;
}

#brushPreview {
  width: 100%;
  height: 64px;
  margin-top: 10px;
}

.brush-path {
  fill: none;
  stroke: #4caf50;
  stroke-width: 5;
  stroke-linecap: round;
}

#opacityRange {
  -webkit-appearance: none;
  appearance: none;
  width: 240px;
  height: 25px;
  background: linear-gradient(to right, rgba(255, 0, 0, 0) 0%, rgba(255, 0, 0, 1) 100%);
  padding-left: 5px;
  padding-right: 5px;
  border-radius: 20px;
  outline: none;
  cursor: pointer;
  border: 0.6px solid grey;
}

#opacityRange::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: transparent;
  border: 3px solid white;
  box-shadow: 0 0 0 2px black;
  cursor: pointer;
}

#opacityRange::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: blue;
  border: 1px solid white;
  box-shadow: 0 0 0 1px black;
  cursor: pointer;
}

#brushRange {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 20px;
  background: linear-gradient(to right, #4caf50 25%, #ddd 25%);
  outline: none;
  border-radius: 10px;
  transition: background 0.2s;
}

#brushRange::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  border: 0.5px solid black;
  background: #4caf50;
  cursor: pointer;
  transition: background-color 0.3s;
}

#brushRange::-moz-range-thumb {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background: #4caf50;
  cursor: pointer;
  transition: background-color 0.3s;
}

.editor-bottom-toolbar {
  height: var(--bottom-toolbar-height);
  flex-shrink: 0;
  background-color: var(--toolbar-bg);
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center; /* Center content on desktop */
  padding: 2px 10px;
  gap: 8px;
  border-top: 1px solid var(--border-color);
  width: 100%;
  box-sizing: border-box;
  margin-top: 2px;
  overflow-x: auto;
  overflow-y: hidden;
}

.bottom-toolbar,
.bottom-toolbar-container {
  height: var(--bottom-toolbar-height);
  min-height: auto;
  display: flex;
  align-items: center;
  justify-content: center; /* Center content on desktop */
  padding: 2px 10px;
  gap: 8px;
  background: var(--toolbar-bg);
  flex-shrink: 0;
  width: 100%;
  box-sizing: border-box;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
}

.crop-button {
    border-radius: 8px;
    background: black;
    margin: 0px;
}

.button-icon {
  width: 16px;
  height: 16px;
  display: block;
  filter: invert(100%) brightness(100%);
}

.editor-global-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  background-color: white;
  position: relative;
  gap: 2px;
}

.editor-top-toolbar {
  height: var(--toolbar-height);
  flex-shrink: 0;
  background-color: var(--toolbar-bg);
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  margin-bottom: 2px; /* Add margin at bottom */
}

.canBack {
  background-color: red;
}

.editor-container {
  flex: 1;
  display: flex;
  justify-content: center;
  overflow: hidden;
  background-color: var(--editor-bg);
  position: relative;
  min-height: 0;
  width: 100%;
  box-sizing: border-box;
  margin: 2px 0;
}

.image-container {
  /* width: 100%;
  height: 100%; */
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}


.cropper-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: var(--editor-bg);
  position: relative;
}

/* .marching-ants-wrapper {
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 1000;
} */

.marching-ants-border {
  width: 100%;
  height: 100%;
}

.marching-ants-border rect {
  fill:rgba(0, 0, 0, 0.05);
  stroke: yellow;
  stroke-width: 4;
  stroke-dasharray: 10 5;
  animation: dashmove 1s linear infinite;
}

@keyframes dashmove {
  to {
    stroke-dashoffset: -15;
  }
}

.cropper-container img {
  max-height: 100%; 
}

cropper-canvas {
  width: 100%;
  height: 100%;
}

/* cropper-shade {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

cropper-selection {
  position: absolute;
  border: 2px dashed #fff;
  box-sizing: border-box;
} */

cropper-image {
  width: 100%;
  height: 100%;
}

.buttons-group-container {
    display: flex;
    align-items: center;
    color: #333;
    justify-content: space-between;
    background-color: #000;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

/* Styling for the rotate buttons */
.rotate-left-button, .rotate-right-button {
    cursor: pointer;
}

/* Styling for the shape dropdown in the bottom toolbar */
.image-viewer-container {
  position: fixed;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 10px;
  padding: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
  cursor: move;
  width: fit-content;
  max-width: 90vw;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  touch-action: none;
  user-select: none;
  left: 0;
  top: 0;
  transform: none;
}

.image-viewer-container.dragging {
  transition: none;
}

.image-viewer-container:not(.dragging) {
  transition: transform 0.3s ease;
}

.image-viewer-container::-webkit-scrollbar {
  width: 0;
  height: 0;
  display: none;
}

.image-viewer-container:hover {
  scrollbar-width: thin;
  -ms-overflow-style: auto;
}

.image-viewer-container:hover::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  display: block;
}

.image-viewer-container::-webkit-scrollbar-track {
  background: #222;
  border-radius: 10px;
}

.image-viewer-container::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 10px;
}

.image-viewer-container::-webkit-scrollbar-thumb:hover {
  background-color: #ddd;
  border-radius: 10px;
}

.image-scroller {
  display: flex;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-snap-type: x mandatory;
  width: 100%;
  max-width: 90vw;
  margin-bottom: 10px;
  position: relative;
  touch-action: pan-x;
  gap: 8px;
  padding: 4px;
}

.image-scroller img {
  margin-right: 5px;
  border-radius: 5px;
  height: 120px;
  width: auto;
  object-fit: contain;
  scroll-snap-align: start;
  transition: border-color 0.3s ease, transform 0.3s ease;
  position: relative;
  cursor: pointer;
  touch-action: manipulation;
}

@media (max-width: 768px) {
  .image-scroller img {
    height: 100px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .image-scroller img {
    height: 140px;
  }
}

@media (min-width: 1025px) {
  .image-scroller img {
    height: 160px;
  }
}

.image-scroller img:last-child {
  margin-right: 0;
}

.image-scroller img.selected {
  border: 3px solid #007bff;
  transform: scale(0.95);
  z-index: 11;
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
}

.image-scroller img.selected::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 5px;
  pointer-events: none;
}

.close-button {
  background-color: rgba(255, 0, 0, 0.7);
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.3s ease;
  align-self: flex-end;
}

.close-button:hover {
  background-color: rgba(255, 0, 0, 1);
}

.image-viewer-container h2 {
  color: #eee;
  font-size: 1em;
  margin-bottom: 0.5em;
  white-space: nowrap;
}

.drag-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  color: #fff;
  opacity: 0.5;
  pointer-events: none;
  user-select: none;
  cursor: move;
  z-index: 12;
}

.cropped-image-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.cropped-image-preview {
  max-width: 100%;
  max-height: 300px;
  margin-bottom: 20px;
}

.modal-buttons-container {
  display: flex;
  gap: 10px;
}

.zoom-in-button, .zoom-out-button {
    cursor: pointer;
}

/* Styling for the rotate buttons */
.rotate-left-button, .rotate-right-button {
    cursor: pointer;
}

/* Styling for the shape dropdown in the bottom toolbar */
@media (max-width: 768px) {
  .bottom-toolbar,
  .bottom-toolbar-container,
  .editor-bottom-toolbar {
    height: var(--bottom-toolbar-height);
    /* justify-content: flex-start; */
    padding: 2px 5px;
    gap: 5px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .button-icon {
    width: 18px !important;
    height: 18px !important;
    min-width: 18px !important;
    min-height: 18px !important;
    max-width: 22px !important;
    max-height: 22px !important;
    display: block;
    filter: invert(100%) brightness(100%);
  }
}

.bottom-toolbar-container, .bottom-toolbar {
  overflow-x: auto; /* or hidden as needed */
  /* other styles */
}

.shape-dropdown-portal {
  position: absolute;
  z-index: 9999;
  /* Add your dropdown styling */
}

.hidden-toolbar-item {
  display: none !important;
}

/* The switch - the box around the slider */
.switch {
  font-size: 14px;
  position: relative;
  display: inline-block;
  width: 2.5em;
  height: 1.4em;
  margin-left: 4px;
}

/* Hide default HTML checkbox */
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

/* The slider */
.slider {
  position: absolute;
  cursor: pointer;
  inset: 0;
  background: #4a4a4a;
  border-radius: 50px;
  transition: all 0.3s ease;
}

.slider:before {
  position: absolute;
  content: "";
  display: flex;
  align-items: center;
  justify-content: center;
  height: 1.2em;
  width: 1.2em;
  left: 0.1em;
  bottom: 0.1em;
  background-color: white;
  border-radius: 50px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  transition: all 0.3s ease;
}

.switch input:checked + .slider {
  background: #0974f1;
}

.switch input:focus + .slider {
  box-shadow: 0 0 1px #0974f1;
}

.switch input:checked + .slider:before {
  transform: translateX(1.1em);
}